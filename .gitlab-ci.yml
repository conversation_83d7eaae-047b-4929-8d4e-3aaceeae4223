stages:
    - versioning
    - auditing
    # - test
    - compiling
    - publishing

versioning:
    image: node:20-alpine
    stage: versioning
    only:
        - tags
        - merge_requests
    when: always
    before_script:
        - npm -v
    script:
        - npm version $CI_COMMIT_TAG --commit-hooks=false --git-tag-version=false
        - mkdir source
        - cp -f package.json source
    artifacts:
        paths:
            - source
        expire_in: 10 minutes

auditing:
    image: node:20-alpine
    stage: auditing
    only:
        - tags
        - merge_requests
    when: on_success
    before_script:
        - ls
        - cp -f source/package.json .
        - npm i --package-lock-only
    script:
        - npm audit --fix --production

# test:
#     image: docker:dind
#     stage: test
#     only:
#         - tags
#         - merge_requests
#     when: on_success
#     script:
#         - docker-compose -f docker-compose.test.yml down && docker-compose -f docker-compose.test.yml up --abort-on-container-exit --no-log-prefix

compiling:
    image: node:20-alpine
    stage: compiling
    only:
        - tags
    when: on_success
    before_script:
        - cp -f source/package.json .
        - npm install
    script:
        - npm run package
    artifacts:
        paths:
            - ./module
        expire_in: 10 minutes

publishing:
    image: node:20-alpine
    stage: publishing
    only:
        - tags
    when: on_success
    before_script:
        - cd ./module
        - echo "//registry.npmjs.org/:_authToken=\${NPM_TOKEN}" > .npmrc
        - cat .npmrc
        - >
            rc="-";
            case "$CI_COMMIT_TAG" in *$rc* ) echo "npm publish --access restricted --tag alpha" > command ;; *) echo "npm publish --access restricted" > command ;; esac
    script:
        - sh command
