import { INTERNAL_API_KEY } from '@configs';
import {
    APIError,
    type AuthResolver,
    type JWTToken,
    type Nullable,
    type SecureContext,
    type SecureProperty,
} from '@heronjs/common';
import axios, { type AxiosResponse } from 'axios';
import fs from 'fs';
import { StatusCodes } from 'http-status-codes';
import JWT from 'jsonwebtoken';
import { type Observable, of } from 'rxjs';

const AUTH_API_URL = process.env.AUTH_API_URL || 'https://api.conative.ai/auth-svc';
const { PUBLIC_KEY_PATH } = process.env;
export const AuthenticationConfig = Object.freeze({
    PUBLIC_KEY_PATH: PUBLIC_KEY_PATH || '/tmp/certs',
});
export const SystemConfig = Object.freeze({
    ACCESS_TOKEN_PUBLIC_KEY_FILE_PATH: 'certs/access_token_public_key.pem',
});

export class JwtUtil {
    public static verifyToken(token: string, publicKey: string) {
        const result = JWT.verify(token, publicKey);

        return result;
    }

    public static decode(token: string) {
        const result = JWT.decode(token);

        return result;
    }
}

export interface PublicKeysDTO {
    accessTokenPublicKey: string;
    refreshTokenPublicKey: string;
}

export class UnauthorizedError extends APIError {
    constructor() {
        super(StatusCodes.UNAUTHORIZED, 'Unauthorized');
    }
}

export interface SampleSecurityType {
    sub?: string;
    organization?: string;
    roles?: string[];
    permissions?: string[];
    accessType?: number;
    organizations?: string[];
    private?: boolean;
    isInternal?: boolean;
}

const verifySystemJWT = async (token: string) => {
    const accessTokenPublicKey = fs.readFileSync(SystemConfig.ACCESS_TOKEN_PUBLIC_KEY_FILE_PATH, 'utf8');

    // verify system token
    const verify = JwtUtil.verifyToken(token, accessTokenPublicKey);

    return verify as JWTToken;
};

const storePublicKeysToLocal = async (organization: string, publicKeys: PublicKeysDTO) => {
    const dir = `${AuthenticationConfig.PUBLIC_KEY_PATH}/${organization}`;
    const path = `${dir}/certs.pub`;
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(path, JSON.stringify(publicKeys), { encoding: 'utf8' });
};

const pullPublicKeys = async (organization: string) => {
    const filePathSecretStored = `${AuthenticationConfig.PUBLIC_KEY_PATH}/${organization}/certs.pub`;
    let secretKeys: PublicKeysDTO;

    try {
        const contentFile = fs.readFileSync(filePathSecretStored, 'utf8');
        secretKeys = JSON.parse(contentFile);
    } catch (error: any) {
        if (error.code === 'ENOENT') {
            const url = `${AUTH_API_URL}/internal/auth/organizations/${organization}/certs`;
            const response: AxiosResponse<any> = await axios.get(url);
            secretKeys = response.data.data;
            await storePublicKeysToLocal(organization, secretKeys);
        } else {
            throw new UnauthorizedError();
        }
    }

    return secretKeys;
};

const verifyOrganizationJWT = async (token: string) => {
    const decodeToken = JwtUtil.decode(token) as JWTToken;
    const organization = decodeToken.organization;

    if (!organization) {
        throw new UnauthorizedError();
    }

    const secretKeys = await pullPublicKeys(organization);

    const { accessTokenPublicKey } = secretKeys;

    const verify = JwtUtil.verifyToken(token, accessTokenPublicKey);

    return verify as JWTToken;
};

export const verifyJWT = async (authorization?: string) => {
    if (!authorization) {
        throw new UnauthorizedError();
    }
    const token = authorization.substring(authorization.indexOf('Bearer ') + 'Bearer '.length);

    if (!token) {
        throw new UnauthorizedError();
    }

    const decodeToken: Nullable<JWTToken> = JwtUtil.decode(token) as Nullable<JWTToken>;
    if (!decodeToken) {
        throw new UnauthorizedError();
    }

    const { is_sys_token } = decodeToken;

    let data;

    try {
        if (is_sys_token) {
            data = await verifySystemJWT(token);
        } else {
            data = await verifyOrganizationJWT(token);
        }
    } catch (err) {
        throw new UnauthorizedError();
    }

    return data;
};

export class AuthContext implements SecureContext<SampleSecurityType, SecureProperty> {
    OnGuard(data: SampleSecurityType): Observable<any> {
        if (data === undefined) return of({});

        return of(data);
    }

    static Resolver: AuthResolver<SampleSecurityType> = {
        http: ['header', 'authorization'],
        ws: ['handshake', 'token'],

        resolve: async (data?: string): Promise<SampleSecurityType> => {
            const internalAuthorizationParts: string[] = data?.split('|') ?? [];
            if (internalAuthorizationParts[0] === INTERNAL_API_KEY) {
                return {
                    isInternal: true,
                    organization: internalAuthorizationParts[1],
                    sub: internalAuthorizationParts[2],
                };
            }

            const payload = await verifyJWT(data);
            const { sub, organization, organizations, roles, permissions, is_sys_token } = payload;

            return is_sys_token
                ? { sub, organizations, private: true }
                : { sub, organization, roles, permissions, private: true };
        },
    };
}
