import { Knex } from 'knex';

export const buildBKeyOnOrderMapQuery = (client: Knex): Knex.QueryBuilder => {
    const query = client.select('*').from(
        client.raw(`(
select
  json_object_agg(b_key, on_order) as "b_key_on_order_map"
from
  (
    select
      "ON_ORDER_TABLE"."h_key",
      sum("ON_ORDER_TABLE"."on_order") as on_order
    from
      (
        select
          "BV_H_items"."h_key",
          "BV_H_PO_line_items"."from_source",
          CASE
            WHEN SUM("BV_S_PO_line_item_data"."ordered_qty") - SUM("BV_S_PO_line_item_data"."received_qty") > 0 THEN SUM("BV_S_PO_line_item_data"."ordered_qty") - SUM("BV_S_PO_line_item_data"."received_qty")
            ELSE 0
          END as on_order
        from
          "BV_H_items"
          left join "BV_L_po_line_item_vendor" on "BV_L_po_line_item_vendor"."h_item_key" = "BV_H_items"."h_key"
          left join "BV_H_PO_line_items" on "BV_H_PO_line_items"."h_key" = "BV_L_po_line_item_vendor"."h_po_line_item_key"
          left join "BV_S_PO_line_item_data" on "BV_S_PO_line_item_data"."h_key" = "BV_H_PO_line_items"."h_key"
          left join "BV_S_purchase_order_data" on "BV_S_purchase_order_data"."h_key" = "BV_L_po_line_item_vendor"."h_po_key"
        where
          "BV_S_purchase_order_data"."status" = 'pending'
          and "BV_H_items"."from_source" != 'amazon'
        group by
          "BV_H_items"."h_key",
          "BV_H_PO_line_items"."from_source",
          "BV_H_items"."sku",
          "BV_S_purchase_order_data"."po_number"
      ) as "ON_ORDER_TABLE"
    where
      "ON_ORDER_TABLE"."on_order" > 0
    group by
      "ON_ORDER_TABLE"."h_key"
    union all
    select
      "ON_ORDER_TABLE"."h_key",
      sum("ON_ORDER_TABLE"."on_order") as on_order
    from
      (
        select
          "BV_H_items"."parent_id" as "h_key",
          "BV_H_PO_line_items"."from_source",
          CASE
            WHEN SUM("BV_S_PO_line_item_data"."ordered_qty") - SUM("BV_S_PO_line_item_data"."received_qty") > 0 THEN SUM("BV_S_PO_line_item_data"."ordered_qty") - SUM("BV_S_PO_line_item_data"."received_qty")
            ELSE 0
          END as on_order
        from
          "BV_H_items"
          left join "BV_L_po_line_item_vendor" on "BV_L_po_line_item_vendor"."h_item_key" = "BV_H_items"."h_key"
          left join "BV_H_PO_line_items" on "BV_H_PO_line_items"."h_key" = "BV_L_po_line_item_vendor"."h_po_line_item_key"
          left join "BV_S_PO_line_item_data" on "BV_S_PO_line_item_data"."h_key" = "BV_H_PO_line_items"."h_key"
          left join "BV_S_purchase_order_data" on "BV_S_purchase_order_data"."h_key" = "BV_L_po_line_item_vendor"."h_po_key"
        where
          "BV_S_purchase_order_data"."status" = 'pending'
          and "BV_H_items"."from_source" != 'amazon'
        group by
          "BV_H_items"."parent_id",
          "BV_H_PO_line_items"."from_source",
          "BV_H_items"."sku",
          "BV_S_purchase_order_data"."po_number"
      ) as "ON_ORDER_TABLE"
    where
      "ON_ORDER_TABLE"."on_order" > 0
    group by
      "ON_ORDER_TABLE"."h_key"
    UNION ALL
    SELECT
      "BV_H_items"."h_key",
      COALESCE(SUM("BV_S_item_po"."on_order"), 0) AS "on_order"
    FROM
      "BV_H_items"
      JOIN "BV_S_item_po" ON "BV_S_item_po"."h_key" = "BV_H_items"."h_key"
    WHERE
      "BV_H_items"."from_source" = 'amazon'
      AND "BV_S_item_po"."on_order" != 0
    GROUP BY
      "BV_H_items"."h_key"
    UNION ALL
    SELECT
      "BV_H_items"."parent_id" as "h_key",
      COALESCE(SUM("BV_S_item_po"."on_order"), 0) AS "on_order"
    FROM
      "BV_H_items"
      JOIN "BV_S_item_po" ON "BV_S_item_po"."h_key" = "BV_H_items"."h_key"
    WHERE
      "BV_H_items"."from_source" = 'amazon'
      AND "BV_S_item_po"."on_order" != 0
    GROUP BY
      "BV_H_items"."parent_id"
  ) as "ON_ORDER_BY_H_KEY_QUERY"
  join "BV_H_items" on "BV_H_items"."h_key" = "ON_ORDER_BY_H_KEY_QUERY"."h_key"
  ) as "SUBQUERY"`),
    );
    return query;
};
