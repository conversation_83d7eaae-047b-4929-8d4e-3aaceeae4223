import { Knex } from 'knex';

export const buildBKeyHKeyMapQuery = (
    client: Knex,
    args: {
        bKeys?: string[];
    },
): Knex.QueryBuilder => {
    const { bKeys } = args;

    const query = client
        .select(
            client.raw(`
                JSON_OBJECT_AGG("BV_H_items".b_key, "BV_H_items".h_key) AS b_key_h_key_map
            `),
        )
        .from('BV_H_items')
        .whereNotNull('b_key')
        .whereNotNull('h_key');

    // Add filter for specific b_keys if provided
    if (bKeys && bKeys.length > 0) {
        query.whereIn('b_key', bKeys);
    }

    return query;
};
