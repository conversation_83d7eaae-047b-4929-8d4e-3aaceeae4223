import { Knex } from 'knex';
import { ItemTypes } from '../../providers';

export const buildProductGradeQuery = (
    client: Knex,
    args: {
        fromDate?: string;
        toDate?: string;
        timezone?: string;
    },
    type: ItemTypes,
): Knex.QueryBuilder => {
    // Example query
    const { fromDate, toDate } = args;
    const timezone = args.timezone ?? 'America/Los_Angeles';

    const key = type === ItemTypes.Variant ? 'h_key' : 'parent_id';

    const subQuery = client('BV_H_items')
        .select(
            `BV_H_items.${key} as h_key`,
            client.raw(
                `
                    COALESCE(
                        SUM(SUM("BV_V_sales".gross_sales)) OVER (
                        ORDER BY COALESCE(SUM("BV_V_sales".gross_sales), 0) DESC, "BV_H_items"."${key}" ASC
                        ),
                        0
                    ) / NULLIF((
                        SELECT SUM("BV_V_sales".gross_sales)
                        FROM "BV_V_sales"
                        WHERE "BV_V_sales".warehouse_id IS NULL
                        AND "BV_V_sales".source_date at time zone ? BETWEEN ? AND ?
                    ), 0) * 100 AS cumulative_gross_sales_percentage
                `,
                [timezone, fromDate, toDate],
            ),
        )
        .innerJoin('BV_V_sales', 'BV_V_sales.h_key', 'BV_H_items.h_key')
        .whereNull('BV_V_sales.warehouse_id')
        .groupBy(`BV_H_items.${key}`)
        .as('SUB_QUERY');

    if (fromDate && toDate) {
        subQuery.whereRaw('"BV_V_sales"."source_date" at time zone ? between ? and ?', [
            timezone,
            fromDate,
            toDate,
        ]);
    }

    if (type === ItemTypes.Variant) {
        subQuery.andWhere('BV_H_items.type', 'simple');
    }

    const mainQuery = client
        .select(
            client.raw(`
                JSON_OBJECT_AGG("SUB_QUERY".h_key,
                    CASE
                    WHEN "SUB_QUERY".cumulative_gross_sales_percentage < 80 THEN 1
                    WHEN "SUB_QUERY".cumulative_gross_sales_percentage >= 80 AND "SUB_QUERY".cumulative_gross_sales_percentage < 95 THEN 2
                    ELSE 3
                    END
                ) AS revenue_grade_map
            `),
        )
        .from(subQuery)
        .where('SUB_QUERY.cumulative_gross_sales_percentage', '<', 95);

    return mainQuery;
};
