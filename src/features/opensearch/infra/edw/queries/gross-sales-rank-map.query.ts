import { K<PERSON> } from 'knex';
import { ItemTypes } from '../../providers';
import { DaoUtils, FilterInput } from '@cbidigital/aqua-ddd';
import { ItemQueryConfig } from '@features/opensearch/infra/edw/query-configs';

export const buildGrossSalesRankMapQuery = (
    client: Knex,
    args: {
        fromDate?: string;
        toDate?: string;
        filter?: FilterInput;
    },
    type: ItemTypes,
): Knex.QueryBuilder => {
    // Example query
    const { fromDate, toDate } = args;
    const filter: FilterInput = { ...args.filter };

    const key = type === ItemTypes.Variant ? 'BV_H_items.h_key' : 'BV_H_items.parent_id';

    const subQuery = client('BV_H_items')
        .select(
            `${key} as id`,
            client.raw(
                `row_number() over (order by cast(sum(gross_sales) as integer) desc) as gross_sales_rank`,
            ),
        )
        .from('BV_H_items')
        .innerJoin('BV_V_sales', 'BV_V_sales.h_key', `BV_H_items.h_key`)
        .whereNull('BV_V_sales.warehouse_id')
        .groupBy(key);

    if (type === ItemTypes.Product) {
        subQuery.whereRaw(`"BV_H_items"."h_key" <> "BV_H_items"."parent_id"`);
    }
    if (fromDate && toDate) filter.sales_source_date = { $gte: fromDate, $lte: toDate };
    DaoUtils.applyFilter(filter, ItemQueryConfig, subQuery);

    const mainQuery = client
        .select(client.raw(`json_object_agg(id, gross_sales_rank) as gross_sales_rank_map`))
        .from(subQuery.as('t'));

    return mainQuery;
};
