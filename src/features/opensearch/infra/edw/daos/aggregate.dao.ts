import { ItemTypes } from '../../providers';
import { BaseDao, FilterInput, IBaseDao, IDatabase } from '@cbidigital/aqua-ddd';
import { Dao, DataSource, Lifecycle, Logger } from '@heronjs/common';
import { OPENSEARCH_INJECT_TOKENS } from '../../../../../constants';
import {
    buildBKeyHKeyMapQuery,
    buildProductGradeQuery,
    buildBKeyOnOrderMapQuery,
    buildGrossSalesRankMapQuery,
} from '../queries';

export type IAggregateEDWDao = IBaseDao<unknown, unknown> & {
    getProductGrade: (args: {
        args: {
            fromDate?: string;
            toDate?: string;
            timezone?: string;
        };
        options: { tenantId: string };
        type: ItemTypes;
    }) => Promise<Record<string, string>>;
    getBKeyHKeyMap: (args: {
        options: { tenantId: string };
        bKeys?: string[];
    }) => Promise<Record<string, string>>;
    getBKeyOnOrderMap: (args: { options: { tenantId: string } }) => Promise<Record<string, number>>;
    getGrossSalesRankMap: (args: {
        args: {
            fromDate?: string;
            toDate?: string;
            hKeys?: string[];
            filter?: FilterInput;
        };
        options: { tenantId: string };
        type: ItemTypes;
    }) => Promise<Record<string, number>>;
};

@Dao({ token: OPENSEARCH_INJECT_TOKENS.EDW_DAO.AGGREGATE, scope: Lifecycle.Singleton })
export class AggregateEDWDao extends BaseDao<unknown, unknown> implements IAggregateEDWDao {
    private readonly logger = new Logger(this.constructor.name);

    constructor(@DataSource() db: IDatabase) {
        super({
            db,
            tableName: 'BV_H_items',
            recordMapper: {} as any,
        });
    }

    async getProductGrade({
        args,
        options,
        type,
    }: {
        args: {
            fromDate?: string;
            toDate?: string;
            timezone?: string;
        };
        options: {
            tenantId: string;
        };
        type: ItemTypes;
    }): Promise<Record<string, string>> {
        const client = this.db.getClient(options.tenantId);
        args.fromDate = `${args.fromDate}T00:00:00`;
        args.toDate = `${args.toDate}T23:59:59`;
        const response = await buildProductGradeQuery(client, args, type);
        return response[0].revenue_grade_map ?? {};
    }

    async getBKeyHKeyMap({
        options,
        bKeys,
    }: {
        options: { tenantId: string };
        bKeys?: string[];
    }): Promise<Record<string, string>> {
        try {
            const client = this.db.getClient(options.tenantId);
            const response = await buildBKeyHKeyMapQuery(client, { bKeys });

            // The query returns a JSON object directly from PostgreSQL
            return response[0].b_key_h_key_map || {};
        } catch (error) {
            this.logger.error('Error getting b_key to h_key map', error);
            throw error;
        }
    }

    async getBKeyOnOrderMap({ options }: { options: { tenantId: string } }): Promise<Record<string, number>> {
        const client = this.db.getClient(options.tenantId);
        const response = await buildBKeyOnOrderMapQuery(client);
        return response[0].b_key_on_order_map || {};
    }

    async getGrossSalesRankMap(args: {
        args: {
            fromDate?: string;
            toDate?: string;
            fromSource?: string;
        };
        options: { tenantId: string };
        type: ItemTypes;
    }): Promise<Record<string, number>> {
        const client = this.db.getClient(args.options.tenantId);
        const response = await buildGrossSalesRankMapQuery(client, args.args, args.type);
        const [grossSalesRankMap] = response;
        const result = grossSalesRankMap.gross_sales_rank_map || {};
        return result;
    }
}
