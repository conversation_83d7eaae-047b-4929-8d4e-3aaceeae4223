import {
    ItemHKeyFilter,
    ItemFromSourceFilter,
    SalesSourceDateFilter,
} from '@features/opensearch/infra/edw/filters';
import { QueryConfig } from '@cbidigital/aqua-ddd';

export const ItemQueryConfig: QueryConfig<'h_key' | 'from_source' | 'sales_source_date'> = {
    h_key: {
        sortable: true,
        filterable: true,
        filterClass: ItemHKeyFilter,
    },

    from_source: {
        sortable: true,
        filterable: true,
        filterClass: ItemFromSourceFilter,
    },

    sales_source_date: {
        sortable: true,
        filterable: true,
        filterClass: SalesSourceDateFilter,
    },
};
