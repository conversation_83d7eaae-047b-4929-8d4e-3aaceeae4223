import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { Lifecycle, Logger, Provider } from '@heronjs/common';

export interface IOPSProvider {
    getQueryCache(key: string): Promise<any>;
    setQueryCache(key: string, value: Promise<any>): boolean;
    removeQueryCache(key: string): boolean;
}

@Provider({ token: OPENSEARCH_INJECT_TOKENS.PROVIDER.OPS, scope: Lifecycle.Singleton })
export class OPSProvider implements IOPSProvider {
    private readonly queryCache: Record<string, Promise<any>>;
    private readonly logger = new Logger(this.constructor.name);

    constructor() {
        this.queryCache = {};
    }

    getQueryCache(key: string): Promise<any> {
        try {
            return this.queryCache[key];
        } catch (error) {
            this.logger.error('OPSProvider:getQueryCache', error);
            throw error;
        }
    }

    setQueryCache(key: string, value: Promise<any>): boolean {
        try {
            this.queryCache[key] = value;
            return true;
        } catch (error) {
            this.logger.error('OPSProvider:setQueryCache', error);
            return false;
        }
    }

    removeQueryCache(key: string): boolean {
        try {
            delete this.queryCache[key];
            return true;
        } catch (error) {
            this.logger.error('OPSProvider:removeQueryCache', error);
            return false;
        }
    }
}
