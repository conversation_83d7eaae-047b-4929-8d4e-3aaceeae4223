import { OPEN<PERSON>AR<PERSON>_INJECT_TOKENS } from '@constants';
import { Lifecycle, Provider } from '@heronjs/common';

export interface ITenantSettingsProvider {
    getTimezone(tenantId: string): Promise<string>;
}

@Provider({ token: OPENSEARCH_INJECT_TOKENS.PROVIDER.TENANT_SETTINGS, scope: Lifecycle.Singleton })
export class TenantSettingsProvider implements ITenantSettingsProvider {
    // This is a simple implementation. In a real-world scenario,
    // you might fetch this from a database or external service
    private readonly tenantTimezones: Record<string, string> = {
        // Default timezone
        default: 'America/Los_Angeles',

        // Custom tenant timezones
        '05cdc460a36647e5bd2cbfbbd044d8fe': 'America/New_York', // Mizmooz
        e6677618fdfe43828e6eacb88bf2518e: 'America/New_York', // AS98
        a2f428895cdf41d6b133465c9aa9eb9e: 'America/New_York', // Ipolita
        '1df99c9485b74ef3afbd67bc5c49f158': 'America/Chicago', // Homesick
        cd2aeda11835470f9d71b58efc293a0f: 'America/New_York', // HonestPaw
        f24d05e83f9a40acba3a0a02b56da0c4: 'America/New_York', // MagnoliaCoffee
        '3321acab906744189f4b0b4312a9085c': 'Europe/Warsaw', // Millanova
    };

    getTimezone(tenantId: string): Promise<string> {
        // Return the tenant's timezone or default to UTC if not found.
        // This is a simple implementation. In a real-world scenario,
        // you might fetch this from a database or external service
        return Promise.resolve(this.tenantTimezones[tenantId] || this.tenantTimezones['default'] || 'UTC');
    }
}
