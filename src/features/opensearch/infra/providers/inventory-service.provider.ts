import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { QueryInput } from '@cbidigital/aqua-ddd';
import { Inject, Lifecycle, Nullable, Provider } from '@heronjs/common';
import axios from 'axios';
import { IAggregateEDWDao } from '../edw/daos';
import { PurchaseOrderDTO } from '@features/opensearch/domain';

export type OnOrderItem = { variantId: string; onOrder: number };
export type DayOfLeadTimes = { variant_id: string; day_of_lead_time: number; day_of_stocks: number };
export type Replenishables = Record<string, 0>;
export type APIParams = {
    tenantId: string;
    type: ItemTypes;
    status?: ItemStatus;
    warehouseIds?: string[];
};
export type APIResponse = OnOrderItem[] | DayOfLeadTimes[] | Replenishables[];
export enum ItemTypes {
    Variant = 'variant',
    Product = 'product',
}
export enum ItemStatus {
    Pending = 'pending',
}
export type ForecastDateRange = {
    fromDate: string;
    toDate: string;
    h_keys: string[];
};

export enum OrderDirection {
    ASC = 'asc',
    DESC = 'desc',
}

export enum PurchaseOrderSortBy {
    VENDOR = 'vendor',
    STATUS = 'status',
    PO_NUMBER = 'po_number',
    WAREHOUSE = 'warehouse',
    CREATED_AT = 'created_at',
    EXPECTED_DATE = 'expected_date',
}

export enum PurchaseOrderStatusEnum {
    DRAFT = 'draft',
    PENDING = 'pending',
    CACELED = 'canceled',
    CLOSED = 'closed',
}

export type PurchaseOrder = {
    id: string;
    source: string;
    status: string;
    poNumber: string;
    vendorId: string;
    warehouseId: string;
    expectedDate: Date;
    vendorName?: string;
    warehouseName?: string;
    receivedStatus?: string;
    totalOrderQuantity?: number;
    totalReceivedQuantity?: number;
    note: Nullable<string>;
    archived: Nullable<boolean>;
    createdAt: Date;
    updatedAt: Nullable<Date>;
};

export type GetListPurchaseOrdersInput = QueryInput & {
    tenant: string;
};

export type GetPurchaseOrdersResponse = {
    purchaseOrders: Partial<PurchaseOrderDTO>[];
    totalCount: number;
};

export interface IInventoryServiceProvider {
    getOnOrders(params: {
        tenantId: string;
        type: ItemTypes;
        status?: ItemStatus;
        warehouseIds?: string[];
    }): Promise<OnOrderItem[]>;

    getDayOfLeadTime(params: { tenantId: string }): Promise<DayOfLeadTimes[]>;

    getReplenishable(params: { tenantId: string }): Promise<Replenishables>;

    getInventoryApiData(
        params: APIParams,
        queryRequiredFields: string[],
    ): Promise<{
        bKeyToOnOrder: Record<string, number>;
        bKeyToDayOfLeadTimes: Record<string, number>;
        bKeyToReplenishables: Record<string, 0>;
        bKeyToDayOfStock: Record<string, number>;
        forecastDateRanges: ForecastDateRange[];
    }>;

    getPurchaseOrders(input: GetListPurchaseOrdersInput): Promise<GetPurchaseOrdersResponse>;
}

@Provider({ token: OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE, scope: Lifecycle.Singleton })
export class InventoryServiceProvider implements IInventoryServiceProvider {
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.EDW_DAO.AGGREGATE)
        private readonly aggregateEDWDao: IAggregateEDWDao,
    ) {}

    async getOnOrders({
        tenantId,
        type,
        status,
        warehouseIds,
    }: {
        tenantId: string;
        type: ItemTypes;
        status?: ItemStatus;
        warehouseIds?: string[];
    }): Promise<OnOrderItem[]> {
        const hostUrl = process.env.INVENTORY_API_URL ?? 'localhost:3003';
        const urlGetOnOrderEachItemInApp = `${hostUrl}/internal/api/purchase-orders/items/on-order`;
        let onOrders: OnOrderItem[] = [];

        try {
            const bodyGetOnOrder = {
                tenant: tenantId,
                type: type ?? ItemTypes.Variant,
                status: status ?? ItemStatus.Pending,
                warehouseIds,
            };
            onOrders = (await axios.post(urlGetOnOrderEachItemInApp, bodyGetOnOrder)).data.data;
        } catch (err: any) {
            console.error('InventoryServiceProvider:getOnOrders', err);
        }

        return onOrders;
    }

    async getDayOfLeadTime({ tenantId }: { tenantId: string }): Promise<DayOfLeadTimes[]> {
        const hostUrl = process.env.INVENTORY_API_URL ?? 'localhost:3003';
        const urlGetOnOrderEachItemInApp = `${hostUrl}/inventory-planner/internal/day-of-lead-time`;
        let dayOfLeadTimes: DayOfLeadTimes[] = [];

        try {
            const bodyGetDayOfLeadTime = {
                tenant: tenantId,
            };
            dayOfLeadTimes = (await axios.post(urlGetOnOrderEachItemInApp, bodyGetDayOfLeadTime)).data.data;
        } catch (err: any) {
            console.error('InventoryServiceProvider:getDayOfLeadTime', err);
        }

        return dayOfLeadTimes;
    }

    async getReplenishable({ tenantId }: { tenantId: string }): Promise<Replenishables> {
        const hostUrl = process.env.INVENTORY_API_URL ?? 'localhost:3003';
        const urlGetOnOrderEachItemInApp = `${hostUrl}/internal/replenishable/map?organization=${tenantId}`;
        let replenishable: Replenishables = {};

        try {
            replenishable = (await axios.get(urlGetOnOrderEachItemInApp)).data.data;
        } catch (err: any) {
            console.error('InventoryServiceProvider:getReplenishable', err);
        }

        return replenishable;
    }

    async getInventoryApiData(params: APIParams, queryRequiredFields: string[]) {
        const { tenantId, type } = params;
        const listApiPromises: [
            Promise<APIResponse>,
            Promise<Record<string, number>>,
            Promise<DayOfLeadTimes[]>,
            Promise<Replenishables>,
        ] = [] as any;

        if (
            queryRequiredFields.includes('on_order') ||
            queryRequiredFields.includes('forecasted_sales_days_remaining_based_on_ats')
        ) {
            listApiPromises.push(
                this.getOnOrders({
                    tenantId,
                    type,
                }),
            );
            listApiPromises.push(
                this.aggregateEDWDao.getBKeyOnOrderMap({
                    options: { tenantId },
                }),
            );
        } else {
            listApiPromises.push(Promise.resolve([]), Promise.resolve({}));
        }

        queryRequiredFields.includes('lead_time') || queryRequiredFields.includes('days_of_stock')
            ? listApiPromises.push(
                  this.getDayOfLeadTime({
                      tenantId,
                  }),
              )
            : listApiPromises.push(Promise.resolve([]));

        queryRequiredFields.includes('replenishable')
            ? listApiPromises.push(
                  this.getReplenishable({
                      tenantId,
                  }),
              )
            : listApiPromises.push(Promise.resolve([]));

        const [onOrders, edwOnOrderMap, dayOfLeadTimes, replenishables]: [
            APIResponse,
            Record<string, number>,
            DayOfLeadTimes[],
            Replenishables,
        ] = await Promise.all(listApiPromises);

        let bKeyToOnOrder;
        if (Object.keys(onOrders).length) {
            bKeyToOnOrder = onOrders.reduce((previous: any, current: any) => {
                return { ...previous, [current.variantId ?? current.productId]: current.onOrder };
            }, {});
        }
        bKeyToOnOrder = { ...bKeyToOnOrder, ...(edwOnOrderMap ?? {}) };

        const bKeyToDayOfLeadTimes: { [key: string]: number } = {};
        (dayOfLeadTimes as DayOfLeadTimes[]).forEach((item) => {
            if (item.day_of_lead_time !== 45) {
                bKeyToDayOfLeadTimes[item.variant_id] = item.day_of_lead_time;
            }
        });

        const bKeyToDayOfStock: { [key: string]: number } = {};
        (dayOfLeadTimes as DayOfLeadTimes[]).forEach((item) => {
            if (item.day_of_stocks !== 90) {
                bKeyToDayOfStock[item.variant_id] = item.day_of_stocks;
            }
        });

        const bKeyToReplenishables: { [key: string]: 0 } = replenishables;

        // Create forecast date ranges if forecasted_gross_qty is required
        const forecastDateRanges: ForecastDateRange[] = [];
        if (queryRequiredFields.includes('forecasted_gross_qty')) {
            const currentDate = new Date().toISOString().split('T')[0];

            // Group by unique combinations of leadtime and dayOfStock
            const hKeyGroups: Record<
                string,
                {
                    leadtime: number;
                    dayOfStock: number;
                    bKeys: string[];
                }
            > = {};

            // Group h_keys by their leadtime and dayOfStock values
            Object.entries(bKeyToDayOfLeadTimes).forEach(([bKey, leadtime]) => {
                const dayOfStock = bKeyToDayOfStock[bKey] || 90; // Default to 90 if not found
                const key = `${leadtime}_${dayOfStock}`;

                if (!hKeyGroups[key]) {
                    hKeyGroups[key] = {
                        leadtime: Number(leadtime),
                        dayOfStock: Number(dayOfStock),
                        bKeys: [],
                    };
                }

                hKeyGroups[key].bKeys.push(bKey);
            });

            // For each group, create a date range
            const groupPromises = Object.values(hKeyGroups).map(async (group) => {
                const fromDate = currentDate;

                // Calculate toDate as current date + leadtime + dayOfStock
                const toDate = new Date();
                toDate.setDate(toDate.getDate() + group.leadtime + group.dayOfStock);
                const toDateStr = toDate.toISOString().split('T')[0];

                // Map b_keys to h_keys using the injected aggregateEDWDao
                let h_keys = group.bKeys;
                try {
                    const bKeyHKeyMap = await this.aggregateEDWDao.getBKeyHKeyMap({
                        options: { tenantId },
                        bKeys: group.bKeys,
                    });

                    // Replace b_keys with their corresponding h_keys
                    h_keys = group.bKeys.map((bKey) => bKeyHKeyMap[bKey] || bKey);
                } catch (error) {
                    console.error('Error mapping b_keys to h_keys:', error);
                    // Fall back to using b_keys if mapping fails
                }

                forecastDateRanges.push({
                    fromDate,
                    toDate: toDateStr,
                    h_keys,
                });
            });

            // Wait for all mapping operations to complete
            await Promise.all(groupPromises);
        }

        return {
            bKeyToOnOrder,
            bKeyToDayOfLeadTimes,
            bKeyToReplenishables,
            bKeyToDayOfStock,
            forecastDateRanges,
        };
    }

    async getPurchaseOrders(input: GetListPurchaseOrdersInput): Promise<GetPurchaseOrdersResponse> {
        const hostUrl = process.env.INVENTORY_API_URL ?? 'localhost:3003';
        const urlGetPurchaseOrders = `${hostUrl}/internal/v2/purchase-orders`;
        try {
            const response = await axios.get(urlGetPurchaseOrders, { params: input });
            const data = response.data.data;
            const totalCount = data.totalCount;
            const purchaseOrders = data.purchaseOrders.map((purchaseOrder: PurchaseOrder) => {
                return {
                    id: purchaseOrder.id,
                    source: purchaseOrder.source,
                    status: purchaseOrder.status,
                    archived: purchaseOrder.archived,
                    po_number: purchaseOrder.poNumber,
                    vendor_id: purchaseOrder.vendorId,
                    updated_at: purchaseOrder.updatedAt,
                    created_at: purchaseOrder.createdAt,
                    vendor_name: purchaseOrder.vendorName,
                    warehouse_id: purchaseOrder.warehouseId,
                    expected_date: purchaseOrder.expectedDate,
                    warehouse_name: purchaseOrder.warehouseName,
                    received_status: purchaseOrder.receivedStatus,
                    total_order_quantity: purchaseOrder.totalOrderQuantity,
                    total_received_quantity: purchaseOrder.totalReceivedQuantity,
                };
            });
            return { purchaseOrders, totalCount };
        } catch (error: unknown) {
            console.error('InventoryServiceProvider:getPurchaseOrders', error);
            throw error;
        }
    }
}
