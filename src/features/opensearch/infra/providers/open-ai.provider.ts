import { OPEN<PERSON>ARCH_INJECT_TOKENS } from '@constants';
import { Lifecycle, Provider } from '@heronjs/common';
import OpenAI from 'openai';

export interface IOpenAIProvider {
    generateEmbedding(text: string): Promise<number[]>;
}

@Provider({ token: OPENSEARCH_INJECT_TOKENS.PROVIDER.OPENAI, scope: Lifecycle.Singleton })
export class OpenAIProvider implements IOpenAIProvider {
    private readonly openai: OpenAI;

    constructor() {
        this.openai = new OpenAI({
            apiKey: process.env.OPENAI_API_KEY,
        });
    }

    async generateEmbedding(text: string): Promise<number[]> {
        try {
            const embeddingResponse = await this.openai.embeddings.create({
                model: 'text-embedding-3-small',
                input: text,
                encoding_format: 'float',
            });

            return embeddingResponse.data[0].embedding;
        } catch (error) {
            console.error('OpenAIProvider:generateEmbedding', error);
            throw error;
        }
    }
}
