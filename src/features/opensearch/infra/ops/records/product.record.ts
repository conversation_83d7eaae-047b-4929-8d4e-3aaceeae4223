import { CategoryItemRecord, Collection<PERSON>temR<PERSON>ord, TagItemR<PERSON>ord, VendorItemRecord } from './variant.record';

export type ProductRecord = {
    _source: {
        h_key: string;
        from_source: string;
        b_key: string;
        product_id: string;
        type: string;
        name: string;
        cost_per_item: number;
        price: number;
        compare_at_price: number;
        image: string;
        status: string;
        published_at: string;
        replenishable: string;
        categories: CategoryItemRecord[];
        collections: CollectionItemRecord[];
        tags: TagItemRecord[];
        vendors: VendorItemRecord[];
    };
};
