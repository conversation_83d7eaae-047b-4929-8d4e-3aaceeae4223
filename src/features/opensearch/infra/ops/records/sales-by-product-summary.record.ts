export type ProductSummaryRecord = {
    sales_metrics: {
        nested: {
            gross_sales: {
                value: number;
            };
            gross_qty: {
                value: number;
            };
            net_sales: {
                value: number;
            };
            net_qty: {
                value: number;
            };
            total_sales: {
                value: number;
            };
            return_value: {
                value: number;
            };
            return_qty: {
                value: number;
            };
            discount: {
                value: number;
            };
            tax: {
                value: number;
            };
            shipping: {
                value: number;
            };
            to_parent: {
                gross_profit: {
                    value: number;
                };
            };
        };
    };
    ads_metrics: {
        nested: {
            ad_spends: {
                value: number;
            };
            clicks: {
                value: number;
            };
            views: {
                value: number;
            };
        };
    };
    stock: {
        value: number;
    };
    on_order: {
        value: number;
    };
    return_rate: {
        value: number;
    };
    ats: {
        value: number;
    };
    total_inventory_value_stock: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };
};
