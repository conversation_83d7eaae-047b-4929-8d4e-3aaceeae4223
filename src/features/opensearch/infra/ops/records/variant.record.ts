export type CollectionItemRecord = {
    from_source: string;
    name: string;
    position: number;
    h_key: string;
};

export type CategoryItemRecord = {
    from_source: string;
    name: string;
    h_key: string;
};

export type TagItemRecord = {
    from_source: string;
    name: string;
    h_key: string;
};

export type VendorItemRecord = {
    from_source: string;
    name: string;
    h_key: string;
};

export type VariantRecord = {
    _source: {
        h_key: string;
        from_source: string;
        parent_h_key: string;
        b_key: string;
        variant_id: string;
        product_id: string;
        type: string;
        name: string;
        sku: string;
        barcode: string;
        cost_per_item: number;
        price: number;
        compare_at_price: number;
        image: string;
        color: string;
        default_color: string;
        size: string;
        status: string;
        source_created_at: string;
        published_at: string;
        categories: CategoryItemRecord[];
        collections: CollectionItemRecord[];
        tags: TagItemRecord[];
        vendors: VendorItemRecord[];
    };
};
