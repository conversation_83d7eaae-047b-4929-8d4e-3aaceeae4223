type Category = {
    name: string;
    h_key: string;
};

type Attribute = {
    product_id: string;
    categories: Category[];
    name: string;
    image: string;
};

export type AttributesOfProductRecord = {
    key: string;
    attributes: {
        hits: {
            hits: {
                _source: Attribute;
            }[];
        };
    };
    stock: {
        value: number;
    };
    gross_sales: {
        value: number;
    };
    gross_qty: {
        value: number;
    };
    return_value: {
        value: number;
    };
    return_qty: {
        value: number;
    };
    return_rate: {
        value: number;
    };
    net_sales: {
        value: number;
    };
    net_qty: {
        value: number;
    };
    total_sales: {
        value: number;
    };
    sales_per_day: {
        value: number;
    };
    sales_day_left: {
        value: number;
    };
    ats: {
        value: number;
    };
    sell_through: {
        value: number;
    };
    tax: {
        value: number;
    };
    discount: {
        value: number;
    };
    shipping: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };

    on_order: {
        value: number;
    };
    price: {
        value: number;
    };

    ads_metrics: {
        nested: {
            views: {
                value: number;
            };
        };
    };

    rop: {
        value: number;
    };
    conversion_rate: {
        value: number;
    };
    gross_sales_percentage: number;
    forecasted_gross_qty: {
        value: number;
    };
    re_order_qty: {
        value: number;
    };
    gross_profit: {
        value: number;
    };
    gross_margin: {
        value: number;
    };
    wos: {
        value: number;
    };
    forecast_sales_per_day: {
        value: number;
    };
    stock_percentage: number;
};
