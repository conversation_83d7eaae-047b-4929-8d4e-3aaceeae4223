export type SalesByCollectionRecord = {
    key: string;
    metadata: any;
    gross_sales: {
        value: number;
    };
    gross_qty: {
        value: number;
    };
    return_value: {
        value: number;
    };
    return_qty: {
        value: number;
    };
    total_sales: {
        value: number;
    };
    net_sales: {
        value: number;
    };
    net_qty: {
        value: number;
    };

    ad_spends: {
        value: number;
    };
    clicks: {
        value: number;
    };
    views: {
        value: number;
    };

    stock: {
        value: number;
    };
    on_order: {
        value: number;
    };
    ats: {
        value: number;
    };
    sell_through: {
        value: number;
    };
    total_inventory_value_stock: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };
    gross_sales_percentage: {
        value: number;
    };
    stock_percentage: {
        value: number;
    };
};
