export type OrderItemRecord = {
    h_item_key: string;
    item_sku: string;
    item_name: string;
    categories: {
        name: string;
        h_key: string;
    }[];
    item_price: number;
    cost_per_item: number;
    tax: number;
    net_qty: number;
    discount: number;
    shipping: number;
    gross_qty: number;
    net_sales: number;
    return_qty: number;
    gross_sales: number;
    total_sales: number;
    return_value: number;
    cost: number;
    gross_profit: number;
};

export type OrderAggregationRecord = {
    key: string;
    total_count: number;
    gross_sales: number;
    gross_qty: number;
    net_sales: number;
    net_qty: number;
    total_sales: number;
    tax: number;
    discount: number;
    return_qty: number;
    return_value: number;
    gross_profit: number;
};

export type OrderRecord = {
    _source: {
        order_id: string;
        from_source: string;
        channel_id: string;
        date: string;
        total_sales: string;
        net_sales: string;
        gross_sales: string;
        gross_qty: string;
        net_qty: string;
        return_qty: string;
        return_value: string;
        discount: string;
        tax: string;
        shipping: string;
        unique_item_count: string;
        gross_profit: string;
        order_items: OrderItemRecord[];
    };
};
