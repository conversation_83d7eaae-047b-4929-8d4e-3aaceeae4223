export type SalesByCategoryRecord = {
    key: string;
    metadata: any;
    gross_sales: {
        value: number;
    };
    gross_qty: {
        value: number;
    };
    return_value: {
        value: number;
    };
    return_qty: {
        value: number;
    };
    total_sales: {
        value: number;
    };
    net_sales: {
        value: number;
    };
    net_qty: {
        value: number;
    };

    ad_spends: {
        value: number;
    };
    clicks: {
        value: number;
    };
    views: {
        value: number;
    };

    stock: {
        value: number;
    };
    on_order: {
        value: number;
    };
    ats: {
        value: number;
    };
    sell_through: {
        value: number;
    };
    total_inventory_value_stock: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };
    gross_sales_percentage: {
        value: number;
    };
    stock_percentage: {
        value: number;
    };
    to_root: {
        sales_metrics: {
            nested: {
                sales_over_time: {
                    buckets: {
                        key_as_string: string;
                        key: number;
                        gross_sales: {
                            value: number;
                        };
                        gross_sales_rounded: {
                            value: number;
                        };
                        gross_qty: {
                            value: number;
                        };
                        gross_qty_rounded: {
                            value: number;
                        };
                    }[];
                };
            };
        };
        forecast_metrics: {
            nested: {
                forecasted_gross_qty: {
                    value: number;
                };
                forecast_over_time: {
                    buckets: {
                        key_as_string: string;
                        key: number;
                        forecasted_gross_qty: {
                            value: number;
                        };
                        forecasted_gross_qty_rounded: {
                            value: number;
                        };
                    }[];
                };
            };
        };
    };
};
