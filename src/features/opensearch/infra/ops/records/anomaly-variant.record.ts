type AnomalyVariantMetadata = {
    type: string;
    replenishable: boolean;
    b_key: string;
    variant_id: string;
    price: number;
    parent_h_key: string;
    sku: string;
    stock: number;
    barcode: string;
    h_key: string;
    from_source: string;
    size: string;
    name: string;
    status: string;
};

export type AnomalyVariantRecord = {
    key: string;
    metadata: {
        hits: {
            hits: {
                _source: AnomalyVariantMetadata;
            }[];
        };
    };
    gross_sales: {
        value: number;
    };
    compared_gross_sales: {
        value: number;
    };
    gross_sales_delta: {
        value: number;
    };
    views: {
        value: number;
    };
    compared_views: {
        value: number;
    };
    views_delta: {
        value: number;
    };
    ad_spends: {
        value: number;
    };
    compared_ad_spends: {
        value: number;
    };
    ad_spends_delta: {
        value: number;
    };
};
