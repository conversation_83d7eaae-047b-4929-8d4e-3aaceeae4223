import { CategoryItemRecord, CollectionItemRecord, TagItemRecord, VendorItemRecord } from './variant.record';

type ProductMetadata = {
    b_key: string;
    barcode: string;
    categories: CategoryItemRecord[];
    collections: CollectionItemRecord[];
    color: string;
    default_color: string;
    compare_at_price: number;
    cost: number;
    dda_code: string;
    dda_value: string;
    from_source: string;
    h_key: string;
    image: string;
    name: string;
    price: number;
    product_h_key: string;
    product_id: string;
    replenishable: string;
    trend: string;
    sale_pattern: string;
    forecast_confidence: string;
    size: string;
    sku: string;
    status: string;
    tags: TagItemRecord[];
    total_inventory_value_stock: number;
    type: string;
    vendors: VendorItemRecord[];
};

export type SalesByProductRecord = {
    key: string;
    product_data: {
        metadata: {
            hits: {
                hits: {
                    _source: ProductMetadata;
                }[];
            };
        };
    };
    gross_sales: {
        value: number;
    };
    gross_qty: {
        value: number;
    };
    net_sales: {
        value: number;
    };
    net_qty: {
        value: number;
    };
    total_sales: {
        value: number;
    };
    return_value: {
        value: number;
    };
    return_qty: {
        value: number;
    };
    discount: {
        value: number;
    };
    tax: {
        value: number;
    };
    shipping: {
        value: number;
    };
    sales_per_day: {
        value: number;
    };
    return_rate: {
        value: number;
    };
    ad_spends: {
        value: number;
    };
    clicks: {
        value: number;
    };
    views: {
        value: number;
    };
    source_created_at: {
        value: number;
    };
    days_on_site: {
        value: number;
    };
    stock: {
        value: number;
    };
    on_order: {
        value: number;
    };
    ats: {
        value: number;
    };
    sales_days_left: {
        value: number;
    };
    gross_profit: {
        value: number;
    };
    gross_margin: {
        value: number;
    };
    total_inventory_value_stock: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };
    wos: {
        value: number;
    };
    stock_percentage: {
        value: number;
    };
    re_order_qty: {
        value: number;
    };
    rop: {
        value: number;
    };
    conversion_rate: {
        value: number;
    };
    sell_through: {
        value: number;
    };
    replenishable: {
        value: number;
    };
    lead_time: {
        value: number;
    };
    days_of_stock: {
        value: number;
    };
    gross_sales_percentage: {
        value: number;
    };
    price: {
        value: number;
    };
    forecast_value: {
        value: number;
    };
    forecast_sales_per_day: {
        value: number;
    };
    forecasted_gross_qty: {
        value: number;
    };
    forecasted_gross_sales: {
        value: number;
    };
    forecasted_sales_days_remaining_based_on_stock: {
        value: number;
    };
    forecasted_sales_days_remaining_based_on_ats: {
        value: number;
    };
    product_grade: {
        value: number;
    };
    collection_position: {
        value: number;
    };
    gross_sales_rank: {
        value: number;
    };
};
