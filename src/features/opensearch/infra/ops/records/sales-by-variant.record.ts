import { CategoryItemRecord, Collection<PERSON>temR<PERSON>ord, TagItemR<PERSON>ord, VendorItemRecord } from './variant.record';

type VariantMetadata = {
    ats: number;
    sale_pattern: string;
    forecast_confidence: string;
    color: string;
    cost: number;
    on_order: number;
    trend: string;
    type: string;
    replenishable: string;
    b_key: string;
    variant_id: string;
    collections: CollectionItemRecord[];
    price: number;
    parent_h_key: string;
    etl_ats: number;
    categories: CategoryItemRecord[];
    sku: string;
    stock: number;
    barcode: string;
    dda_code: string;
    compare_at_price: number;
    image: string;
    dda_value: string;
    h_key: string;
    tags: TagItemRecord[];
    app_ats: number;
    from_source: string;
    size: string;
    name: string;
    product_id: string;
    product_h_key: string;
    default_color: string;
    status: string;
    vendors: VendorItemRecord[];
};

export type SalesByVariantRecord = {
    key: string;
    metadata: {
        hits: {
            hits: {
                _source: VariantMetadata;
            }[];
        };
    };
    cost_per_item: {
        value: number;
    };
    stock: {
        value: number;
    };
    on_order: {
        value: number;
    };
    ats: {
        value: number;
    };
    gross_sales: {
        value: number;
    };
    gross_qty: {
        value: number;
    };
    sales_days_left: {
        value: number;
    };
    gross_profit: {
        value: number;
    };
    gross_margin: {
        value: number;
    };
    total_inventory_value_stock: {
        value: number;
    };
    total_inventory_value_ats: {
        value: number;
    };
    wos: {
        value: number;
    };
    net_sales: {
        value: number;
    };
    net_qty: {
        value: number;
    };
    total_sales: {
        value: number;
    };
    return_value: {
        value: number;
    };
    return_qty: {
        value: number;
    };
    discount: {
        value: number;
    };
    tax: {
        value: number;
    };
    shipping: {
        value: number;
    };
    sales_per_day: {
        value: number;
    };
    ad_spends: {
        value: number;
    };
    clicks: {
        value: number;
    };
    views: {
        value: number;
    };
    return_rate: {
        value: number;
    };
    days_on_site: {
        value: number;
    };
    sell_through: {
        value: number;
    };
    stock_percentage: {
        value: number;
    };
    re_order_qty: {
        value: number;
    };
    rop: {
        value: number;
    };
    conversion_rate: {
        value: number;
    };
    replenishable: {
        value: number;
    };
    lead_time: {
        value: number;
    };
    days_of_stock: {
        value: number;
    };
    gross_sales_percentage: {
        value: number;
    };
    forecast_value: {
        value: number;
    };
    forecast_sales_per_day: {
        value: number;
    };
    forecasted_gross_sales: {
        value: number;
    };
    forecasted_gross_qty: {
        value: number;
    };
    forecasted_sales_days_remaining_based_on_stock: {
        value: number;
    };
    forecasted_sales_days_remaining_based_on_ats: {
        value: number;
    };
    product_grade: {
        value: number;
    };
    collection_position: {
        value: number;
    };
    gross_sales_rank: {
        value: number;
    };
    sales_metrics: {
        nested: {
            sales_over_time: {
                buckets: {
                    key_as_string: string;
                    key: number;
                    gross_sales: {
                        value: number;
                    };
                    gross_sales_rounded: {
                        value: number;
                    };
                    gross_qty: {
                        value: number;
                    };
                    gross_qty_rounded: {
                        value: number;
                    };
                }[];
            };
        };
    };
    forecast_metrics: {
        nested: {
            forecast_over_time: {
                buckets: {
                    key_as_string: string;
                    key: number;
                    forecasted_gross_qty: {
                        value: number;
                    };
                    forecasted_gross_qty_rounded: {
                        value: number;
                    };
                }[];
            };
        };
    };
    stock_metrics: {
        nested: {
            days_in_stock_over_time: {
                buckets: {
                    key_as_string: string;
                    key: number;
                    days_in_stock: {
                        value: number;
                    };
                }[];
            };
        };
    };
    in_transit_stock: {
        filtered_by_date: {
            in_transit_stock: {
                value: number;
            };
        };
    };
};
