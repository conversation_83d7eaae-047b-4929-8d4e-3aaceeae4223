import { FilterInput, SortType } from '@cbidigital/aqua-ddd';
import { FieldSchema } from '@core';
import { IndexKeys } from '../enums';

export namespace DaoUtitls {
    export const getIndexName = (tenantId: string, indexKey: IndexKeys) => {
        return `${tenantId}-${indexKey}`;
    };

    export const getRequiredFields = (
        input: {
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
            fields?: string[];
            channelId?: string;
        },
        fieldSchema: FieldSchema,
    ): string[] => {
        const { sort, filter, fields } = input;

        if (!fields || fields.length === 0) return [];

        const sortFields = sort ? sort.map((sortItem) => Object.keys(sortItem)[0]) : [];
        const filterFields = filter ? Object.keys(filter) : [];
        const combineFields = [...sortFields, ...filterFields, ...(fields ?? [])];

        let requiredFields: string[] = [];

        combineFields.forEach((field) => {
            const schema = fieldSchema[field];
            if (schema) {
                requiredFields.push(schema.key);
                if (schema.overrideSortKey) requiredFields.push(schema.overrideSortKey);
                if (schema.requiredKeys)
                    requiredFields = [
                        ...requiredFields,
                        ...getRequiredFieldsRecursively({ fields: schema.requiredKeys }, fieldSchema),
                    ];
            }
        });

        const uniqueFields = Array.from(new Set(requiredFields));

        uniqueFields.push('max_score');

        return uniqueFields;
    };
}

export const getRequiredFieldsRecursively = (
    input: {
        fields?: string[];
    },
    fieldSchema: FieldSchema,
): string[] => {
    const { fields } = input;

    if (!fields || fields.length === 0) return [];

    const requiredFields: string[] = [];

    fields.forEach((field) => {
        const schema = fieldSchema[field];
        if (schema) {
            requiredFields.push(schema.key);
            if (schema.requiredKeys) {
                requiredFields.push(
                    ...getRequiredFieldsRecursively({ fields: schema.requiredKeys }, fieldSchema),
                );
            }
        }
    });

    const uniqueFields = Array.from(new Set(requiredFields));

    return uniqueFields;
};

export const hasMatchKey = (data: FilterInput) => {
    for (const key in data) {
        if (typeof data[key] === 'object' && data[key] !== null) {
            for (const keyNested in data[key]) {
                if (keyNested === 'match') {
                    return true;
                }
            }
        }
    }
    return false;
};

export function removeUndefinedFields<T extends Record<string, any>>(obj: T): Partial<T> {
    return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== undefined)) as Partial<T>;
}
