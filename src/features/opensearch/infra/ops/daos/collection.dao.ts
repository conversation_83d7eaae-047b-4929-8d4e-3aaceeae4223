import { FilterInput, RepositoryOptions, SortType } from '@cbidigital/aqua-ddd';
import { DefaultFields, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { <PERSON>acheDAO, SemanticSearch } from '@core';
import {
    CollectionR<PERSON>ord,
    CollectionRecordMapper,
    CollectionSummaryRecordMapper,
    CollectionSummaryRequestBodySearch,
    GetCollectionsRequestBodySearch,
    ICollectionRecordMapper,
    ICollectionSummaryRecordMapper,
    IndexKeys,
    ISalesByCollectionRecordMapper,
    SalesByCollectionFieldSchema,
    SalesByCollectionRecord,
    SalesByCollectionRecordMapper,
    SalesByCollectionRequestBodySearch,
} from '@features/opensearch';
import {
    CollectionDTO,
    SalesByCollectionDto,
    SalesByCollectionSummaryDTO,
} from '@features/opensearch/domain';
import { Dao, Inject, Lifecycle, Logger } from '@heronjs/common';
import { OpensearchResponseUtil } from '@utils';
import { IOPSDataSource } from '../../../../../data-sources';
import { IInventoryServiceProvider, IOpenAIProvider, ItemTypes } from '../../providers';
import { DaoUtitls } from '../utils';

@Dao({ token: OPENSEARCH_INJECT_TOKENS.OPS_DAO.COLLECTION, scope: Lifecycle.Singleton })
export class CollectionDAO {
    private readonly logger = new Logger(this.constructor.name);
    private readonly recordMapper: ISalesByCollectionRecordMapper = new SalesByCollectionRecordMapper();
    private readonly collectionRecordMapper: ICollectionRecordMapper = new CollectionRecordMapper();
    private readonly collectionSummaryRecordMapper: ICollectionSummaryRecordMapper =
        new CollectionSummaryRecordMapper();
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE) private readonly osService: IOPSDataSource,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        private readonly inventoryService: IInventoryServiceProvider,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.OPENAI)
        protected readonly openAIProvider: IOpenAIProvider,
    ) {}

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);

        switch (err.constraint) {
            default:
                throw err;
        }
    }

    @CacheDAO()
    async getCollections(
        input: {
            offset?: number;
            limit?: number;
            fields?: string[];
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
            semanticSearch?: SemanticSearch;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        collections: Partial<CollectionDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId || '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Collection);

            const query = GetCollectionsRequestBodySearch.buildQuery({
                ...input,
            });

            const jsonQuery: any = query.toJSON();

            const knnSearchVector = input.semanticSearch?.text
                ? await this.openAIProvider.generateEmbedding(input.semanticSearch.text)
                : undefined;
            if (knnSearchVector) {
                jsonQuery.min_score = input.semanticSearch?.minScore;
                jsonQuery.query = {
                    bool: {
                        must: [
                            {
                                knn: {
                                    name_knn: {
                                        vector: knnSearchVector,
                                        k: input.limit,
                                    },
                                },
                            },
                            {
                                ...(jsonQuery.query ?? { match_all: {} }),
                            },
                        ],
                    },
                };
            }

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: jsonQuery,
                },
            });
            const records: CollectionRecord[] = response.body.hits.hits;
            const totalCount: number = response.body.hits.total.value;
            const collections = this.collectionRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                collections,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesByCollection(
        input: {
            offset?: number;
            limit?: number;
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<SalesByCollectionDto>[];
    }> {
        try {
            const tenantId = options?.tenantId!;
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: input.filter,
                    fields: ['h_key'],
                },
                SalesByCollectionFieldSchema,
            );
            const queryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByCollectionFieldSchema);

            // Get metadata
            const getMetadataPromises = [];
            const getInventoryDataPromise = this.inventoryService.getInventoryApiData(
                { tenantId, type: ItemTypes.Variant },
                queryRequiredFields,
            );
            getMetadataPromises.push(getInventoryDataPromise);

            let getTotalStockPromise;
            if (input.fields?.includes('stock_percentage')) {
                getTotalStockPromise = OpensearchResponseUtil.getTotalStock(this.osService, tenantId, index);
            }
            getMetadataPromises.push(getTotalStockPromise);

            let getTotalGrossSalesPromise;
            if (input.fields?.includes('gross_sales_percentage')) {
                getTotalGrossSalesPromise = OpensearchResponseUtil.getTotalGrossSales(
                    this.osService,
                    tenantId,
                    index,
                    {
                        ...input,
                    },
                );
            }
            getMetadataPromises.push(getTotalGrossSalesPromise);

            const [inventoryData, totalStock, totalGrossSales] = await Promise.all(getMetadataPromises);
            const metadata: Record<string, any> = {
                ...((inventoryData as object) ?? {}),
                totalStock,
                totalGrossSales,
            };

            // Get ids
            const idsQuery = SalesByCollectionRequestBodySearch.buildQuery({
                ...input,
                fields: idsQueryRequiredFields,
                defaultSort: DefaultFields.relevanceScore.sort,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.collections.filtered_collections.collections.buckets.key'],
                },
            });
            const idsRecords: SalesByCollectionRecord[] =
                idsResponse.body.aggregations.collections.filtered_collections.collections.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return {
                    totalCount,
                    items: [],
                };
            }

            let ids = idsRecords.map((record) => record.key);

            const { offset, limit } = input;
            if (input.offset !== undefined && input.limit !== undefined)
                ids = ids.slice(offset, offset! + limit!);

            // Get all fields
            const query = SalesByCollectionRequestBodySearch.buildQuery({
                ...input,
                fields: queryRequiredFields,
                ids,
                defaultSort: DefaultFields.relevanceScore.sort,
                metadata,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const records: SalesByCollectionRecord[] =
                response.body.aggregations.collections.filtered_collections.collections.buckets;

            const items = this.recordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesSummary(
        input: {
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
            metadata?: Record<string, any>;
        },
        options?: RepositoryOptions,
    ): Promise<Partial<SalesByCollectionSummaryDTO>> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: input.filter,
                    fields: ['h_key'],
                },
                SalesByCollectionFieldSchema,
            );
            const queryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByCollectionFieldSchema);

            // Get metadata
            const getMetadataPromises = [];
            const getInventoryDataPromise = this.inventoryService.getInventoryApiData(
                { tenantId, type: ItemTypes.Variant },
                queryRequiredFields,
            );
            getMetadataPromises.push(getInventoryDataPromise);

            let getTotalStockPromise;
            if (input.fields?.includes('stock_percentage')) {
                getTotalStockPromise = OpensearchResponseUtil.getTotalStock(this.osService, tenantId, index);
            }
            getMetadataPromises.push(getTotalStockPromise);

            let getTotalGrossSalesPromise;
            if (input.fields?.includes('gross_sales_percentage')) {
                getTotalGrossSalesPromise = OpensearchResponseUtil.getTotalGrossSales(
                    this.osService,
                    tenantId,
                    index,
                    {
                        ...input,
                    },
                );
            }
            getMetadataPromises.push(getTotalGrossSalesPromise);

            const [inventoryData, totalStock, totalGrossSales] = await Promise.all(getMetadataPromises);
            const metadata: Record<string, any> = {
                ...((inventoryData as object) ?? {}),
                totalStock,
                totalGrossSales,
            };

            // Get ids
            const idsQuery = SalesByCollectionRequestBodySearch.buildQuery({
                ...input,
                fields: idsQueryRequiredFields,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.collections.filtered_collections.collections.buckets.key'],
                },
            });
            const idsRecords: SalesByCollectionRecord[] =
                idsResponse.body.aggregations.collections.filtered_collections.collections.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return {
                    total_count: 0,
                    gross_sales: 0,
                    gross_qty: 0,
                    net_sales: 0,
                    net_qty: 0,
                    total_sales: 0,
                    return_value: 0,
                    return_qty: 0,
                    ad_spends: 0,
                    clicks: 0,
                    views: 0,
                    stock: 0,
                    on_order: 0,
                    ats: 0,
                    total_inventory_value_stock: 0,
                    total_inventory_value_ats: 0,
                    sell_through: 0,
                };
            }

            const ids = idsRecords.map((record) => record.key);

            const query = CollectionSummaryRequestBodySearch.buildQuery({
                ...input,
                ids,
                fields: queryRequiredFields,
                metadata,
            });
            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const summaryRecord: SalesByCollectionRecord = response.body.aggregations.summary;

            const summary = this.collectionSummaryRecordMapper.fromRecordToDto(summaryRecord);

            return {
                total_count: totalCount,
                ...summary,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }
}
