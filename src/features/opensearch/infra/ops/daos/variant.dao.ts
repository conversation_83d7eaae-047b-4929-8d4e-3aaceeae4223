import { FilterInput, RepositoryOptions, SortType } from '@cbidigital/aqua-ddd';
import { DefaultFields, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { CacheDAO, SemanticSearch } from '@core';
import { TimeFrame } from '@features/opensearch/app';
import { SalesByVariantDTO, SalesByVariantSummaryDTO, VariantDTO } from '@features/opensearch/domain';
import {
    AnomalyVariantRecord,
    AnomalyVariantRecordMapper,
    AnomalyVariantRequestBodySearch,
    GetVariantsRequestBodySearch,
    IAggregateEDWDao,
    IAnomalyVariantRecordMapper,
    IndexKeys,
    ISalesByVariantRecordMapper,
    IVariantRecordMapper,
    IVariantSummaryRecordMapper,
    SalesByVariantFieldSchema,
    SalesByVariantRecord,
    SalesByVariantRecordMapper,
    SalesByVariantRequestBodySearch,
    VariantRecord,
    <PERSON>ariantRecordMapper,
    VariantSummaryFieldSchema,
    VariantSummaryRecord,
    VariantSummaryRecordMapper,
    VariantSummaryRequestBodySearch,
} from '@features/opensearch/infra';
import { Dao, Inject, Lifecycle, Logger } from '@heronjs/common';
import { OpensearchResponseUtil } from '@utils';
import { IOPSDataSource } from '../../../../../data-sources';
import {
    IInventoryServiceProvider,
    IOpenAIProvider,
    ItemTypes,
    ITenantSettingsProvider,
} from '../../providers';
import { DaoUtitls } from '../utils';

@Dao({ token: OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT, scope: Lifecycle.Singleton })
export class VariantDAO {
    private readonly logger = new Logger(this.constructor.name);
    private readonly variantRecordMapper: IVariantRecordMapper = new VariantRecordMapper();
    private readonly salesByVariantRecordMapper: ISalesByVariantRecordMapper =
        new SalesByVariantRecordMapper();
    private readonly summaryRecordMapper: IVariantSummaryRecordMapper = new VariantSummaryRecordMapper();
    private readonly anomalyVariantRecordMapper: IAnomalyVariantRecordMapper =
        new AnomalyVariantRecordMapper();

    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE) private readonly osService: IOPSDataSource,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        private readonly inventoryService: IInventoryServiceProvider,
        @Inject(OPENSEARCH_INJECT_TOKENS.EDW_DAO.AGGREGATE)
        protected readonly aggregateDao: IAggregateEDWDao,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.TENANT_SETTINGS)
        protected readonly tenantSettingsProvider: ITenantSettingsProvider,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.OPENAI)
        protected readonly openAIProvider: IOpenAIProvider,
    ) {}

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);

        switch (err.constraint) {
            default:
                throw err;
        }
    }

    @CacheDAO()
    async getVariants(
        input: {
            offset?: number;
            limit?: number;
            fields?: string[];
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
            semanticSearch?: SemanticSearch;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<VariantDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId || '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const query = GetVariantsRequestBodySearch.buildQuery({
                ...input,
            });

            const jsonQuery: any = query.toJSON();

            const knnSearchVector = input.semanticSearch?.text
                ? await this.openAIProvider.generateEmbedding(input.semanticSearch.text)
                : undefined;
            if (knnSearchVector) {
                jsonQuery.min_score = input.semanticSearch?.minScore;
                jsonQuery.query = {
                    bool: {
                        must: [
                            {
                                knn: {
                                    name_knn: {
                                        vector: knnSearchVector,
                                        k: input.limit,
                                    },
                                },
                            },
                            {
                                ...(jsonQuery.query ?? { match_all: {} }),
                            },
                        ],
                    },
                };
            }

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: jsonQuery,
                },
            });
            const records: VariantRecord[] = response.body.hits.hits;
            const totalCount: number = response.body.hits.total.value;
            const items = this.variantRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    private async getMetadata(payload: {
        input: {
            fromDate?: string;
            toDate?: string;
            fields?: string[];
            filter?: FilterInput;
            sort?: Record<string, SortType>[];
            metadata?: Record<string, any>;
        };
        tenantId: string;
        index: string;
        requiredFields: string[];
    }): Promise<Record<string, any>> {
        const { input, tenantId, index, requiredFields } = payload;

        const client = this.osService.getClient();

        const defaultMetadata: {
            bKeyToOnOrder: Record<string, number>;
            bKeyToDayOfLeadTimes: Record<string, number>;
            bKeyToReplenishables: Record<string, number>;
            bKeyToDayOfStock: Record<string, number>;
            productGradeData: Record<string, number>;
            grossSalesRankData: Record<string, number>;
            totalInTransitStock: Record<string, number>;
        } = {
            bKeyToOnOrder: {},
            bKeyToDayOfLeadTimes: {},
            bKeyToReplenishables: {},
            bKeyToDayOfStock: {},
            productGradeData: {},
            grossSalesRankData: {},
            totalInTransitStock: {},
        };

        // Get metadata
        const getMetadataPromises = [];
        const getInventoryDataPromise = this.inventoryService.getInventoryApiData(
            { tenantId, type: ItemTypes.Variant },
            requiredFields,
        );
        getMetadataPromises.push(getInventoryDataPromise);

        let getTotalStockPromise;
        if (requiredFields.includes('stock_percentage')) {
            getTotalStockPromise = OpensearchResponseUtil.getTotalStock(this.osService, tenantId, index);
        }
        getMetadataPromises.push(getTotalStockPromise);

        let getTotalGrossSalesPromise;
        if (requiredFields.includes('gross_sales_percentage')) {
            getTotalGrossSalesPromise = OpensearchResponseUtil.getTotalGrossSales(
                this.osService,
                tenantId,
                index,
                {
                    ...input,
                },
            );
        }
        getMetadataPromises.push(getTotalGrossSalesPromise);

        if (requiredFields.includes('product_grade')) {
            const timezone = await this.tenantSettingsProvider.getTimezone(tenantId);
            getMetadataPromises.push(
                this.aggregateDao.getProductGrade({
                    args: {
                        fromDate: input.fromDate,
                        toDate: input.toDate,
                        timezone: timezone,
                    },
                    options: { tenantId },
                    type: ItemTypes.Variant,
                }),
            );
        } else getMetadataPromises.push(undefined);

        if (requiredFields.includes('gross_sales_rank')) {
            getMetadataPromises.push(
                this.aggregateDao.getGrossSalesRankMap({
                    args: {
                        fromDate: input.fromDate,
                        toDate: input.toDate,
                        filter: input.filter,
                    },
                    options: { tenantId },
                    type: ItemTypes.Variant,
                }),
            );
        } else getMetadataPromises.push(undefined);

        if (requiredFields.includes('in_transit_stock')) {
            getMetadataPromises.push(
                OpensearchResponseUtil.getTotalInTransitStock(this.osService, tenantId, index, {
                    ...input,
                }),
            );
        } else getMetadataPromises.push(undefined);

        const [
            inventoryData,
            totalStock,
            totalGrossSales,
            productGradeData,
            grossSalesRankData,
            totalInTransitStock,
        ] = await Promise.all(getMetadataPromises);

        return {
            ...defaultMetadata,
            ...((inventoryData as object) ?? {}),
            totalStock,
            totalGrossSales,
            productGradeData,
            grossSalesRankData,
            totalInTransitStock,
        };
    }

    @CacheDAO()
    async getSalesByVariant(
        input: {
            offset?: number;
            limit?: number;
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
            metadata?: Record<string, any>;
            channelId?: string;
            productGrade?: Record<string, any>;
            timeFrame?: TimeFrame;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<SalesByVariantDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: input.filter,
                    fields: ['h_key'],
                },
                SalesByVariantFieldSchema,
            );
            const queryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByVariantFieldSchema);

            const metadata = await this.getMetadata({
                input,
                tenantId,
                index,
                requiredFields: queryRequiredFields,
            });

            // Get ids
            const idsQuery = SalesByVariantRequestBodySearch.buildQuery({
                ...input,
                fields: idsQueryRequiredFields,
                defaultSort: DefaultFields.relevanceScore.sort,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.items.buckets.key'],
                },
            });
            const idsRecords: SalesByVariantRecord[] = idsResponse.body.aggregations.items.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return {
                    totalCount,
                    items: [],
                };
            }

            let ids = idsRecords.map((record) => record.key);
            const { offset, limit } = input;
            if (input.offset !== undefined && input.limit !== undefined)
                ids = ids.slice(offset, offset! + limit!);

            queryRequiredFields.push(...DefaultFields.relevanceScore.fields);

            // Get all fields
            const query = SalesByVariantRequestBodySearch.buildQuery({
                ...input,
                fields: queryRequiredFields,
                ids,
                defaultSort: DefaultFields.relevanceScore.sort,
                metadata,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const records: SalesByVariantRecord[] = response.body.aggregations.items.buckets;
            const items = this.salesByVariantRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesSummary(
        input: {
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
            metadata?: Record<string, any>;
        },
        options?: RepositoryOptions,
    ): Promise<Partial<SalesByVariantSummaryDTO>> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);
            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: input.filter,
                    fields: ['h_key'],
                },
                SalesByVariantFieldSchema,
            );

            const queryRequiredFields = DaoUtitls.getRequiredFields(input, VariantSummaryFieldSchema);

            const metadataRequiredFields = DaoUtitls.getRequiredFields(input, SalesByVariantFieldSchema);
            const metadata = await this.getMetadata({
                input,
                tenantId,
                index,
                requiredFields: metadataRequiredFields,
            });

            // Get ids
            const idsQuery = SalesByVariantRequestBodySearch.buildQuery({
                ...input,
                fields: idsQueryRequiredFields,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.items.buckets.key'],
                },
            });
            const idsRecords: SalesByVariantRecord[] = idsResponse.body.aggregations.items.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return {
                    total_count: 0,
                    gross_sales: 0,
                    gross_qty: 0,
                    net_sales: 0,
                    net_qty: 0,
                    total_sales: 0,
                    return_value: 0,
                    return_qty: 0,
                    discount: 0,
                    tax: 0,
                    shipping: 0,
                    ad_spends: 0,
                    clicks: 0,
                    views: 0,
                    stock: 0,
                    on_order: 0,
                    ats: 0,
                };
            }

            const ids = idsRecords.map((record) => record.key);

            const query = VariantSummaryRequestBodySearch.buildQuery({
                ...input,
                ids,
                fields: queryRequiredFields,
                metadata,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const summaryRecord: VariantSummaryRecord = response.body.aggregations.summary;

            const summary = this.summaryRecordMapper.fromRecordToDto(summaryRecord);

            return {
                total_count: totalCount,
                ...summary,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async findAnomalyVariant(
        input: {
            offset?: number;
            limit?: number;
            fromDate?: string;
            toDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<SalesByVariantDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            // Get ids
            // const idsQueryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByVariantFieldSchema);

            const idsQuery = AnomalyVariantRequestBodySearch.buildQuery({
                ...input,
                // fields: idsQueryRequiredFields,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.items.buckets.key'],
                },
            });

            const idsRecords: SalesByVariantRecord[] = idsResponse.body.aggregations.items.buckets;
            const totalCount = idsRecords.length;
            let ids = idsRecords.map((record) => record.key);
            const { offset, limit } = input;
            if (input.offset !== undefined && input.limit !== undefined)
                ids = ids.slice(offset, offset! + limit!);

            // Get all fields
            const queryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByVariantFieldSchema);

            const query = AnomalyVariantRequestBodySearch.buildQuery({
                ...input,
                fields: queryRequiredFields,
                ids,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const records: AnomalyVariantRecord[] = response.body.aggregations.items.buckets;
            const items = this.anomalyVariantRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }
}
