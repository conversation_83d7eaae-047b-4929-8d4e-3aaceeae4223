import { FilterInput, RepositoryOptions, SortType } from '@cbidigital/aqua-ddd';
import { DefaultFields, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { <PERSON>acheDAO, MetadataFilter, SemanticSearch } from '@core';
import { TimeFrame } from '@features/opensearch/app';
import { SalesByProductDTO, SalesByProductSummaryDTO } from '@features/opensearch/domain';
import {
    filterFactory,
    IAggregateEDWDao,
    IndexKeys,
    IProductRecordMapper,
    IProductSummaryRecordMapper,
    ISalesByProductRecordMapper,
    ProductRecord,
    ProductRecordMapper,
    ProductRequestBodySearch,
    ProductSummaryFieldSchema,
    ProductSummaryRecord,
    ProductSummaryRecordMapper,
    ProductSummaryRequestBodySearch,
    SalesBarChartRequestBodySearch,
    SalesByProductFieldSchema,
    SalesByProductRecord,
    SalesByProductRecordMapper,
    SalesByProductRequestBodySearch,
    SalesByVariantRequestBodySearch,
    SalesOverTimeChartRequestBodySearch,
} from '@features/opensearch/infra';
import { Dao, Inject, Lifecycle, Logger } from '@heronjs/common';
import { OpensearchResponseUtil } from '@utils';
import { IOPSDataSource } from '../../../../../data-sources';
import {
    IInventoryServiceProvider,
    IOpenAIProvider,
    ItemTypes,
    ITenantSettingsProvider,
} from '../../providers';
import { DaoUtitls } from '../utils';

@Dao({ token: OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT, scope: Lifecycle.Singleton })
export class ProductDAO {
    private readonly salesByProductRecordMapper: ISalesByProductRecordMapper =
        new SalesByProductRecordMapper();
    private readonly productRecordMapper: IProductRecordMapper = new ProductRecordMapper();
    private readonly summaryRecordMapper: IProductSummaryRecordMapper = new ProductSummaryRecordMapper();

    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE) private readonly osService: IOPSDataSource,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        private readonly inventoryService: IInventoryServiceProvider,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.OPENAI)
        protected readonly openAIProvider: IOpenAIProvider,
        @Inject(OPENSEARCH_INJECT_TOKENS.EDW_DAO.AGGREGATE)
        protected readonly aggregateDao: IAggregateEDWDao,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.TENANT_SETTINGS)
        protected readonly tenantSettingsProvider: ITenantSettingsProvider,
    ) {}

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);

        switch (err.constraint) {
            default:
                throw err;
        }
    }

    @CacheDAO()
    async getProducts(
        input: {
            offset?: number;
            limit?: number;
            fields?: string[];
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
            semanticSearch?: SemanticSearch;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<SalesByProductDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const query = ProductRequestBodySearch.buildQuery({
                ...input,
            });

            const jsonQuery: any = query.toJSON();

            const knnSearchVector = input.semanticSearch?.text
                ? await this.openAIProvider.generateEmbedding(input.semanticSearch.text)
                : undefined;
            if (knnSearchVector) {
                jsonQuery.min_score = input.semanticSearch?.minScore;
                jsonQuery.query = {
                    bool: {
                        must: [
                            {
                                knn: {
                                    name_knn: {
                                        vector: knnSearchVector,
                                        k: input.limit,
                                    },
                                },
                            },
                            {
                                ...(jsonQuery.query ?? { match_all: {} }),
                            },
                        ],
                    },
                };
            }

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: jsonQuery,
                },
            });
            const records: ProductRecord[] = response.body.hits.hits;
            const totalCount: number = response.body.hits.total.value;

            const items = this.productRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    private async getMetadata(payload: {
        input: {
            fromDate?: string;
            toDate?: string;
            fields?: string[];
            filter?: FilterInput;
            sort?: Record<string, SortType>[];
            metadata?: Record<string, any>;
        };
        tenantId: string;
        index: string;
        requiredFields: string[];
    }): Promise<Record<string, any>> {
        const { input, tenantId, index, requiredFields } = payload;

        const defaultMetadata: {
            bKeyToOnOrder: Record<string, number>;
            bKeyToDayOfLeadTimes: Record<string, number>;
            bKeyToReplenishables: Record<string, number>;
            bKeyToDayOfStock: Record<string, number>;
            productGradeData: Record<string, number>;
            grossSalesRankData: Record<string, number>;
        } = {
            bKeyToOnOrder: {},
            bKeyToDayOfLeadTimes: {},
            bKeyToReplenishables: {},
            bKeyToDayOfStock: {},
            productGradeData: {},
            grossSalesRankData: {},
        };

        // Get metadata
        const getMetadataPromises = [];
        const getInventoryDataPromise = this.inventoryService.getInventoryApiData(
            { tenantId, type: ItemTypes.Product },
            requiredFields,
        );
        getMetadataPromises.push(getInventoryDataPromise);

        let getTotalStockPromise;
        if (requiredFields.includes('stock_percentage')) {
            getTotalStockPromise = OpensearchResponseUtil.getTotalStock(this.osService, tenantId, index);
        }
        getMetadataPromises.push(getTotalStockPromise);

        let getTotalGrossSalesPromise;
        if (requiredFields.includes('gross_sales_percentage')) {
            getTotalGrossSalesPromise = OpensearchResponseUtil.getTotalGrossSales(
                this.osService,
                tenantId,
                index,
                {
                    ...input,
                },
            );
        }
        getMetadataPromises.push(getTotalGrossSalesPromise);

        if (requiredFields.includes('product_grade')) {
            const timezone = await this.tenantSettingsProvider.getTimezone(tenantId);
            getMetadataPromises.push(
                this.aggregateDao.getProductGrade({
                    args: {
                        fromDate: input.fromDate,
                        toDate: input.toDate,
                        timezone: timezone,
                    },
                    options: { tenantId },
                    type: ItemTypes.Product,
                }),
            );
        } else getMetadataPromises.push(undefined);

        if (requiredFields.includes('gross_sales_rank')) {
            getMetadataPromises.push(
                this.aggregateDao.getGrossSalesRankMap({
                    args: {
                        fromDate: input.fromDate,
                        toDate: input.toDate,
                        filter: input.filter,
                    },
                    options: { tenantId },
                    type: ItemTypes.Product,
                }),
            );
        } else getMetadataPromises.push(undefined);

        const [inventoryData, totalStock, totalGrossSales, productGradeData, grossSalesRankData] =
            await Promise.all(getMetadataPromises);

        // console.dir(grossSalesRankData, { depth: Infinity });

        return {
            ...defaultMetadata,
            ...((inventoryData as object) ?? {}),
            totalStock,
            totalGrossSales,
            productGradeData,
            grossSalesRankData,
        };
    }

    private async buildSalesByProductSearch(input: Record<string, any>, options?: RepositoryOptions) {
        const tenantId = options?.tenantId ?? '';
        const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);
        let searchedHKeys: string[] = [];
        if (input.search) {
            const searchQuery = SalesByVariantRequestBodySearch.buildQuery({
                ...input,
                isSearchingProduct: true,
                fields: ['product_h_key'],
            });

            const searchResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: searchQuery.toJSON(),
                },
            });
            searchedHKeys = searchResponse.body?.aggregations?.items?.buckets?.map(
                (record: Record<string, any>) => record.key,
            );

            if (searchedHKeys.length) {
                searchedHKeys = [...new Set(searchedHKeys)];
            }
        }
        return searchedHKeys;
    }

    @CacheDAO()
    async getSalesByProduct(
        input: {
            offset?: number;
            limit?: number;
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
            metadata?: Record<string, any>;
            channelId?: string;
        },
        options?: RepositoryOptions,
    ): Promise<{
        totalCount: number;
        items: Partial<SalesByProductDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const queryRequiredFields = DaoUtitls.getRequiredFields(input, SalesByProductFieldSchema);

            const metadata = await this.getMetadata({
                input,
                tenantId,
                index,
                requiredFields: queryRequiredFields,
            });
            // Metadata filter step
            const fiterForIdsQuery: Record<string, any> = {};
            const metadataFilter: Record<string, any> = {
                type: {
                    eq: 'configuration',
                },
            };

            // search
            const searchedHKeys = await this.buildSalesByProductSearch(input, options);

            Object.entries(input.filter ?? {}).map(([key, value]) => {
                const [firstFilterModel] = filterFactory.create({ [key]: value }, SalesByProductFieldSchema);
                if (firstFilterModel instanceof MetadataFilter) metadataFilter[key] = value;
                else fiterForIdsQuery[key] = value;
            });
            const filterByMetadataQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    filter: metadataFilter,
                    fields: ['h_key'],
                },
                SalesByProductFieldSchema,
            );
            const filterByMetadataQuery = SalesByProductRequestBodySearch.buildQuery({
                filter: metadataFilter,
                fields: filterByMetadataQueryRequiredFields,
                metadata,
                ids: searchedHKeys.length ? searchedHKeys : undefined,
            });

            const filterByMetadataQueryResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: filterByMetadataQuery.toJSON(),
                },
            });

            if (filterByMetadataQueryResponse.body.aggregations.items.buckets.length !== 0) {
                fiterForIdsQuery.product_h_key = {
                    in: filterByMetadataQueryResponse.body.aggregations.items.buckets.map(
                        (bucket: any) => bucket.key,
                    ),
                };
            } else {
                return {
                    totalCount: 0,
                    items: [],
                };
            }

            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: fiterForIdsQuery,
                    fields: ['product_h_key'],
                },
                SalesByProductFieldSchema,
            );

            // Get ids
            const idsQuery = SalesByProductRequestBodySearch.buildQuery({
                ...input,
                filter: fiterForIdsQuery,
                fields: idsQueryRequiredFields,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.items.buckets.key'],
                },
            });
            const idsRecords: SalesByProductRecord[] = idsResponse.body.aggregations.items.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return {
                    totalCount,
                    items: [],
                };
            }

            let ids = idsRecords.map((record) => record.key);

            const { offset, limit } = input;
            if (input.offset !== undefined && input.limit !== undefined)
                ids = ids.slice(offset, offset! + limit!);

            // Get all fields
            queryRequiredFields.push(...DefaultFields.relevanceScore.fields);

            //@ts-ignore
            delete input?.filter?.sku;
            //@ts-ignore
            delete input?.filter?.barcode;

            const query = SalesByProductRequestBodySearch.buildQuery({
                ...input,
                fields: queryRequiredFields,
                ids,
                defaultSort: DefaultFields.relevanceScore.sort,
                metadata,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const records: SalesByProductRecord[] = response.body.aggregations.items.buckets;
            const items = this.salesByProductRecordMapper.fromRecordsToDtos(records);

            return {
                totalCount,
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesSummary(
        input: {
            fromDate?: string;
            toDate?: string;
            forecastFromDate?: string;
            forecastToDate?: string;
            ids?: string[];
            fields?: string[];
            sort?: Record<string, SortType>[];
            search?: string;
            filter?: FilterInput;
            metadata?: Record<string, any>;
        },
        options?: RepositoryOptions,
    ): Promise<Partial<SalesByProductSummaryDTO>> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const queryRequiredFields = DaoUtitls.getRequiredFields(input, ProductSummaryFieldSchema);

            // Metadata filter step
            const fiterForIdsQuery: Record<string, any> = {};
            const metadataFilter: Record<string, any> = {
                type: {
                    eq: 'configuration',
                },
            };

            const emptyResponse = {
                total_count: 0,
                gross_sales: 0,
                gross_qty: 0,
                net_sales: 0,
                net_qty: 0,
                total_sales: 0,
                return_value: 0,
                return_qty: 0,
                discount: 0,
                tax: 0,
                shipping: 0,
                ad_spends: 0,
                clicks: 0,
                views: 0,
                stock: 0,
                on_order: 0,
                ats: 0,
            };

            // search
            const searchedHKeys = await this.buildSalesByProductSearch(input, options);
            if (input.search && searchedHKeys.length === 0) return emptyResponse;

            const metadata = await this.getMetadata({
                input,
                tenantId,
                index,
                requiredFields: queryRequiredFields,
            });

            Object.entries(input.filter ?? {}).map(([key, value]) => {
                const [firstFilterModel] = filterFactory.create({ [key]: value }, SalesByProductFieldSchema);
                if (firstFilterModel instanceof MetadataFilter) metadataFilter[key] = value;
                else fiterForIdsQuery[key] = value;
            });
            const filterByMetadataQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    filter: metadataFilter,
                    fields: ['h_key'],
                },
                SalesByProductFieldSchema,
            );
            const filterByMetadataQuery = SalesByProductRequestBodySearch.buildQuery({
                filter: metadataFilter,
                fields: filterByMetadataQueryRequiredFields,
                metadata,
                ids: searchedHKeys.length ? searchedHKeys : undefined,
            });

            const filterByMetadataQueryResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: filterByMetadataQuery.toJSON(),
                },
            });

            if (filterByMetadataQueryResponse.body.aggregations.items.buckets.length !== 0) {
                fiterForIdsQuery.product_h_key = {
                    in: filterByMetadataQueryResponse.body.aggregations.items.buckets.map(
                        (bucket: any) => bucket.key,
                    ),
                };
            } else {
                return emptyResponse;
            }

            const idsQueryRequiredFields = DaoUtitls.getRequiredFields(
                {
                    sort: input.sort,
                    filter: fiterForIdsQuery,
                    fields: ['product_h_key'],
                },
                SalesByProductFieldSchema,
            );

            // Get ids
            const idsQuery = SalesByProductRequestBodySearch.buildQuery({
                ...input,
                filter: fiterForIdsQuery,
                fields: idsQueryRequiredFields,
                metadata,
            });

            const idsResponse = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: idsQuery.toJSON(),
                    filter_path: ['aggregations.items.buckets.key'],
                },
            });
            const idsRecords: SalesByProductRecord[] = idsResponse.body.aggregations.items.buckets;
            const totalCount = idsRecords.length;

            if (totalCount === 0) {
                return emptyResponse;
            }

            const ids = idsRecords.map((record) => record.key);

            const query = ProductSummaryRequestBodySearch.buildQuery({
                ...input,
                ids,
                fields: queryRequiredFields,
                metadata,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            const summaryRecord: ProductSummaryRecord = response.body.aggregations.summary;

            const summary = this.summaryRecordMapper.fromRecordToDto(summaryRecord);

            return {
                total_count: totalCount,
                ...summary,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesBarChart(
        input: { hKey: string; fromDate: string; toDate: string; timeFrame: TimeFrame },
        options?: RepositoryOptions,
    ) {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const query = SalesBarChartRequestBodySearch.buildQuery(input);

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            return response.body;
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getSalesOverTimeChart(
        input: { fromDate: string; toDate: string; timeFrame: TimeFrame; fromSource?: string },
        options?: RepositoryOptions,
    ) {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const query = SalesOverTimeChartRequestBodySearch.buildQuery(input);

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            return response.body;
        } catch (error) {
            throw this.transformError(error);
        }
    }

    @CacheDAO()
    async getProductPerformanceChart(
        input: { fromDate: string; toDate: string; timeFrame: TimeFrame; fromSource?: string },
        options?: RepositoryOptions,
    ) {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);

            const query = SalesOverTimeChartRequestBodySearch.buildQuery(input);

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });

            return response.body;
        } catch (error) {
            throw this.transformError(error);
        }
    }
}
