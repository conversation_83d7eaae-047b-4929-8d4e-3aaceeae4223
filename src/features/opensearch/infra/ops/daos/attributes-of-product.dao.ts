import { RepositoryOptions, SortType } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { CacheDAO } from '@core';
import { AttributesOfProductDTO } from '@features/opensearch/domain';
import {
    AttributesOfProductFieldSchema,
    AttributesOfProductRecord,
    AttributesOfProductRecordMapper,
    AttributesOfProductRequestBodySearch,
    IAttributesOfProductRecordMapper,
    IndexKeys,
} from '@features/opensearch/infra';
import { Dao, Inject, Lifecycle, Logger } from '@heronjs/common';
import { OpensearchResponseUtil } from '@utils';
import { IOPSDataSource } from '../../../../../data-sources';
import { IInventoryServiceProvider, ItemTypes } from '../../providers';
import { DaoUtitls } from '../utils';

@Dao({ token: OPENSEARCH_INJECT_TOKENS.OPS_DAO.ATTRIBUTE, scope: Lifecycle.Singleton })
export class AttributesOfProductDAO {
    private readonly recordMapper: IAttributesOfProductRecordMapper = new AttributesOfProductRecordMapper();

    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE) private readonly osService: IOPSDataSource,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        private readonly inventoryService: IInventoryServiceProvider,
    ) {}

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);

        switch (err.constraint) {
            default:
                throw err;
        }
    }

    @CacheDAO()
    async find(
        input: {
            fromDate?: string;
            toDate?: string;
            productHKey: string;
            attribute: string;
            fields?: string[];
            sort?: Record<string, SortType>[];
            metadata?: Record<string, any>;
        },
        options?: RepositoryOptions,
    ): Promise<{
        items: Partial<AttributesOfProductDTO>[];
    }> {
        try {
            const tenantId = options?.tenantId ?? '';
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Variant);
            input.metadata = input.metadata || {
                bKeyToOnOrder: {},
                bKeyToDayOfLeadTimes: {},
            };

            // Get all fields
            const queryRequiredFields = DaoUtitls.getRequiredFields(input, AttributesOfProductFieldSchema);
            let maxStockDate;
            if (queryRequiredFields.includes('stock')) {
                maxStockDate = await OpensearchResponseUtil.getMaxStockDate(
                    this.osService,
                    tenantId,
                    index,
                    input,
                );
            }
            const metadata =
                (await this.inventoryService.getInventoryApiData(
                    { tenantId, type: ItemTypes.Variant },
                    queryRequiredFields,
                )) || {};

            const query = AttributesOfProductRequestBodySearch.buildQuery({
                ...input,
                metadata,
                fields: queryRequiredFields,
                maxStockDate,
            });

            const response = await this.osService.search({
                tenantId,
                params: {
                    index: index,
                    body: query.toJSON(),
                },
            });
            const records: AttributesOfProductRecord[] = response.body.aggregations.items.buckets;
            if (input.fields?.includes('stock_percentage')) {
                const totalStock = await OpensearchResponseUtil.getTotalStock(
                    this.osService,
                    tenantId,
                    index,
                );
                records.forEach((record) => {
                    record.stock_percentage = Number(((record.stock.value / totalStock) * 100).toFixed(2));
                });
            }

            if (input.fields?.includes('gross_sales_percentage')) {
                const totalGrossSales = await OpensearchResponseUtil.getTotalGrossSales(
                    this.osService,
                    tenantId,
                    index,
                    {
                        ...input,
                    },
                );

                records.forEach((record) => {
                    record.gross_sales_percentage = Number(
                        ((record.gross_sales.value / totalGrossSales) * 100).toFixed(2),
                    );
                });
            }
            const items = this.recordMapper.fromRecordsToDtos(records);

            return {
                items,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }
}
