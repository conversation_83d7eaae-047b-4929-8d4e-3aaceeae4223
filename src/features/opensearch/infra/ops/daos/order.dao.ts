import { FilterInput, RepositoryOptions, SortType } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { CacheDAO, SemanticSearch } from '@core';
import {
    GetOrdersRequestBodySearch,
    IndexKeys,
    IOrderRecordMapper,
    OrderRecord,
    OrderRecordMapper,
    VariantDAO,
} from '@features/opensearch';
import { OrderAggregationDTO, OrdersQueryOutputDTO } from '@features/opensearch/domain';
import { Dao, Inject, Lifecycle, Logger } from '@heronjs/common';
import { IOPSDataSource } from '../../../../../data-sources';
import { DaoUtitls, removeUndefinedFields } from '../utils';

@Dao({ token: OPENSEARCH_INJECT_TOKENS.OPS_DAO.ORDER, scope: Lifecycle.Singleton })
export class OrderDAO {
    private readonly orderRecordMapper: IOrderRecordMapper = new OrderRecordMapper();

    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE)
        private readonly osDataSource: IOPSDataSource,
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        private readonly variantDao: VariantDAO,
    ) {}

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);

        switch (err.constraint) {
            default:
                throw err;
        }
    }

    @CacheDAO()
    async getOrders(
        input: {
            offset?: number;
            limit?: number;
            fields?: string[];
            sort?: Record<string, SortType>[];
            filter?: FilterInput;
            semanticSearch?: SemanticSearch;
            groupBy?: string;
        },
        options?: RepositoryOptions,
    ): Promise<OrdersQueryOutputDTO> {
        try {
            const tenantId = options?.tenantId || '';
            const client = this.osDataSource.getClient();
            const index = DaoUtitls.getIndexName(tenantId, IndexKeys.Order);

            // Get variant h_keys
            let variantHKeys: string[] = [];
            let variantFilter: FilterInput = {
                h_key: input.filter?.h_item_key,
                name: input.filter?.variant_name,
                sku: input.filter?.variant_sku,
                barcode: input.filter?.variant_barcode,
                categories_h_key: input.filter?.variant_categories_h_key,
                collections_h_key: input.filter?.variant_collections_h_key,
                categories_name: input.filter?.variant_categories_name,
                collections_name: input.filter?.variant_collections_name,
            };
            variantFilter = removeUndefinedFields(variantFilter);

            if (Object.keys(variantFilter).length > 0) {
                const { items } = await this.variantDao.getSalesByVariant(
                    {
                        fields: ['h_key'],
                        filter: variantFilter,
                    },
                    {
                        tenantId,
                    },
                );
                variantHKeys = items.map((item) => item.h_key!);
                if (variantHKeys.length === 0) {
                    return {
                        total_count: 0,
                        total_gross_sales: 0,
                        total_gross_qty: 0,
                        total_net_sales: 0,
                        total_net_qty: 0,
                        total_total_sales: 0,
                        total_tax: 0,
                        total_discount: 0,
                        total_return_qty: 0,
                        total_return_value: 0,
                        total_gross_profit: 0,
                        total_order_count: 0,
                        total_return_order_count: 0,
                        items: [],
                        aggs: [],
                    };
                }
            }

            // Add variant h_keys to filter
            input.filter = {
                ...input.filter,
                h_item_key: variantHKeys.length > 0 ? { in: variantHKeys } : undefined,
            } as any;

            const query = GetOrdersRequestBodySearch.buildQuery(input);

            const jsonQuery: any = query.toJSON();

            const response = await this.osDataSource.search({
                tenantId,
                params: {
                    index: index,
                    body: jsonQuery,
                },
            });
            const records: OrderRecord[] = response.body.hits.hits;
            const totalCount: number = response.body.hits.total.value;
            const orders = this.orderRecordMapper.fromRecordsToDtos(records);

            // Extract aggregation results
            let totalGrossSales = 0;
            let totalGrossQty = 0;
            let totalNetSales = 0;
            let totalNetQty = 0;
            let totalTotalSales = 0;
            let totalTax = 0;
            let totalDiscount = 0;
            let totalReturnQty = 0;
            let totalReturnValue = 0;
            let totalGrossProfit = 0;
            let totalOrderCount = 0;
            let totalReturnOrderCount = 0;

            if (response.body.aggregations?.filtered_orders?.order_metrics) {
                const metrics = response.body.aggregations.filtered_orders.order_metrics;
                totalGrossSales =
                    metrics.total_gross_sales?.value !== undefined
                        ? Math.round(metrics.total_gross_sales.value * 100) / 100
                        : 0;
                totalGrossQty =
                    metrics.total_gross_qty?.value !== undefined
                        ? Math.round(metrics.total_gross_qty.value * 100) / 100
                        : 0;
                totalNetSales =
                    metrics.total_net_sales?.value !== undefined
                        ? Math.round(metrics.total_net_sales.value * 100) / 100
                        : 0;
                totalNetQty =
                    metrics.total_net_qty?.value !== undefined
                        ? Math.round(metrics.total_net_qty.value * 100) / 100
                        : 0;
                totalTotalSales =
                    metrics.total_total_sales?.value !== undefined
                        ? Math.round(metrics.total_total_sales.value * 100) / 100
                        : 0;
                totalTax =
                    metrics.total_tax?.value !== undefined
                        ? Math.round(metrics.total_tax.value * 100) / 100
                        : 0;
                totalDiscount =
                    metrics.total_discount?.value !== undefined
                        ? Math.round(metrics.total_discount.value * 100) / 100
                        : 0;
                totalReturnQty =
                    metrics.total_return_qty?.value !== undefined
                        ? Math.round(metrics.total_return_qty.value * 100) / 100
                        : 0;
                totalReturnValue =
                    metrics.total_return_value?.value !== undefined
                        ? Math.round(metrics.total_return_value.value * 100) / 100
                        : 0;
                totalGrossProfit =
                    metrics.total_gross_profit?.value !== undefined
                        ? Math.round(metrics.total_gross_profit.value * 100) / 100
                        : 0;
                totalOrderCount =
                    metrics.buy_orders?.total_order_count?.value !== undefined
                        ? metrics.buy_orders.total_order_count.value
                        : 0;
                totalReturnOrderCount =
                    metrics.return_orders?.total_return_order_count?.value !== undefined
                        ? metrics.return_orders.total_return_order_count.value
                        : 0;
            }

            let aggs: Partial<OrderAggregationDTO>[] = [];
            if (response.body.aggregations?.filtered_orders?.order_group_by) {
                const aggData = response.body.aggregations.filtered_orders.order_group_by;

                aggs = aggData.buckets.map((agg: any) => {
                    return {
                        key: agg.key_as_string,
                        gross_sales:
                            agg.total_gross_sales?.value !== undefined
                                ? Math.round(agg.total_gross_sales.value * 100) / 100
                                : 0,
                        gross_qty: agg.total_gross_qty?.value,
                        net_sales:
                            agg.total_net_sales?.value !== undefined
                                ? Math.round(agg.total_net_sales.value * 100) / 100
                                : 0,
                        net_qty: agg.total_net_qty?.value,
                        total_sales:
                            agg.total_total_sales?.value !== undefined
                                ? Math.round(agg.total_total_sales.value * 100) / 100
                                : 0,
                        tax:
                            agg.total_tax?.value !== undefined
                                ? Math.round(agg.total_tax.value * 100) / 100
                                : 0,
                        discount:
                            agg.total_discount?.value !== undefined
                                ? Math.round(agg.total_discount.value * 100) / 100
                                : 0,
                        return_qty: agg.total_return_qty?.value,
                        return_value:
                            agg.total_return_value?.value !== undefined
                                ? Math.round(agg.total_return_value.value * 100) / 100
                                : 0,
                        gross_profit:
                            agg.total_gross_profit?.value !== undefined
                                ? Math.round(agg.total_gross_profit.value * 100) / 100
                                : 0,
                    };
                });
            }

            return {
                total_count: totalCount,
                total_gross_sales: totalGrossSales,
                total_gross_qty: totalGrossQty,
                total_net_sales: totalNetSales,
                total_net_qty: totalNetQty,
                total_total_sales: totalTotalSales,
                total_tax: totalTax,
                total_discount: totalDiscount,
                total_return_qty: totalReturnQty,
                total_return_value: totalReturnValue,
                total_gross_profit: totalGrossProfit,
                total_order_count: totalOrderCount,
                total_return_order_count: totalReturnOrderCount,
                items: orders,
                aggs,
            };
        } catch (error) {
            throw this.transformError(error);
        }
    }
}
