import {
    addMetadataSortToQuery,
    AggregationItem,
    buildAggregation,
    buildQueryAndFilterAgg,
    FieldSchema,
    QueryOptions,
    RequestBodySearchItem,
} from '@core';
import { OrderDTO } from '@features/opensearch/domain';
import esb, {
    BoolQuery,
    dateHistogramAggregation,
    filterAggregation,
    matchAllQuery,
    rangeQuery,
    requestBodySearch,
    scriptedMetricAggregation,
    sumAggregation,
} from 'elastic-builder';
import {
    ChannelIdFilter,
    FromSourceFilter,
    OrderDiscountFilter,
    OrderGrossProfitFilter,
    OrderGrossQtyFilter,
    OrderGrossSalesFilter,
    OrderHItemKeyFilter,
    OrderIdFilter,
    OrderNetQtyFilter,
    OrderNetSalesFilter,
    OrderReturnQtyFilter,
    OrderReturnValueFilter,
    OrderShippingFilter,
    OrderTaxFilter,
    OrderTotalSalesFilter,
    OrderUniqueItemCountFilter,
} from '../filters';

export type OrderFields = keyof OrderDTO | 'h_item_key';

export const OrderFieldSchema: FieldSchema<OrderFields> = {
    order_id: {
        key: 'order_id',
        filterable: true,
        filterClass: OrderIdFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    channel_id: {
        key: 'channel_id',
        filterable: true,
        filterClass: ChannelIdFilter,
        sortable: false,
    },
    date: {
        key: 'date',
        filterable: false,
        sortable: true,
    },
    total_sales: {
        key: 'total_sales',
        filterable: true,
        filterClass: OrderTotalSalesFilter,
        sortable: true,
    },
    net_sales: {
        key: 'net_sales',
        filterable: true,
        filterClass: OrderNetSalesFilter,
        sortable: true,
    },
    gross_sales: {
        key: 'gross_sales',
        filterable: true,
        filterClass: OrderGrossSalesFilter,
        sortable: true,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: true,
        filterClass: OrderGrossQtyFilter,
        sortable: true,
    },
    net_qty: {
        key: 'net_qty',
        filterable: true,
        filterClass: OrderNetQtyFilter,
        sortable: true,
    },
    return_qty: {
        key: 'return_qty',
        filterable: true,
        filterClass: OrderReturnQtyFilter,
        sortable: true,
    },
    return_value: {
        key: 'return_value',
        filterable: true,
        filterClass: OrderReturnValueFilter,
        sortable: true,
    },
    discount: {
        key: 'discount',
        filterable: true,
        filterClass: OrderDiscountFilter,
        sortable: true,
    },
    tax: {
        key: 'tax',
        filterable: true,
        filterClass: OrderTaxFilter,
        sortable: true,
    },
    shipping: {
        key: 'shipping',
        filterable: true,
        filterClass: OrderShippingFilter,
        sortable: true,
    },
    unique_item_count: {
        key: 'unique_item_count',
        filterable: true,
        filterClass: OrderUniqueItemCountFilter,
        sortable: true,
    },
    gross_profit: {
        key: 'gross_profit',
        filterable: true,
        filterClass: OrderGrossProfitFilter,
        sortable: true,
    },
    order_items: {
        key: 'order_items',
        filterable: false,
        sortable: false,
    },
    h_item_key: {
        key: 'h_item_key',
        filterable: true,
        filterClass: OrderHItemKeyFilter,
        sortable: false,
    },
};

// Order Summary Aggregation for summary calculations
export const OrderSummaryAggregation: AggregationItem = {
    key: 'order_metrics',
    children: [
        {
            key: 'total_gross_sales',
            buildQuery: () => sumAggregation('total_gross_sales', 'gross_sales').missing('0'),
        },
        {
            key: 'total_gross_qty',
            buildQuery: () => sumAggregation('total_gross_qty', 'gross_qty').missing('0'),
        },
        {
            key: 'total_net_sales',
            buildQuery: () => sumAggregation('total_net_sales', 'net_sales').missing('0'),
        },
        {
            key: 'total_net_qty',
            buildQuery: () => sumAggregation('total_net_qty', 'net_qty').missing('0'),
        },
        {
            key: 'total_total_sales',
            buildQuery: () => sumAggregation('total_total_sales', 'total_sales').missing('0'),
        },
        {
            key: 'total_tax',
            buildQuery: () => sumAggregation('total_tax', 'tax').missing('0'),
        },
        {
            key: 'total_discount',
            buildQuery: () => sumAggregation('total_discount', 'discount').missing('0'),
        },
        {
            key: 'total_return_qty',
            buildQuery: () => sumAggregation('total_return_qty', 'return_qty').missing('0'),
        },
        {
            key: 'total_return_value',
            buildQuery: () => sumAggregation('total_return_value', 'return_value').missing('0'),
        },
        {
            key: 'total_gross_profit',
            buildQuery: () => sumAggregation('total_gross_profit', 'gross_profit').missing('0'),
        },
        {
            key: 'total_order_count',
            buildQuery: () =>
                filterAggregation('buy_orders', rangeQuery('net_qty').gte(0)).agg(
                    scriptedMetricAggregation('total_order_count')
                        .initScript('state.orders = new HashSet()')
                        .mapScript(
                            `
                            if (doc['order_id'].size() > 0) {
                                state.orders.add(doc['order_id'].value);
                            }
                        `,
                        )
                        .combineScript('return state.orders').reduceScript(`
                            Set allOrders = new HashSet();
                            for (orderSet in states) {
                                allOrders.addAll(orderSet);
                            }
                            return allOrders.size();
                        `),
                ),
        },
        {
            key: 'total_return_order_count',
            buildQuery: () =>
                filterAggregation('return_orders', rangeQuery('net_qty').lt(0)).agg(
                    scriptedMetricAggregation('total_return_order_count')
                        .initScript('state.orders = new HashSet()')
                        .mapScript(
                            `
                            if (doc['order_id'].size() > 0) {
                                state.orders.add(doc['order_id'].value);
                            }
                        `,
                        )
                        .combineScript('return state.orders').reduceScript(`
                            Set allOrders = new HashSet();
                            for (orderSet in states) {
                                allOrders.addAll(orderSet);
                            }
                            return allOrders.size();
                        `),
                ),
        },
    ],
    buildQuery: (options, children) => {
        return buildAggregation({
            aggregation: filterAggregation('order_metrics', matchAllQuery()),
            options,
            children,
        });
    },
};

// Order Group By Aggregation for group by calculations
export const OrderGroupByAggregation: AggregationItem = {
    key: 'order_group_by',
    children: [
        {
            key: 'gross_sales',
            buildQuery: () => sumAggregation('total_gross_sales', 'gross_sales').missing('0'),
        },
        {
            key: 'gross_qty',
            buildQuery: () => sumAggregation('total_gross_qty', 'gross_qty').missing('0'),
        },
        {
            key: 'net_sales',
            buildQuery: () => sumAggregation('total_net_sales', 'net_sales').missing('0'),
        },
        {
            key: 'net_qty',
            buildQuery: () => sumAggregation('total_net_qty', 'net_qty').missing('0'),
        },
        {
            key: 'total_sales',
            buildQuery: () => sumAggregation('total_total_sales', 'total_sales').missing('0'),
        },
        {
            key: 'tax',
            buildQuery: () => sumAggregation('total_tax', 'tax').missing('0'),
        },
        {
            key: 'discount',
            buildQuery: () => sumAggregation('total_discount', 'discount').missing('0'),
        },
        {
            key: 'return_qty',
            buildQuery: () => sumAggregation('total_return_qty', 'return_qty').missing('0'),
        },
        {
            key: 'return_value',
            buildQuery: () => sumAggregation('total_return_value', 'return_value').missing('0'),
        },
        {
            key: 'gross_profit',
            buildQuery: () => sumAggregation('total_gross_profit', 'gross_profit').missing('0'),
        },
        {
            key: 'order_count',
            buildQuery: () =>
                filterAggregation('buy_orders', rangeQuery('net_qty').gte(0)).agg(
                    scriptedMetricAggregation('total_order_count')
                        .initScript('state.orders = new HashSet()')
                        .mapScript(
                            `
                            if (doc['order_id'].size() > 0) {
                                state.orders.add(doc['order_id'].value);
                            }
                        `,
                        )
                        .combineScript('return state.orders').reduceScript(`
                            Set allOrders = new HashSet();
                            for (orderSet in states) {
                                allOrders.addAll(orderSet);
                            }
                            return allOrders.size();
                        `),
                ),
        },
        {
            key: 'return_order_count',
            buildQuery: () =>
                filterAggregation('return_orders', rangeQuery('net_qty').lt(0)).agg(
                    scriptedMetricAggregation('total_return_order_count')
                        .initScript('state.orders = new HashSet()')
                        .mapScript(
                            `
                            if (doc['order_id'].size() > 0) {
                                state.orders.add(doc['order_id'].value);
                            }
                        `,
                        )
                        .combineScript('return state.orders').reduceScript(`
                            Set allOrders = new HashSet();
                            for (orderSet in states) {
                                allOrders.addAll(orderSet);
                            }
                            return allOrders.size();
                        `),
                ),
        },
    ],
    buildQuery: (options, children) => {
        const { groupBy } = options;
        const dateFormat = 'yyyy-MM-dd';
        return buildAggregation({
            aggregation: dateHistogramAggregation('order_group_by', 'date')
                .calendarInterval(groupBy)
                .format(dateFormat),
            options,
            children,
        });
    },
};

export const GetOrdersRequestBodySearch: RequestBodySearchItem = {
    key: 'orders',
    buildQuery: (options: QueryOptions) => {
        const { fields, filter, offset, limit, fromDate, toDate, sort } = options;

        let filterQuery: BoolQuery = esb.boolQuery();
        if (filter) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                fieldSchema: OrderFieldSchema,
            });
            filterQuery = queyAndFilterData.filterQuery;
        }

        if (fromDate && toDate) {
            filterQuery.must([rangeQuery('date').gte(fromDate).lte(toDate)]);
        }

        const query = requestBodySearch().size(limit).from(offset).trackTotalHits(true);

        if (filterQuery) query.query(filterQuery);

        query.source(fields ?? []);

        const aggregation = buildAggregation({
            aggregation: filterAggregation('filtered_orders', filterQuery || matchAllQuery()),
            options,
            children: [OrderSummaryAggregation, OrderGroupByAggregation],
        });
        query.agg(aggregation);

        if (sort) addMetadataSortToQuery({ query, sort, fieldSchema: OrderFieldSchema });

        return query;
    },
};
