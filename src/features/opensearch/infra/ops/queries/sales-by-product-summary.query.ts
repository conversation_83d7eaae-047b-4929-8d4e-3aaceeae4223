import { buildAggregation, FieldSchema, RequestBodySearchItem } from '@core';
import { SalesByProductSummaryDTO as ProductSummaryDto } from '@features/opensearch/domain';
import {
    filterAggregation,
    matchAllQuery,
    requestBodySearch,
    RequestBodySearch,
    script,
    sumAggregation,
    termsQuery,
} from 'elastic-builder';
import { SalesByProductFieldSchema } from './sales-by-product.query';
import { AdsMetricsAggregation, SalesMetricsAggregation } from './sales-by-variant.query';

export type ProductSummaryFields = keyof ProductSummaryDto;
export const ProductSummaryFieldSchema: FieldSchema<Exclude<ProductSummaryFields, 'total_count'>> = {
    gross_sales: {
        key: 'gross_sales',
        filterable: false,
        sortable: false,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: false,
        sortable: false,
    },
    net_sales: {
        key: 'net_sales',
        filterable: false,
        sortable: false,
    },
    net_qty: {
        key: 'net_qty',
        filterable: false,
        sortable: false,
    },
    total_sales: {
        key: 'total_sales',
        filterable: false,
        sortable: false,
    },
    return_value: {
        key: 'return_value',
        filterable: false,
        sortable: false,
    },
    return_qty: {
        key: 'return_qty',
        filterable: false,
        sortable: false,
    },
    return_rate: {
        key: 'return_rate',
        filterable: false,
        sortable: false,
        requiredKeys: ['gross_qty', 'return_qty'],
    },
    discount: {
        key: 'discount',
        filterable: false,
        sortable: false,
    },
    tax: {
        key: 'tax',
        filterable: false,
        sortable: false,
    },
    shipping: {
        key: 'shipping',
        filterable: false,
        sortable: false,
    },

    ad_spends: {
        key: 'ad_spends',
        filterable: false,
        sortable: false,
    },
    clicks: {
        key: 'clicks',
        filterable: false,
        sortable: false,
    },
    views: {
        key: 'views',
        filterable: false,
        sortable: false,
    },
    stock: {
        key: 'stock',
        filterable: false,
        sortable: false,
    },
    on_order: {
        key: 'on_order',
        filterable: false,
        sortable: false,
    },
    ats: {
        key: 'ats',
        filterable: false,
        sortable: false,
        requiredKeys: ['on_order', 'stock'],
    },
    total_inventory_value_stock: {
        key: 'total_inventory_value_stock',
        filterable: false,
        sortable: false,
        requiredKeys: ['stock'],
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: false,
        sortable: false,
        requiredKeys: ['ats'],
    },
    gross_profit: {
        key: 'gross_profit',
        filterable: false,
        sortable: false,
    },
};

export const ProductSummaryRequestBodySearch: RequestBodySearchItem = {
    key: 'product_summary',
    buildQuery: (options) => {
        const { ids } = options;
        const bKeyToOnOrder = options.metadata?.bKeyToOnOrder;

        const summaryAgg = buildAggregation({
            aggregation: filterAggregation('summary', matchAllQuery()),
            options,
            children: [
                SalesMetricsAggregation,
                AdsMetricsAggregation,
                {
                    key: SalesByProductFieldSchema.stock.key,
                    buildQuery: () =>
                        sumAggregation('stock').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return doc['stock'].size() == 0 ? 0 : doc['stock'].value;
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByProductFieldSchema.on_order.key,
                    buildQuery: () =>
                        sumAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'configuration') return 0;
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return 0;
                                `,
                            ).params({
                                bKeyToOnOrder,
                            }),
                        ),
                },
                {
                    key: SalesByProductFieldSchema.total_inventory_value_stock.key,
                    buildQuery: () =>
                        sumAggregation('total_inventory_value_stock').script(
                            script(
                                'source',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return (doc['price'].size() == 0 ? 0 : doc['price'].value) * (doc['stock'].size() == 0 ? 0 : doc['stock'].value)
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByProductFieldSchema.total_inventory_value_ats.key,
                    buildQuery: (options) => {
                        return sumAggregation('total_inventory_value_ats').script(
                            script(
                                'source',
                                `
                                if (doc['type'].value != 'simple') return 0;
                                Map bKeyToOnOrder = params.bKeyToOnOrder;
                                String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                long onOrder = 0;
                                if (bKeyToOnOrder.containsKey(docBKey))
                                    onOrder = bKeyToOnOrder.get(docBKey);
                                long stock = doc.containsKey('stock') && !doc['stock'].empty && doc['status'].value != 'deleted' ? doc['stock'].value : 0;
                                double ats = stock + onOrder;
                                double price = doc.containsKey('price') && !doc['price'].empty ? doc['price'].value : 0;
                                return ats*price;
                                `,
                            ).params({
                                bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                            }),
                        );
                    },
                },
            ],
        }) as any as RequestBodySearch;

        const query = requestBodySearch().size(0) as any;

        if (ids) query.query(termsQuery('product_h_key', ids));

        query.agg(summaryAgg);

        return query;
    },
};
