import { FilterInput } from '@cbidigital/aqua-ddd';
import {
    AggregationItem,
    BucketFilter,
    buildAggregation,
    MAX_BUCKET_SIZE,
    MetadataFilter,
    QueryOptions,
    RequestBodySearchItem,
} from '@core';
import { AnomalyVariantDTOFieldMap, KeyOfAnomalyVariantDTO } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import esb, {
    BoolQuery,
    boolQuery,
    BucketSelectorAggregation,
    BucketSortAggregation,
    bucketSortAggregation,
    existsQuery,
    filterAggregation,
    nestedAggregation,
    rangeQuery,
    requestBodySearch,
    sumAggregation,
    termsAggregation,
    termsQuery,
    TopHitsAggregation,
} from 'elastic-builder';
import { filterFactory } from '../filters';
import { SalesByVariantFieldSchema } from './sales-by-variant.query';

export type RequestBodySearchAnomalyItem = RequestBodySearchItem;

export type AnomalyMetricFilter = {
    origin: {
        from_date: string;
        to_date: string;
    };
    compare: {
        from_date: string;
        to_date: string;
    };
};

export const AnomalyVariantFieldPaths: Record<string, string> = {
    // metadata
    b_key: 'b_key',
    variant_id: 'variant_id',
    sku: 'sku',
    name: 'name',
    barcode: 'barcode',

    // sales_metrics
    sales_metrics: 'sales_metrics',
    sales_metrics_date: 'sales_metrics.date',
    gross_sales: 'sales_metrics.gross_sales',
    h_warehouse_key: 'sales_metrics.h_warehouse_key',

    // ads_metrics
    ads_metrics: 'ads_metrics',
    ads_metrics_date: 'ads_metrics.date',
    views: 'ads_metrics.views',
    ad_spends: 'ads_metrics.ad_spends',

    // sort_orders
    b_key_order: 'sort_orders.b_key',
    variant_id_order: 'sort_orders.variant_id',
    sku_order: 'sort_orders.sku',
    name_order: 'sort_orders.name',
    barcode_order: 'sort_orders.barcode',
};

export const AnomalyVariantSortKeys: Record<string, string> = {
    b_key: 'b_key_order',
    variant_id: 'variant_id_order',
    sku: 'sku_order',
    name: 'name_order',
    barcode: 'barcode_order',

    gross_sales: 'gross_sales',
    compared_gross_sales: 'compared_gross_sales',
    gross_sales_delta: 'gross_sales_delta',
    views: 'views',
    compared_views: 'compared_views',
    views_delta: 'views_delta',
    ad_spends: 'ad_spends',
    compared_ad_spends: 'compared_ad_spends',
    ad_spends_delta: 'ad_spends_delta',
};

export const AnomalyVariantSearchKeys = ['name', 'sku', 'barcode'];
export const AnomalyVariantMetadataKeys = [
    'h_key',
    'parent_h_key',
    'b_key',
    'variant_id',
    'sku',
    'type',
    'name',
    'barcode',
    'price',
    'status',
    'stock',
    'replenishable',
];

export const AnomalyVariantQueryKeys: Record<string, any> = {
    _key: 'variants',
    items: {
        _key: 'items',
        metadata: {
            _key: 'metadata',
        },
        sales_metrics: {
            _key: 'sales_metrics',
            nested: {
                _key: 'nested',
                gross_sales: {
                    _key: 'gross_sales',
                },
            },
        },
        ads_metrics: {
            _key: 'ads_metrics',
            nested: {
                _key: 'nested',
                views: {
                    _key: 'views',
                },
                ad_spends: {
                    _key: 'ad_spends',
                },
            },
        },

        // metadata order
        name_order: {
            _key: 'name_order',
        },
        sku_order: {
            _key: 'sku_order',
        },
    },
};

export const AnomalyVariantMetricTypes = {
    sales_metrics: ['gross_sales'],
    ads_metrics: ['views', 'ad_spends'],
};

export const AnomalyVariantRequestBodySearch: RequestBodySearchItem = {
    key: AnomalyVariantQueryKeys._key,
    buildQuery: (options) => {
        const { from, size, ids, sort, fields, filter, search } = options;
        const boolQueryPart: BoolQuery = boolQuery();
        let sortAgg: Optional<BucketSortAggregation>;
        let filterAgg: Optional<BucketSelectorAggregation>;
        let metadataAgg: Optional<TopHitsAggregation>;

        // search
        search && boolQueryPart.must(esb.multiMatchQuery(AnomalyVariantSearchKeys, search));

        // filter Agg
        if (filter) filterAgg = buildFilterQueryAgg(filter, boolQueryPart);

        // sortAgg
        // if (sort) sortAgg = buildSortAgg(sort, AnomalyVariantSortKeys);

        if (fields && ids) metadataAgg = buildMetadataAgg(fields);

        // Main query
        const query = requestBodySearch().size(0);

        // Build aggregation
        const anomalyMetricsAggs: AggregationItem[] = buildAnomalyMetricAggs(options.anomaly_filters);

        const itemsAgg = buildAggregation({
            aggregation: termsAggregation(AnomalyVariantQueryKeys.items._key, 'h_key').size(MAX_BUCKET_SIZE), // Set max to unlimit bucket to get
            options,
            children: [
                ...anomalyMetricsAggs,
                // sort_orders
                {
                    key: AnomalyVariantSortKeys.b_key,
                    buildQuery: () =>
                        new esb.MaxAggregation(AnomalyVariantSortKeys.b_key).field(
                            AnomalyVariantFieldPaths.b_key_order,
                        ),
                },
                {
                    key: AnomalyVariantSortKeys.variant_id,
                    buildQuery: () =>
                        new esb.MaxAggregation(AnomalyVariantSortKeys.variant_id).field(
                            AnomalyVariantFieldPaths.variant_id_order,
                        ),
                },
                {
                    key: AnomalyVariantSortKeys.name,
                    buildQuery: () =>
                        new esb.MaxAggregation(AnomalyVariantSortKeys.name).field(
                            AnomalyVariantFieldPaths.name_order,
                        ),
                },
                {
                    key: AnomalyVariantSortKeys.sku,
                    buildQuery: () =>
                        new esb.MaxAggregation(AnomalyVariantSortKeys.sku).field(
                            AnomalyVariantFieldPaths.sku_order,
                        ),
                },
                {
                    key: AnomalyVariantSortKeys.barcode,
                    buildQuery: () =>
                        new esb.MaxAggregation(AnomalyVariantSortKeys.barcode).field(
                            AnomalyVariantFieldPaths.barcode_order,
                        ),
                },
            ],
            filterAgg,
            sortAgg,
            metadataAgg,
        });

        query.query(boolQueryPart);

        // Paging
        let pagingResultsAgg;
        if (size !== undefined) {
            pagingResultsAgg = bucketSortAggregation('paging_results').size(size);
            if (from !== undefined) pagingResultsAgg.from(from);
        }
        if (pagingResultsAgg) itemsAgg.agg(pagingResultsAgg);

        query.agg(itemsAgg);

        if (ids) query.query(termsQuery('h_key', ids));

        return query;
    },
};

const buildMetadataAgg = (fields: string[]) => {
    const metadataKeys = fields.filter((field) => AnomalyVariantMetadataKeys.includes(field));
    if (metadataKeys.length) {
        return new esb.TopHitsAggregation(AnomalyVariantQueryKeys.items.metadata._key)
            .size(1)
            .source(metadataKeys);
    } else {
        return new esb.TopHitsAggregation(AnomalyVariantQueryKeys.items.metadata._key)
            .size(1)
            .source(AnomalyVariantMetadataKeys);
    }
};

const buildFilterQueryAgg = (filter: FilterInput, boolQueryPart: esb.BoolQuery) => {
    const filterModels = filterFactory.create(filter, SalesByVariantFieldSchema);
    let filterAgg: Optional<BucketSelectorAggregation>;
    const { bucketFilters, metadataFilters } = filterModels.reduce(
        (acc, filterModel) => {
            if (filterModel instanceof BucketFilter) {
                acc.bucketFilters.push(filterModel);
            } else {
                acc.metadataFilters.push(filterModel);
            }
            return acc;
        },
        { bucketFilters: [] as BucketFilter[], metadataFilters: [] as MetadataFilter[] },
    );

    metadataFilters.length && metadataFilters.forEach((filter) => filter.apply(boolQueryPart));

    if (bucketFilters.length) {
        const arr = bucketFilters.map((filter) => ({ [filter.compareField]: filter.compareField }));
        const script = bucketFilters.map((filter) => filter.apply()).join(' || ');
        const bucketsPath = arr.reduce((acc, curr) => {
            return { ...acc, ...curr };
        }, {});
        filterAgg = esb.bucketSelectorAggregation('filtered_results').bucketsPath(bucketsPath).script(script);
        return filterAgg;
    }
};

const buildAnomalyMetricAggs = (filters: Record<string, AnomalyMetricFilter>) => {
    const anomalyMetricsAggs: AggregationItem[] = [];
    for (const metric in filters) {
        if (AnomalyVariantMetricTypes.sales_metrics.includes(metric)) {
            anomalyMetricsAggs.push(
                getSalesMetricAgg(`${metric}_agg`, `${metric}`, {
                    fromDate: filters[metric].origin.from_date,
                    toDate: filters[metric].origin.to_date,
                }),
            );
            anomalyMetricsAggs.push(
                getSalesMetricAgg(`compared_${metric}_agg`, `${metric}`, {
                    fromDate: filters[metric].compare.from_date,
                    toDate: filters[metric].compare.to_date,
                }),
            );
        } else if (AnomalyVariantMetricTypes.ads_metrics.includes(metric)) {
            anomalyMetricsAggs.push(
                getAdsMetricAgg(`${metric}_agg`, `${metric}`, {
                    fromDate: filters[metric].origin.from_date,
                    toDate: filters[metric].origin.to_date,
                }),
            );
            anomalyMetricsAggs.push(
                getAdsMetricAgg(`compared_${metric}_agg`, `${metric}`, {
                    fromDate: filters[metric].compare.from_date,
                    toDate: filters[metric].compare.to_date,
                }),
            );
        }

        anomalyMetricsAggs.push({
            key: metric,
            buildQuery: () =>
                esb
                    .bucketScriptAggregation(metric)
                    .bucketsPath({
                        value: `${metric}_agg>${AnomalyVariantQueryKeys.items._key}>${metric}`,
                    })
                    .script('Math.round(params.value * 100) / 100.00'),
        });
        anomalyMetricsAggs.push({
            key: `compared_${metric}`,
            buildQuery: () =>
                esb
                    .bucketScriptAggregation(`compared_${metric}`)
                    .bucketsPath({
                        value: `compared_${metric}_agg>${AnomalyVariantQueryKeys.items._key}>${metric}`,
                    })
                    .script('Math.round(params.value * 100) / 100.00'),
        });
        anomalyMetricsAggs.push({
            key: `${metric}_delta`,
            buildQuery: () =>
                esb
                    .bucketScriptAggregation(`${metric}_delta`)
                    .bucketsPath({
                        metric_value: `${metric}_agg>${AnomalyVariantQueryKeys.items._key}>${metric}`,
                        compared_metric_value: `compared_${metric}_agg>${AnomalyVariantQueryKeys.items._key}>${metric}`,
                    })
                    .script(
                        'Math.round((params.metric_value - params.compared_metric_value) / params.metric_value * 100) / 1.00',
                    ),
        });
    }
    return anomalyMetricsAggs;
};

const getSalesMetricAgg = (key: string, metricName: string, options: QueryOptions) => {
    const res: AggregationItem = {
        key,
        children: [
            {
                key: AnomalyVariantDTOFieldMap[metricName as KeyOfAnomalyVariantDTO],
                buildQuery: () =>
                    sumAggregation(
                        AnomalyVariantQueryKeys.items.sales_metrics.nested[metricName]._key,
                        AnomalyVariantFieldPaths[metricName],
                    ).missing('0'),
            },
        ],
        options,
        buildQuery: (options, children) => {
            const { fromDate, toDate } = options;
            const filterQuery = boolQuery().mustNot([existsQuery(AnomalyVariantFieldPaths.h_warehouse_key)]);
            if (fromDate && toDate) {
                filterQuery.must([
                    rangeQuery(AnomalyVariantFieldPaths.sales_metrics_date).gte(fromDate).lte(toDate),
                ]);
            }
            const nestedQuery = buildAggregation({
                aggregation: filterAggregation('items', filterQuery),
                options,
                children,
            });

            const query = nestedAggregation(key, AnomalyVariantFieldPaths.sales_metrics).agg(nestedQuery);

            return query;
        },
    };
    return res;
};

const getAdsMetricAgg = (key: string, metricName: string, options: QueryOptions) => {
    const res: AggregationItem = {
        key,
        children: [
            {
                key: AnomalyVariantDTOFieldMap[metricName as KeyOfAnomalyVariantDTO],
                buildQuery: () =>
                    sumAggregation(
                        AnomalyVariantQueryKeys.items.ads_metrics.nested[metricName]._key,
                        AnomalyVariantFieldPaths[metricName],
                    ).missing('0'),
            },
        ],
        options,
        buildQuery: (options, children) => {
            const { fromDate, toDate } = options;

            const filterQuery = boolQuery();
            if (fromDate && toDate) {
                filterQuery.must([
                    rangeQuery(AnomalyVariantFieldPaths.ads_metrics_date).gte(fromDate).lte(toDate),
                ]);
            }

            const nestedQuery = buildAggregation({
                aggregation: filterAggregation('items', filterQuery),
                options,
                children,
            });

            const query = nestedAggregation(key, AnomalyVariantFieldPaths.ads_metrics).agg(nestedQuery);

            return query;
        },
    };
    return res;
};
