import { buildQueryAndFilterAgg, FieldSchema, QueryOptions, RequestBodySearchItem } from '@core';
import { CategoryDTO } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import { BoolQuery, requestBodySearch } from 'elastic-builder';
import { FromSourceFilter, HKeyFilter, NameFilter } from '../filters';

export type CategoryFields = keyof CategoryDTO;

export const CategoryFieldSchema: FieldSchema<CategoryFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    b_key: {
        key: 'b_key',
        filterable: false,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: NameFilter,
        sortable: false,
    },
};

export const GetCategoriesRequestBodySearch: RequestBodySearchItem = {
    key: 'collections',
    buildQuery: (options: QueryOptions) => {
        const { fields, filter, offset, limit } = options;

        let filterQuery: Optional<BoolQuery>;
        if (filter) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                fieldSchema: CategoryFieldSchema,
            });
            filterQuery = queyAndFilterData.filterQuery;
        }

        const query = requestBodySearch().size(limit).from(offset).trackTotalHits(true);
        if (filterQuery) {
            query.query(filterQuery);
        }

        query.source(fields ?? []);

        return query;
    },
};
