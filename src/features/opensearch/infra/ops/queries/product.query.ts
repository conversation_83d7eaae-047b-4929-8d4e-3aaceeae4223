import { buildQueryAnd<PERSON>ilterAgg, FieldSchema, QueryOptions, RequestBodySearchItem } from '@core';
import { VariantDTO } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import esb, { Bo<PERSON><PERSON>uery, requestBodySearch } from 'elastic-builder';
import {
    BarcodeFilter,
    CategoriesHKeyFilter,
    CollectionsHKeyFilter,
    ColorFilter,
    CompareAtPriceFilter,
    CostPerItemFilter,
    FromSourceFilter,
    HKeyFilter,
    NameFilter,
    PriceFilter,
    ProductIdFilter,
    SizeFilter,
    SKUFilter,
    StatusFilter,
    TypeFilter,
} from '../filters';

export type ProductFields =
    | keyof VariantDTO
    | 'b_key_order'
    | 'product_id_order'
    | 'name_order'
    | 'sku_order'
    | 'barcode_order'
    | 'color_order'
    | 'size_order'
    | 'categories_h_key'
    | 'collections_h_key'
    | 'published_at'
    | 'parent_h_key'
    | 'variant_id';

export const ProductFieldSchema: FieldSchema<ProductFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    b_key: {
        key: 'b_key',
        filterable: false,
        sortable: true,
        overrideSortKey: 'b_key_order',
    },
    product_id: {
        key: 'product_id',
        filterable: true,
        filterClass: ProductIdFilter,
        sortable: true,
        overrideSortKey: 'product_id_order',
    },
    type: {
        key: 'type',
        filterable: true,
        filterClass: TypeFilter,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: NameFilter,
        sortable: true,
        overrideSortKey: 'name_order',
    },
    sku: {
        key: 'sku',
        filterable: true,
        filterClass: SKUFilter,
        sortable: true,
        overrideSortKey: 'sku_order',
    },
    barcode: {
        key: 'barcode',
        filterable: true,
        filterClass: BarcodeFilter,
        sortable: true,
        overrideSortKey: 'barcode_order',
    },
    price: {
        key: 'price',
        filterable: true,
        filterClass: PriceFilter,
        sortable: true,
    },
    compare_at_price: {
        key: 'compare_at_price',
        filterable: true,
        filterClass: CompareAtPriceFilter,
        sortable: true,
    },
    image: {
        key: 'image',
        filterable: false,
        sortable: false,
    },
    color: {
        key: 'color',
        filterable: true,
        filterClass: ColorFilter,
        sortable: true,
        overrideSortKey: 'color_order',
    },
    default_color: {
        key: 'default_color',
        filterable: true,
        sortable: false,
    },
    size: {
        key: 'size',
        filterable: true,
        filterClass: SizeFilter,
        sortable: true,
        overrideSortKey: 'size_order',
    },
    status: {
        key: 'status',
        filterable: true,
        filterClass: StatusFilter,
        sortable: false,
    },
    categories: {
        key: 'categories',
        filterable: false,
        sortable: false,
    },
    collections: {
        key: 'collections',
        filterable: false,
        sortable: false,
    },
    tags: {
        key: 'tags',
        filterable: false,
        sortable: false,
    },
    vendors: {
        key: 'vendors',
        filterable: false,
        sortable: false,
    },
    cost_per_item: {
        key: 'cost_per_item',
        filterable: true,
        filterClass: CostPerItemFilter,
        sortable: true,
    },
    b_key_order: {
        key: 'b_key_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    product_id_order: {
        key: 'product_id_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    name_order: {
        key: 'name_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    sku_order: {
        key: 'sku_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    barcode_order: {
        key: 'barcode_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    color_order: {
        key: 'color_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    size_order: {
        key: 'size_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    categories_h_key: {
        key: 'categories_h_key',
        filterable: true,
        filterClass: CategoriesHKeyFilter,
        sortable: false,
        hidden: true,
    },
    collections_h_key: {
        key: 'collections_h_key',
        filterable: true,
        filterClass: CollectionsHKeyFilter,
        sortable: false,
        hidden: true,
    },
    published_at: {
        key: 'published_at',
        filterable: false,
        sortable: true,
    },
    parent_h_key: {
        key: 'parent_h_key',
        filterable: false,
        sortable: false,
    },
    variant_id: {
        key: 'variant_id',
        filterable: false,
        sortable: true,
    },
};

export const ProductRequestBodySearch: RequestBodySearchItem = {
    key: 'products',
    buildQuery: (options: QueryOptions) => {
        const { fields, filter, search, offset, limit } = options;

        let filterQuery: Optional<BoolQuery>;
        if (filter) {
            const queryAndFilterData = buildQueryAndFilterAgg({
                filter,
                search,
                fieldSchema: ProductFieldSchema,
            });
            filterQuery = queryAndFilterData.filterQuery;
        }

        const query = requestBodySearch().size(limit).from(offset);

        const typeTermQuery = esb.termQuery('type', 'configuration');
        if (filterQuery) {
            query.query(filterQuery);
            filterQuery.filter(typeTermQuery);
        } else {
            query.query(esb.boolQuery().must(typeTermQuery));
        }

        query.source(fields ?? []);

        return query;
    },
};
