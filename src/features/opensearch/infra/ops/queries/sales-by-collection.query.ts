import {
    AggregationItem,
    buildAggregation,
    buildQueryAndFilterAgg,
    buildSortAgg,
    FieldSchema,
    MAX_BUCKET_SIZE,
    RequestBodySearchItem,
} from '@core';
import { SalesByCollectionDto } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import { DSLUtil } from '@utils';
import esb, {
    boolQuery,
    BoolQuery,
    BucketSelectorAggregation,
    BucketSortAggregation,
    existsQuery,
    filterAggregation,
    nestedAggregation,
    nestedQuery,
    rangeQuery,
    requestBodySearch,
    reverseNestedAggregation,
    script,
    sumAggregation,
    termQuery,
    termsAggregation,
    termsQuery,
    TopHitsAggregation,
} from 'elastic-builder';
import {
    AdSpendsFilter,
    ATSFilter,
    ClicksFilter,
    CollectionHKeyFilter,
    CollectionNameFilter,
    filterFactory,
    GrossQuantityFilter,
    GrossSalesFilter,
    GrossSalesPercentageFilter,
    NetQuantityFilter,
    NetSalesFilter,
    OnOrderFilter,
    ReturnQuantityFilter,
    ReturnValueFilter,
    SellThroughFilter,
    StockFilter,
    StockPercentageFilter,
    TotalInventoryValueAtsFilter,
    TotalInventoryValueStockFilter,
    TotalSalesFilter,
    ViewsFilter,
} from '../filters';

export type SalesByCollectionFields = keyof SalesByCollectionDto | 'max_score';
export const SalesByCollectionFieldSchema: FieldSchema<SalesByCollectionFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: CollectionHKeyFilter,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: CollectionNameFilter,
        sortable: false,
    },
    stock: {
        key: 'stock',
        filterable: true,
        filterClass: StockFilter,
        sortable: true,
    },
    on_order: {
        key: 'on_order',
        filterable: true,
        filterClass: OnOrderFilter,
        sortable: true,
    },
    ats: {
        key: 'ats',
        filterable: true,
        filterClass: ATSFilter,
        sortable: true,
        requiredKeys: ['on_order', 'stock'],
    },
    total_inventory_value_stock: {
        key: 'total_inventory_value_stock',
        filterable: true,
        filterClass: TotalInventoryValueStockFilter,
        sortable: true,
    },

    // sales_metrics
    gross_sales: {
        key: 'gross_sales',
        filterable: true,
        filterClass: GrossSalesFilter,
        sortable: true,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: true,
        filterClass: GrossQuantityFilter,
        sortable: true,
    },
    net_sales: {
        key: 'net_sales',
        filterClass: NetSalesFilter,
        filterable: true,
        sortable: true,
    },
    net_qty: {
        key: 'net_qty',
        filterable: true,
        filterClass: NetQuantityFilter,
        sortable: true,
    },
    total_sales: {
        key: 'total_sales',
        filterable: true,
        filterClass: TotalSalesFilter,
        sortable: true,
    },
    return_value: {
        key: 'return_value',
        filterable: true,
        filterClass: ReturnValueFilter,
        sortable: true,
    },
    return_qty: {
        key: 'return_qty',
        filterable: true,
        filterClass: ReturnQuantityFilter,
        sortable: true,
    },
    sell_through: {
        key: 'sell_through',
        filterable: true,
        filterClass: SellThroughFilter,
        sortable: true,
        requiredKeys: ['ats', 'gross_qty'],
    },

    // ads_metrics
    ad_spends: {
        key: 'ad_spends',
        filterable: true,
        filterClass: AdSpendsFilter,
        sortable: true,
    },
    clicks: {
        key: 'clicks',
        filterable: true,
        filterClass: ClicksFilter,
        sortable: true,
    },
    views: {
        key: 'views',
        filterable: true,
        filterClass: ViewsFilter,
        sortable: true,
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: true,
        filterClass: TotalInventoryValueAtsFilter,
        sortable: true,
    },
    stock_percentage: {
        key: 'stock_percentage',
        filterable: true,
        filterClass: StockPercentageFilter,
        sortable: true,
        requiredKeys: ['stock'],
    },
    gross_sales_percentage: {
        key: 'gross_sales_percentage',
        filterable: true,
        filterClass: GrossSalesPercentageFilter,
        sortable: true,
        requiredKeys: ['gross_sales'],
    },
    max_score: {
        key: 'max_score',
        filterable: false,
        sortable: true,
    },
};

export const SalesByCollectionFieldPathMap = {
    h_key: 'collections.h_key',
    collections: 'collections',
    // metadata
    // name: 'name',

    // sales_metrics
    sales_metrics: 'sales_metrics',
    sales_metrics_date: 'sales_metrics.date',
    gross_sales: 'sales_metrics.gross_sales',
    gross_qty: 'sales_metrics.gross_qty',
    net_sales: 'sales_metrics.net_sales',
    net_qty: 'sales_metrics.net_qty',
    total_sales: 'sales_metrics.total_sales',
    return_qty: 'sales_metrics.return_qty',
    return_value: 'sales_metrics.return_value',
    h_warehouse_key: 'sales_metrics.h_warehouse_key',

    // ads metrics
    ads_metrics: 'ads_metrics',
    ads_metrics_date: 'ads_metrics.date',
    ad_spends: 'ads_metrics.ad_spends',
    clicks: 'ads_metrics.clicks',
    views: 'ads_metrics.views',

    stock: 'stock',
    on_order: 'on_order',
    ats: 'ats',
    total_inventory_value_stock: 'total_inventory_value_stock',
    total_inventory_value_ats: 'total_inventory_value_ats',

    // metadata sort_orders
    name_order: 'collections.sort_orders.name',
};

export const SalesByCollectionQueryKeyTree = {
    _key: 'collections',
    collections: {
        _key: 'collections',
        filtered_collections: {
            _key: 'filtered_collections',
            collections: {
                _key: 'collections',
                metadata: {
                    _key: 'metadata',
                },
                to_root: {
                    _key: 'to_root',
                    sales_metrics: {
                        _key: 'sales_metrics',
                        nested: {
                            _key: 'nested',
                            gross_sales: {
                                _key: 'gross_sales',
                            },
                            gross_qty: {
                                _key: 'gross_qty',
                            },
                            net_sales: {
                                _key: 'net_sales',
                            },
                            net_qty: {
                                _key: 'net_qty',
                            },
                            total_sales: {
                                _key: 'total_sales',
                            },
                            return_value: {
                                _key: 'return_value',
                            },
                            return_qty: {
                                _key: 'return_qty',
                            },
                        },
                    },
                    ads_metrics: {
                        _key: 'ads_metrics',
                        nested: {
                            _key: 'nested',
                            ad_spends: {
                                _key: 'ad_spends',
                            },
                            clicks: {
                                _key: 'clicks',
                            },
                            views: {
                                _key: 'views',
                            },
                        },
                    },
                    nested: {
                        _key: 'nested',
                        stock: {
                            _key: 'stock',
                        },
                        on_order: {
                            _key: 'on_order',
                        },
                    },
                    total_inventory_value_stock: {
                        _key: 'total_inventory_value_stock',
                    },
                    total_inventory_value_ats: {
                        _key: 'total_inventory_value_ats',
                    },
                },
                gross_sales: {
                    _key: 'gross_sales',
                },
                gross_qty: {
                    _key: 'gross_qty',
                },
                return_value: {
                    _key: 'return_value',
                },
                return_qty: {
                    _key: 'return_qty',
                },
                total_sales: {
                    _key: 'total_sales',
                },
                net_sales: {
                    _key: 'net_sales',
                },
                net_qty: {
                    _key: 'net_qty',
                },

                ad_spends: {
                    _key: 'ad_spends',
                },
                clicks: {
                    _key: 'clicks',
                },
                views: {
                    _key: 'views',
                },

                stock: {
                    _key: 'stock',
                },
                on_order: {
                    _key: 'on_order',
                },
                ats: {
                    _key: 'ats',
                },
                total_inventory_value_stock: {
                    _key: 'total_inventory_value_stock',
                },
                total_inventory_value_ats: {
                    _key: 'total_inventory_value_ats',
                },

                sell_through: {
                    _key: 'sell_through',
                },

                // sort order
                name_order: {
                    _key: 'name_order',
                },
            },
        },
    },
    items: {
        _key: 'items',
        sales_metrics: {
            _key: 'sales_metrics',
            nested: {
                _key: 'nested',
                gross_sales: {
                    _key: 'gross_sales',
                },
            },
        },
        stock: {
            _key: 'stock',
        },
    },
};

const SalesByCollectionsMetadataFields = ['collections.name'];
const SalesByCollectionSearchKeys = ['name'];

export const SalesByCollectionRequestBodySearch: RequestBodySearchItem = {
    key: SalesByCollectionQueryKeyTree._key,
    buildQuery: (options) => {
        const { ids, sort, filter, search, fields, defaultSort, metadata } = options;
        const { totalStock, totalGrossSales } = metadata ?? {};
        let sortAgg: Optional<BucketSortAggregation>;
        let filterAgg: Optional<BucketSelectorAggregation>;
        let filterQuery: Optional<BoolQuery>;
        let metadataAgg: Optional<TopHitsAggregation>;

        if (sort) {
            sortAgg = buildSortAgg({ sort, fieldSchema: SalesByCollectionFieldSchema, defaultSort });
        } else {
            if (defaultSort)
                sortAgg = buildSortAgg({ sort: defaultSort, fieldSchema: SalesByCollectionFieldSchema });
        }
        if (filter) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                search,
                fieldSchema: SalesByCollectionFieldSchema,
                searchFields: SalesByCollectionSearchKeys,
            });
            filterAgg = queyAndFilterData.filterAgg;
            filterQuery = queyAndFilterData.filterQuery;
        }

        if (fields && ids) metadataAgg = buildMetadataAgg(fields);

        const salesMetricsBucketPathPrefix = `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested._key}`;
        const adsMetricsBucketPathPrefix = `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics.nested._key}`;

        const collectionsAgg = buildAggregation({
            aggregation: termsAggregation(
                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections._key,
                'collections.h_key',
            ).size(MAX_BUCKET_SIZE), // Set max to unlimit bucket to get
            options,
            children: [
                CollectionMetadataAggregation,
                CollectionToRootAggregation,
                DSLUtil.MaxScoreAggregation,

                // sales_metrics
                {
                    key: SalesByCollectionFieldSchema.gross_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .gross_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.gross_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.gross_sales_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_sales_percentage')
                            .bucketsPath({
                                gross_sales: `gross_sales`,
                            })
                            .script(
                                `Math.round((params.gross_sales * 100 / ${totalGrossSales}) * 100) / 100.00`,
                            ),
                },
                {
                    key: SalesByCollectionFieldSchema.gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .gross_qty._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.gross_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.net_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .net_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.net_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.net_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .net_qty._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.net_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.total_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .total_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.total_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.return_value.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .return_value._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.return_value._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.return_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .return_qty._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics.nested.return_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },

                // ads_metrics
                {
                    key: SalesByCollectionFieldSchema.ad_spends.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .ad_spends._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics.nested.ad_spends._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.clicks.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .clicks._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics.nested.clicks._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.views.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .views._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics.nested.views._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .stock._key,
                            )
                            .bucketsPath({
                                value: `to_root>nested>stock`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByCollectionFieldSchema.stock_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('stock_percentage')
                            .bucketsPath({
                                stock: 'stock',
                            })
                            .script(`Math.round((params.stock * 100 / ${totalStock}) * 100) / 100.00`),
                },
                {
                    key: SalesByCollectionFieldSchema.on_order.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .on_order._key,
                            )
                            .bucketsPath({
                                value: `to_root>nested>on_order`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByCollectionFieldSchema.ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.ats
                                    ._key,
                            )
                            .bucketsPath({
                                stock: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.stock._key}`,
                                on_order: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.on_order._key}`,
                            })
                            .script('params.stock + params.on_order'),
                },
                {
                    key: SalesByCollectionFieldSchema.sell_through.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .sell_through._key,
                            )
                            .bucketsPath({
                                gross_qty: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.gross_qty._key}`,
                                stock: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.stock._key}`,
                            })
                            .script(
                                '(params.gross_qty + params.stock) != 0 ? Math.round((params.gross_qty / (params.gross_qty + params.stock)) * 10000) / 100.00 : 0',
                            ),
                },
                {
                    key: SalesByCollectionFieldSchema.total_inventory_value_stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .total_inventory_value_stock._key,
                            )
                            .bucketsPath({
                                value: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.total_inventory_value_stock._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCollectionFieldSchema.total_inventory_value_ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCollectionQueryKeyTree.collections.filtered_collections.collections
                                    .total_inventory_value_ats._key,
                            )
                            .bucketsPath({
                                value: `${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root._key}>${SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.total_inventory_value_ats._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
            ],
            filterAgg,
            sortAgg,
            metadataAgg,
        });

        const filteredCollectionsConditions = [];

        if (ids) filteredCollectionsConditions.push(termsQuery(SalesByCollectionFieldPathMap.h_key, ids));
        // if (Object.keys(filter ?? {}).includes('h_key')) {
        //     filteredCollectionsConditions.push(
        //         termsQuery(
        //             SalesByCollectionFieldPathMap.h_key,
        //             Object.values(filter?.h_key ?? '')[0] as string[],
        //         ),
        //     );
        // }
        const filteredCollectionsBoolQuery = boolQuery().must(filteredCollectionsConditions);

        if (filter) {
            const filterModels = filterFactory.create(filter, SalesByCollectionFieldSchema);
            filterModels.forEach((filterModel) => {
                switch (true) {
                    case filterModel instanceof CollectionNameFilter:
                    case filterModel instanceof CollectionHKeyFilter: {
                        filterModel.apply(filteredCollectionsBoolQuery, true);
                        break;
                    }
                }
            });
        }

        const filteredCollectionsAgg = filterAggregation(
            'filtered_collections',
            filteredCollectionsBoolQuery,
        ).agg(collectionsAgg);

        const query = requestBodySearch().size(0);

        if (filterQuery) query.query(filterQuery);

        if (ids)
            query.query(
                nestedQuery(
                    termsQuery(SalesByCollectionFieldPathMap.h_key, ids),
                    SalesByCollectionFieldPathMap.collections,
                ),
            );

        query.agg(
            nestedAggregation(
                SalesByCollectionQueryKeyTree.collections._key,
                SalesByCollectionFieldPathMap.collections,
            ).agg(filteredCollectionsAgg),
        );

        // query.agg(
        //     CollectionSalesMetricsByItemAggregation.buildQuery(
        //         options,
        //         CollectionSalesMetricsByItemAggregation.children,
        //     ),
        // );

        // query.agg(CollectionTotalStockAggregation.buildQuery(options, CollectionTotalStockAggregation.children));

        return query;
    },
};

const buildMetadataAgg = (fields: string[]) => {
    const metadataKeys = fields
        .map((field) => `collections.${field}`)
        .filter((field) => SalesByCollectionsMetadataFields.includes(field as SalesByCollectionFields));
    if (fields.length) {
        return new esb.TopHitsAggregation(
            SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.metadata._key,
        )
            .size(1)
            .source(metadataKeys);
    } else {
        return new esb.TopHitsAggregation(
            SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.metadata._key,
        )
            .size(1)
            .source(metadataKeys);
    }
};

const CollectionMetadataAggregation: AggregationItem = {
    key: 'metadata',
    buildQuery: () => new esb.TopHitsAggregation('metadata').size(1).source(['collections.name']),
};

export const CollectionSalesMetricsAggregation: AggregationItem = {
    key: SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics
        ._key,
    children: [
        {
            key: SalesByCollectionFieldSchema.gross_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.gross_sales._key,
                    SalesByCollectionFieldPathMap.gross_sales,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.gross_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.gross_qty._key,
                    SalesByCollectionFieldPathMap.gross_qty,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.net_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.net_sales._key,
                    SalesByCollectionFieldPathMap.net_sales,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.net_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.net_qty._key,
                    SalesByCollectionFieldPathMap.net_qty,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.total_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.total_sales._key,
                    SalesByCollectionFieldPathMap.total_sales,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.return_value.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.return_value._key,
                    SalesByCollectionFieldPathMap.return_value,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.return_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .sales_metrics.nested.return_qty._key,
                    SalesByCollectionFieldPathMap.return_qty,
                ).missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;
        const filterQuery = boolQuery().mustNot([existsQuery(SalesByCollectionFieldPathMap.h_warehouse_key)]);
        if (fromDate && toDate)
            filterQuery.must([
                rangeQuery(SalesByCollectionFieldPathMap.sales_metrics_date).gte(fromDate).lte(toDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation(
            SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.sales_metrics
                ._key,
            SalesByCollectionFieldPathMap.sales_metrics,
        ).agg(nestedQuery);

        return query;
    },
};

export const CollectionAdsMetricsAggregation: AggregationItem = {
    key: SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics._key,
    children: [
        {
            key: SalesByCollectionFieldSchema.ad_spends.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .ads_metrics.nested.ad_spends._key,
                    SalesByCollectionFieldPathMap.ad_spends,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.clicks.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .ads_metrics.nested.clicks._key,
                    SalesByCollectionFieldPathMap.clicks,
                ).missing('0'),
        },
        {
            key: SalesByCollectionFieldSchema.views.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .ads_metrics.nested.views._key,
                    SalesByCollectionFieldPathMap.views,
                ).missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery();
        if (fromDate && toDate)
            filterQuery.must([
                rangeQuery(SalesByCollectionFieldPathMap.ads_metrics_date).gte(fromDate).lte(toDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation(
            SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root.ads_metrics
                ._key,
            SalesByCollectionFieldPathMap.ads_metrics,
        ).agg(nestedQuery);

        return query;
    },
};

const CollectionToRootAggregation: AggregationItem = {
    key: SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root._key,
    children: [
        CollectionSalesMetricsAggregation,
        CollectionAdsMetricsAggregation,
        {
            key: 'filtered_product',
            buildQuery: (options, children) => {
                return buildAggregation({
                    aggregation: filterAggregation(
                        'nested',
                        boolQuery().must([termQuery('type').value('simple')]),
                    ),
                    options,
                    children,
                });
            },
            children: [
                {
                    key: SalesByCollectionFieldSchema.stock.key,
                    buildQuery: () =>
                        sumAggregation('stock').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return doc['stock'].size() == 0 ? 0 : doc['stock'].value;
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByCollectionFieldSchema.on_order.key,
                    buildQuery: (options) =>
                        sumAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple') return 0;
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return 0;
                                `,
                            ).params({
                                bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                            }),
                        ),
                },
            ],
        },
        {
            key: SalesByCollectionFieldSchema.total_inventory_value_stock.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .total_inventory_value_stock._key,
                ).script(
                    script(
                        'inline',
                        `
                            if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                            return (doc['price'].size() == 0 ? 0 : doc['price'].value) * (doc['stock'].size() == 0 ? 0 : doc['stock'].value)
                        `,
                    ),
                ),
        },
        {
            key: SalesByCollectionFieldSchema.total_inventory_value_ats.key,
            buildQuery: (options) =>
                sumAggregation(
                    SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                        .total_inventory_value_ats._key,
                ).script(
                    script(
                        'source',
                        `
                        if (doc['type'].value != 'simple') return 0;
                        Map bKeyToOnOrder = params.bKeyToOnOrder;
                        String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                        long onOrder = 0;
                        if (bKeyToOnOrder.containsKey(docBKey)) onOrder = bKeyToOnOrder.get(docBKey);
                        long stock = doc.containsKey('stock') && !doc['stock'].empty && doc['status'].value != 'deleted' ? doc['stock'].value : 0;
                        double ats = stock + onOrder;
                        double price = doc.containsKey('price') && !doc['price'].empty ? doc['price'].value : 0;
                        return ats*price;
                        `,
                    ).params({
                        bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                    }),
                ),
        },
    ],

    buildQuery: (options, children) => {
        const query = buildAggregation({
            aggregation: reverseNestedAggregation('to_root'),
            options,
            children,
        });
        return query;
    },
};
