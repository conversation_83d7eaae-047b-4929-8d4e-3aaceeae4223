import { buildQueryAnd<PERSON>ilterAgg, FieldSchema, QueryOptions, RequestBodySearchItem } from '@core';
import { VariantDTO } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import esb, { Bo<PERSON><PERSON>uery, requestBodySearch } from 'elastic-builder';
import {
    BarcodeFilter,
    CategoriesH<PERSON>eyFilter,
    CollectionsHKeyFilter,
    ColorFilter,
    CompareAtPriceFilter,
    CostPerItemFilter,
    FromSourceFilter,
    HKeyFilter,
    NameFilter,
    PriceFilter,
    ProductIdFilter,
    SizeFilter,
    SKUFilter,
    StatusFilter,
    TypeFilter,
    VariantIdFilter,
} from '../filters';

export type VariantFields =
    | keyof VariantDTO
    | 'b_key_order'
    | 'variant_id_order'
    | 'name_order'
    | 'sku_order'
    | 'barcode_order'
    | 'color_order'
    | 'size_order'
    | 'trend_order'
    | 'sale_pattern_order'
    | 'categories_h_key'
    | 'collections_h_key'
    | 'published_at';

export const VariantFieldSchema: FieldSchema<VariantFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKey<PERSON>ilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    parent_h_key: {
        key: 'parent_h_key',
        filterable: false,
        sortable: false,
    },
    b_key: {
        key: 'b_key',
        filterable: false,
        sortable: true,
        overrideSortKey: 'b_key_order',
    },
    product_id: {
        key: 'product_id',
        filterable: true,
        filterClass: ProductIdFilter,
        sortable: false,
    },
    variant_id: {
        key: 'variant_id',
        filterable: true,
        filterClass: VariantIdFilter,
        sortable: true,
        overrideSortKey: 'variant_id_order',
    },
    type: {
        key: 'type',
        filterable: true,
        filterClass: TypeFilter,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: NameFilter,
        sortable: true,
        overrideSortKey: 'name_order',
    },
    sku: {
        key: 'sku',
        filterable: true,
        filterClass: SKUFilter,
        sortable: true,
        overrideSortKey: 'sku_order',
    },
    barcode: {
        key: 'barcode',
        filterable: true,
        filterClass: BarcodeFilter,
        sortable: true,
        overrideSortKey: 'barcode_order',
    },
    price: {
        key: 'price',
        filterable: true,
        filterClass: PriceFilter,
        sortable: true,
    },
    compare_at_price: {
        key: 'compare_at_price',
        filterable: true,
        filterClass: CompareAtPriceFilter,
        sortable: true,
    },
    image: {
        key: 'image',
        filterable: false,
        sortable: false,
    },
    color: {
        key: 'color',
        filterable: true,
        filterClass: ColorFilter,
        sortable: true,
        overrideSortKey: 'color_order',
    },
    default_color: {
        key: 'default_color',
        filterable: true,
        sortable: false,
    },
    size: {
        key: 'size',
        filterable: true,
        filterClass: SizeFilter,
        sortable: true,
        overrideSortKey: 'size_order',
    },
    status: {
        key: 'status',
        filterable: true,
        filterClass: StatusFilter,
        sortable: false,
    },
    categories: {
        key: 'categories',
        filterable: false,
        sortable: false,
    },
    collections: {
        key: 'collections',
        filterable: false,
        sortable: false,
    },
    tags: {
        key: 'tags',
        filterable: false,
        sortable: false,
    },
    vendors: {
        key: 'vendors',
        filterable: false,
        sortable: false,
    },
    published_at: {
        key: 'published_at',
        filterable: false,
        sortable: false,
    },

    cost_per_item: {
        key: 'cost_per_item',
        filterable: true,
        filterClass: CostPerItemFilter,
        sortable: true,
    },
    b_key_order: {
        key: 'b_key_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    variant_id_order: {
        key: 'variant_id_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    name_order: {
        key: 'name_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    sku_order: {
        key: 'sku_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    barcode_order: {
        key: 'barcode_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    color_order: {
        key: 'color_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    size_order: {
        key: 'size_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    trend_order: {
        key: 'trend_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    sale_pattern_order: {
        key: 'sale_pattern_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    categories_h_key: {
        key: 'categories_h_key',
        filterable: true,
        filterClass: CategoriesHKeyFilter,
        sortable: false,
        hidden: true,
    },
    collections_h_key: {
        key: 'collections_h_key',
        filterable: true,
        filterClass: CollectionsHKeyFilter,
        sortable: false,
        hidden: true,
    },
};

export const GetVariantsRequestBodySearch: RequestBodySearchItem = {
    key: 'variants',
    buildQuery: (options: QueryOptions) => {
        const { fields, filter, search, offset, limit } = options;

        let filterQuery: Optional<BoolQuery>;
        if (filter) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                search,
                fieldSchema: VariantFieldSchema,
            });
            filterQuery = queyAndFilterData.filterQuery;
        }

        const query = requestBodySearch().size(limit).from(offset);
        const typeTermQuery = esb.termQuery('type', 'simple');
        if (filterQuery) {
            query.query(filterQuery);
            filterQuery.filter(typeTermQuery);
        } else {
            query.query(esb.boolQuery().must(typeTermQuery));
        }

        query.source(fields ?? []);

        return query;
    },
};
