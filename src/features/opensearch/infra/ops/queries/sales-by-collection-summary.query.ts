import { buildAggregation, FieldSchema, RequestBodySearchItem } from '@core';
import { SalesByCollectionSummaryDTO as CollectionSummaryDto } from '@features/opensearch/domain';
import {
    filterAggregation,
    matchAllQuery,
    nestedQuery,
    requestBodySearch,
    RequestBodySearch,
    script,
    sumAggregation,
    termsQuery,
} from 'elastic-builder';
import {
    CollectionAdsMetricsAggregation,
    CollectionSalesMetricsAggregation,
    SalesByCollectionFieldPathMap,
    SalesByCollectionFieldSchema,
    SalesByCollectionQueryKeyTree,
} from './sales-by-collection.query';

export type CollectionSummaryFields = keyof CollectionSummaryDto;
export const CollectionSummaryFieldSchema: FieldSchema<Exclude<CollectionSummaryFields, 'total_count'>> = {
    gross_sales: {
        key: 'gross_sales',
        filterable: false,
        sortable: false,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: false,
        sortable: false,
    },
    net_sales: {
        key: 'net_sales',
        filterable: false,
        sortable: false,
    },
    net_qty: {
        key: 'net_qty',
        filterable: false,
        sortable: false,
    },
    total_sales: {
        key: 'total_sales',
        filterable: false,
        sortable: false,
    },
    return_value: {
        key: 'return_value',
        filterable: false,
        sortable: false,
    },
    return_qty: {
        key: 'return_qty',
        filterable: false,
        sortable: false,
    },

    ad_spends: {
        key: 'ad_spends',
        filterable: false,
        sortable: false,
    },
    clicks: {
        key: 'clicks',
        filterable: false,
        sortable: false,
    },
    views: {
        key: 'views',
        filterable: false,
        sortable: false,
    },

    stock: {
        key: 'stock',
        filterable: false,
        sortable: false,
    },
    on_order: {
        key: 'on_order',
        filterable: false,
        sortable: false,
    },
    ats: {
        key: 'ats',
        filterable: false,
        sortable: false,
        requiredKeys: ['on_order', 'stock'],
    },
    total_inventory_value_stock: {
        key: 'total_inventory_value_stock',
        filterable: false,
        sortable: false,
        requiredKeys: ['stock'],
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: false,
        sortable: false,
        requiredKeys: ['on_order', 'stock'],
    },
    sell_through: {
        key: 'sell_through',
        filterable: false,
        sortable: false,
        requiredKeys: ['stock', 'gross_qty'],
    },
};

export const CollectionSummaryRequestBodySearch: RequestBodySearchItem = {
    key: 'collection_summary',
    buildQuery: (options) => {
        const { ids } = options;
        const bKeyToOnOrder = options.metadata?.bKeyToOnOrder;

        const summaryAgg = buildAggregation({
            aggregation: filterAggregation('summary', matchAllQuery()),
            options,
            children: [
                CollectionSalesMetricsAggregation,
                CollectionAdsMetricsAggregation,
                {
                    key: SalesByCollectionFieldSchema.stock.key,
                    buildQuery: () =>
                        sumAggregation('stock').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return doc['stock'].size() == 0 ? 0 : doc['stock'].value;
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByCollectionFieldSchema.on_order.key,
                    buildQuery: () =>
                        sumAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple') return 0;
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return 0;
                                `,
                            ).params({
                                bKeyToOnOrder,
                            }),
                        ),
                },
                {
                    key: SalesByCollectionFieldSchema.total_inventory_value_stock.key,
                    buildQuery: () =>
                        sumAggregation(
                            SalesByCollectionQueryKeyTree.collections.filtered_collections.collections.to_root
                                .total_inventory_value_stock._key,
                        ).script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return (doc['price'].size() == 0 ? 0 : doc['price'].value) * (doc['stock'].size() == 0 ? 0 : doc['stock'].value)
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByCollectionFieldSchema.total_inventory_value_ats.key,
                    buildQuery: (options) => {
                        return sumAggregation(
                            SalesByCollectionFieldSchema.total_inventory_value_ats.key,
                        ).script(
                            script(
                                'source',
                                `
                                if (doc['type'].value != 'simple') return 0;
                                Map bKeyToOnOrder = params.bKeyToOnOrder;
                                String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                long onOrder = 0;
                                if (bKeyToOnOrder.containsKey(docBKey)) onOrder = bKeyToOnOrder.get(docBKey);
                                long stock = doc.containsKey('stock') && !doc['stock'].empty && doc['status'].value != 'deleted' ? doc['stock'].value : 0;
                                double ats = stock + onOrder;
                                double price = doc.containsKey('price') && !doc['price'].empty ? doc['price'].value : 0;
                                return ats*price;
                                `,
                            ).params({
                                bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                            }),
                        );
                    },
                },
            ],
        }) as any as RequestBodySearch;

        const query = requestBodySearch().size(0) as any;

        if (ids)
            query.query(
                nestedQuery(
                    termsQuery(SalesByCollectionFieldPathMap.h_key, ids),
                    SalesByCollectionFieldPathMap.collections,
                ),
            );

        query.agg(summaryAgg);

        return query;
    },
};
