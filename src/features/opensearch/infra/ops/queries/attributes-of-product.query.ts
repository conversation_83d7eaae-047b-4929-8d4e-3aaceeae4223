import {
    AggregationItem,
    buildAggregation,
    buildSortAgg,
    FieldSchema,
    MAX_BUCKET_SIZE,
    RequestBodySearchItem,
} from '@core';
import { AttributesOfProductDTO as AttributesOfProductDto } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import { DateUtil, DSLUtil } from '@utils';
import esb, {
    BoolQuery,
    boolQuery,
    BucketSortAggregation,
    existsQuery,
    filterAggregation,
    maxAggregation,
    nestedAggregation,
    rangeQuery,
    requestBodySearch,
    script,
    sumAggregation,
    termQuery,
    termsAggregation,
    TopHitsAggregation,
} from 'elastic-builder';
import { ForecastValueFilter, HKeyFilter, NameFilter } from '../filters';

export type AttributesOfProductFields = keyof AttributesOfProductDto | 'lead_time' | 'cost_per_item';

export const AttributesOfProductFieldSchema: FieldSchema<AttributesOfProductFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },

    attribute_value: {
        key: 'attribute_value',
        filterable: false,
        sortable: false,
    },
    product_id: {
        key: 'product_id',
        filterable: false,
        sortable: false,
    },
    categories: {
        key: 'categories',
        filterable: false,
        sortable: false,
    },
    gross_sales: {
        key: 'gross_sales',
        filterable: false,
        sortable: true,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: false,
        sortable: true,
    },
    return_value: {
        key: 'return_value',
        filterable: false,
        sortable: true,
    },
    return_qty: {
        key: 'return_qty',
        filterable: false,
        sortable: true,
    },
    return_rate: {
        key: 'return_rate',
        filterable: false,
        sortable: true,
        requiredKeys: ['gross_qty', 'return_qty'],
    },
    net_sales: {
        key: 'net_sales',
        filterable: false,
        sortable: true,
    },
    net_qty: {
        key: 'net_qty',
        filterable: false,
        sortable: true,
    },
    total_sales: {
        key: 'total_sales',
        filterable: false,
        sortable: true,
    },
    sales_per_day: {
        key: 'sales_per_day',
        filterable: false,
        sortable: true,
        requiredKeys: ['gross_qty'],
    },
    sales_day_left: {
        key: 'sales_day_left',
        filterable: false,
        sortable: true,
        requiredKeys: ['ats', 'sales_per_day'],
    },
    sell_through: {
        key: 'sell_through',
        filterable: false,
        sortable: true,
        requiredKeys: ['ats', 'gross_qty'],
    },
    tax: {
        key: 'tax',
        filterable: false,
        sortable: true,
    },
    discount: {
        key: 'discount',
        filterable: false,
        sortable: true,
    },
    shipping: {
        key: 'shipping',
        filterable: false,
        sortable: true,
    },
    views: {
        key: 'views',
        filterable: false,
        sortable: true,
    },
    stock: {
        key: 'stock',
        filterable: false,
        sortable: true,
    },
    on_order: {
        key: 'on_order',
        filterable: false,
        sortable: true,
    },
    ats: {
        key: 'ats',
        filterable: false,
        sortable: true,
        requiredKeys: ['on_order', 'stock'],
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: false,
        sortable: true,
        requiredKeys: ['price', 'ats'],
    },
    price: {
        key: 'price',
        filterable: false,
        sortable: true,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: NameFilter,
        sortable: false,
    },
    image: {
        key: 'image',
        filterable: false,
        sortable: false,
    },

    rop: {
        key: 'rop',
        filterable: false,
        sortable: false,
        requiredKeys: ['sales_per_day', 'lead_time'],
    },
    lead_time: {
        key: 'lead_time',
        filterable: false,
        sortable: false,
    },
    conversion_rate: {
        key: 'conversion_rate',
        filterable: false,
        sortable: false,
        requiredKeys: ['views', 'gross_qty'],
    },
    gross_sales_percentage: {
        key: 'gross_sales_percentage',
        filterable: false,
        sortable: false,
        requiredKeys: ['gross_sales'],
    },
    re_order_qty: {
        key: 're_order_qty',
        filterable: false,
        sortable: false,
        requiredKeys: ['forecasted_gross_qty', 'ats'],
    },
    forecasted_gross_qty: {
        key: 'forecasted_gross_qty',
        filterable: true,
        filterClass: ForecastValueFilter,
        sortable: true,
    },
    gross_profit: {
        key: 'gross_profit',
        filterable: false,
        sortable: false,
        requiredKeys: ['net_sales', 'net_qty', 'cost_per_item'],
    },
    gross_margin: {
        key: 'gross_margin',
        filterable: false,
        sortable: false,
        requiredKeys: ['net_sales', 'net_qty', 'cost_per_item'],
    },
    cost_per_item: {
        key: 'cost_per_item',
        filterable: false,
        sortable: true,
    },
    wos: {
        key: 'wos',
        filterable: false,
        sortable: true,
        requiredKeys: ['ats', 'gross_qty'],
    },
    stock_percentage: {
        key: 'stock_percentage',
        filterable: false,
        sortable: false,
    },
    forecast_sales_per_day: {
        key: 'forecast_sales_per_day',
        filterable: false,
        sortable: false,
    },
};
export const AttributeFieldPathMap = {
    forecast_metrics_date: 'forecast_metrics.date',
    forecast_metrics_channel_id: 'forecast_metrics.channel_id',
};

export const AttributeMetadataFields: AttributesOfProductFields[] = [
    'h_key',
    'attribute_value',
    'product_id',
    'categories',
    'gross_sales',
    'gross_qty',
    'return_value',
    'return_qty',
    'return_rate',
    'net_sales',
    'net_qty',
    'total_sales',
    'sales_per_day',
    'sales_day_left',
    'sell_through',
    'tax',
    'discount',
    'shipping',
    'views',
    'stock',
    'on_order',
    'ats',
    'total_inventory_value_ats',
    'image',
    'name',
];

const buildAttributeAgg = (fields: string[]) => {
    const metadataKeys = fields.filter((field) =>
        AttributeMetadataFields.includes(field as AttributesOfProductFields),
    );
    if (fields.length) {
        return new esb.TopHitsAggregation('attributes').size(1).source(metadataKeys);
    } else {
        return new esb.TopHitsAggregation('attributes').size(1).source(AttributeMetadataFields);
    }
};

export const AttributesOfProductRequestBodySearch: RequestBodySearchItem = {
    key: 'attributes_of_product',
    buildQuery: (options) => {
        const { fields, sort, fromDate, toDate, productHKey, attribute } = options;
        const totalDate = fromDate && toDate ? DateUtil.calculateDateDifference(fromDate, toDate) : null;
        const { bKeyToOnOrder, bKeyToDayOfLeadTimes } = options.metadata ?? {};
        let filterQuery: Optional<BoolQuery>;
        let sortAgg: Optional<BucketSortAggregation>;
        let metadataAgg: Optional<TopHitsAggregation>;

        if (sort) sortAgg = buildSortAgg({ sort, fieldSchema: AttributesOfProductFieldSchema });

        if (fields) metadataAgg = buildAttributeAgg(fields);

        // Build aggregation
        const itemsAgg = buildAggregation({
            aggregation: termsAggregation('items', `${attribute}`).size(MAX_BUCKET_SIZE), // Set max to unlimit bucket to get
            options,
            children: [
                AttributeAdsMetricsAggregation,
                AttributeSalesMetricsAggregation,
                AttributeForecastMetricsAggregation,
                DSLUtil.StockMetricsAggregation,

                {
                    key: AttributesOfProductFieldSchema.gross_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>gross_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>gross_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.return_value.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_value')
                            .bucketsPath({
                                value: `sales_metrics>nested>return_value`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.return_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>return_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.return_rate.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_rate')
                            .bucketsPath({
                                return_qty: `sales_metrics>nested>return_qty`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                'params.gross_qty != 0 ? Math.round((params.return_qty * 100 / params.gross_qty) * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.net_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('net_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>net_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.net_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('net_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>net_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.total_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('total_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>total_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.sales_per_day.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sales_per_day')
                            .bucketsPath({
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                totalDate !== null
                                    ? `${totalDate}!= 0 ? Math.round((params.gross_qty / ${totalDate}) * 100) / 100.00 : 0`
                                    : `return null`,
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.sell_through.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sell_through')
                            .bucketsPath({
                                gross_qty: 'sales_metrics>nested>gross_qty',
                                ats: 'ats',
                            })
                            .script(
                                '(params.gross_qty + params.ats) != 0 ? Math.round((params.gross_qty*100 / (params.gross_qty + params.ats)) * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.tax.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('tax')
                            .bucketsPath({
                                value: `sales_metrics>nested>tax`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.discount.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('discount')
                            .bucketsPath({
                                value: `sales_metrics>nested>discount`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.shipping.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('shipping')
                            .bucketsPath({
                                value: `sales_metrics>nested>shipping`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('stock')
                            .bucketsPath({
                                stock: `stock_metrics>nested>stock`,
                            })
                            .script('params.stock'),
                },
                {
                    key: AttributesOfProductFieldSchema.on_order.key,
                    buildQuery: () =>
                        sumAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    long docOnOrder = doc['on_order'].size() == 0 ? 0 : doc['on_order'].value;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return docOnOrder;
                                `,
                            ).params({
                                bKeyToOnOrder,
                            }),
                        ),
                },
                {
                    key: AttributesOfProductFieldSchema.ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('ats')
                            .bucketsPath({
                                stock: 'stock',
                                on_order: 'on_order',
                            })
                            .script(`params.stock + params.on_order`),
                },
                {
                    key: AttributesOfProductFieldSchema.sales_day_left.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sales_day_left')
                            .bucketsPath({
                                ats: `ats`,
                                sales_per_day: `sales_per_day`,
                                gross_qty: `gross_qty`,
                            })
                            .script(
                                'params.sales_per_day != 0 ? Math.round((params.gross_qty / params.sales_per_day) * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.price.key,
                    buildQuery: () =>
                        esb
                            .maxAggregation('price')
                            .field('price')
                            .script(script('inline', 'Math.round(doc["price"].value * 100) / 100.00')),
                },
                {
                    key: AttributesOfProductFieldSchema.total_inventory_value_ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('total_inventory_value_ats')
                            .bucketsPath({
                                price: 'price',
                                ats: 'ats',
                            })
                            .script('Math.round((params.price * params.ats) * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.lead_time.key,
                    buildQuery: () =>
                        maxAggregation('lead_time').script(
                            script(
                                'inline',
                                `
                                    Map bKeyToDayOfLeadTimes = params.bKeyToDayOfLeadTimes;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToDayOfLeadTimes.containsKey(docBKey)) return bKeyToDayOfLeadTimes.get(docBKey);
                                    return 45;
                                `,
                            ).params({
                                bKeyToDayOfLeadTimes,
                            }),
                        ),
                },
                {
                    key: AttributesOfProductFieldSchema.rop.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('rop')
                            .bucketsPath({
                                lead_time: `lead_time`,
                                sales_per_day: `sales_per_day`,
                            })
                            .script('Math.round(params.lead_time*params.sales_per_day * 100) / 100.00'),
                },
                {
                    key: AttributesOfProductFieldSchema.conversion_rate.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('conversion_rate')
                            .bucketsPath({
                                views: `ads_metrics>nested>views`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                'params.views != 0 ? Math.round((params.gross_qty/ params.views)* 100 * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.forecasted_gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecasted_gross_qty')
                            .bucketsPath({
                                value: `forecast_metrics>nested>forecasted_gross_qty`,
                            })
                            .script('Math.round(params.value)'),
                },
                {
                    key: AttributesOfProductFieldSchema.forecast_sales_per_day.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecast_sales_per_day')
                            .bucketsPath({
                                value: `forecast_metrics>nested>forecast_sales_per_day`,
                            })
                            .script('Math.round(params.value)'),
                },
                {
                    key: AttributesOfProductFieldSchema.re_order_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('re_order_qty')
                            .bucketsPath({
                                sales_forecast: `forecast_metrics>nested>forecasted_gross_qty`,
                                ats: 'ats',
                            })
                            .script('Math.round(params.sales_forecast - params.ats)'),
                },
                {
                    key: AttributesOfProductFieldSchema.gross_profit.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_profit')
                            .bucketsPath({
                                net_sales: `sales_metrics>nested>net_sales`,
                                net_qty: `sales_metrics>nested>net_qty`,
                                cost_per_item: `cost_per_item`,
                            })
                            .script(
                                `Math.round((params.net_sales - (params.cost_per_item * params.net_qty))* 100) / 100.00`,
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.gross_margin.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_margin')
                            .bucketsPath({
                                net_sales: `sales_metrics>nested>net_sales`,
                                net_qty: `sales_metrics>nested>net_qty`,
                                cost_per_item: `cost_per_item`,
                            })
                            .script(
                                `params.net_sales != 0 ? Math.round(100 * (params.net_sales - (params.cost_per_item * params.net_qty))/ params.net_sales * 100) / 100.00 : 0`,
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.wos.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('wos')
                            .bucketsPath({
                                ats: `ats`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                totalDate !== null
                                    ? `params.gross_qty != 0 ? Math.round(params.ats  * 100/ (params.gross_qty * 7 / ${totalDate})) / 100.00 : 0`
                                    : `return null`,
                            ),
                },
                {
                    key: AttributesOfProductFieldSchema.cost_per_item.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('cost_per_item')
                            .field('cost')
                            .script(script('inline', 'Math.round(doc["cost"].value * 100) / 100.00')),
                },
            ],
            sortAgg,
            metadataAgg,
        });

        // Main query
        const query = requestBodySearch().size(0).query(termQuery('parent_h_key', productHKey));
        if (filterQuery) query.query(filterQuery);
        // if (ids) query.query(termsQuery('h_key', ids));

        query.agg(itemsAgg);

        return query;
    },
};

export const AttributeSalesMetricsAggregation: AggregationItem = {
    key: 'sales_metrics',
    children: [
        {
            key: AttributesOfProductFieldSchema.gross_sales.key,
            buildQuery: () => sumAggregation('gross_sales', 'sales_metrics.gross_sales').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.gross_qty.key,
            buildQuery: () => sumAggregation('gross_qty', 'sales_metrics.gross_qty').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.net_sales.key,
            buildQuery: () => sumAggregation('net_sales', 'sales_metrics.net_sales').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.net_qty.key,
            buildQuery: () => sumAggregation('net_qty', 'sales_metrics.net_qty').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.total_sales.key,
            buildQuery: () => sumAggregation('total_sales', 'sales_metrics.total_sales').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.return_value.key,
            buildQuery: () => sumAggregation('return_value', 'sales_metrics.return_value').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.return_qty.key,
            buildQuery: () => sumAggregation('return_qty', 'sales_metrics.return_qty').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.discount.key,
            buildQuery: () => sumAggregation('discount', 'sales_metrics.discount').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.tax.key,
            buildQuery: () => sumAggregation('tax', 'sales_metrics.tax').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.shipping.key,
            buildQuery: () => sumAggregation('shipping', 'sales_metrics.shipping').missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery().mustNot([existsQuery('sales_metrics.h_warehouse_key')]);
        if (fromDate && toDate)
            filterQuery.must([rangeQuery('sales_metrics.date').gte(fromDate).lte(toDate)]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation('sales_metrics', 'sales_metrics').agg(nestedQuery);

        return query;
    },
};

export const AttributeAdsMetricsAggregation: AggregationItem = {
    key: 'ads_metrics',
    children: [
        {
            key: AttributesOfProductFieldSchema.views.key,
            buildQuery: () => sumAggregation('views', 'ads_metrics.views').missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery();
        if (fromDate && toDate) filterQuery.must([rangeQuery('ads_metrics.date').gte(fromDate).lte(toDate)]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation('ads_metrics', 'ads_metrics').agg(nestedQuery);

        return query;
    },
};

export const AttributeForecastMetricsAggregation: AggregationItem = {
    key: 'forecast_metrics',
    children: [
        {
            key: AttributesOfProductFieldSchema.forecasted_gross_qty.key,
            buildQuery: () =>
                sumAggregation('forecasted_gross_qty', 'forecast_metrics.forecasted_gross_qty').missing('0'),
        },
        {
            key: AttributesOfProductFieldSchema.forecast_sales_per_day.key,
            buildQuery: () =>
                sumAggregation('forecast_sales_per_day', 'forecast_metrics.forecast_sales_per_day').missing(
                    '0',
                ),
        },
    ],
    buildQuery: (options, children) => {
        const { forecastFromDate, forecastToDate } = options;

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation(
                'nested',
                boolQuery().must([
                    termQuery(AttributeFieldPathMap.forecast_metrics_channel_id).value('0'),
                    rangeQuery(AttributeFieldPathMap.forecast_metrics_date)
                        .gte(forecastFromDate!)
                        .lte(forecastToDate!),
                ]),
            ),
            options,
            children,
        });

        const query = nestedAggregation('forecast_metrics', 'forecast_metrics').agg(nestedQuery);

        return query;
    },
};
