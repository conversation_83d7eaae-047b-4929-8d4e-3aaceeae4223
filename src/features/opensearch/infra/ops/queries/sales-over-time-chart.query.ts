import { FromSourceFilter, <PERSON><PERSON><PERSON>ilter, TypeFilter } from '../filters';
import { SalesByProductFieldPathMap } from '@features/opensearch/infra/ops/queries/sales-by-product.query';
import { FieldSchema, QueryOptions, AggregationItem, buildAggregation, RequestBodySearchItem } from '@core';
import {
    boolQuery,
    termQuery,
    rangeQuery,
    existsQuery,
    sumAggregation,
    filterAggregation,
    nestedAggregation,
    requestBodySearch,
    bucketScriptAggregation,
    dateHistogramAggregation,
} from 'elastic-builder';

export type SalesOverTimeChartFields = 'h_key' | 'from_source' | 'type';

export const SalesOverTimeChartFieldSchema: FieldSchema<SalesOverTimeChartFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    type: {
        key: 'type',
        filterable: true,
        filterClass: TypeFilter,
        sortable: false,
    },
};

export const SalesOverTimeChartRequestBodySearch: RequestBodySearchItem = {
    key: 'sales_over_time_chart',
    buildQuery: (options: QueryOptions) => {
        const query = requestBodySearch().size(0);
        if (options.fromSource) {
            query.query(boolQuery().must([termQuery('from_source', options.fromSource)]));
        }
        query.agg(SalesDataAggregation.buildQuery(options, SalesDataAggregation.children));

        return query;
    },
};

const SalesDataAggregation: AggregationItem = {
    key: 'sales_agg',
    children: [],
    buildQuery: (options, children) => {
        const { fromDate, toDate, timeFrame } = options;
        const filterAggQuery = boolQuery()
            .must([rangeQuery('sales_metrics.date').gte(fromDate!).lte(toDate!)])
            .mustNot([existsQuery(SalesByProductFieldPathMap.h_warehouse_key)]);
        const filterAgg = filterAggregation('nested', filterAggQuery)
            .agg(
                dateHistogramAggregation('sales_histogram_agg', 'sales_metrics.date')
                    .calendarInterval(timeFrame)
                    .format('yyyy-MM-dd')
                    .agg(sumAggregation('gross_qty', 'sales_metrics.gross_qty'))
                    .agg(sumAggregation('gross_sales', 'sales_metrics.gross_sales'))
                    .agg(sumAggregation('net_sales', 'sales_metrics.net_sales'))
                    .agg(sumAggregation('net_qty', 'sales_metrics.net_qty'))
                    .agg(sumAggregation('return_qty', 'sales_metrics.return_qty'))
                    .agg(
                        bucketScriptAggregation('avg_selling_price')
                            .bucketsPath({
                                gross_sales: 'gross_sales',
                                gross_qty: 'gross_qty',
                            })
                            .script(
                                'params.gross_qty != 0 ? Math.round(params.gross_sales / params.gross_qty * 100) / 100.00 : 0',
                            ),
                    ),
            )
            .agg(sumAggregation('total_gross_qty', 'sales_metrics.gross_qty'))
            .agg(sumAggregation('total_gross_sales', 'sales_metrics.gross_sales'))
            .agg(sumAggregation('total_net_sales', 'sales_metrics.net_sales'))
            .agg(sumAggregation('total_net_qty', 'sales_metrics.net_qty'))
            .agg(sumAggregation('total_return_qty', 'sales_metrics.return_qty'));

        const query = nestedAggregation('sales_agg', 'sales_metrics').agg(
            buildAggregation({
                aggregation: filterAgg,
                options,
                children,
            }),
        );

        return query;
    },
};
