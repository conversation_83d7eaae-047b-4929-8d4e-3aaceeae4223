import { FromSourceFilter, HKeyFilter, ProductHKeyFilter, TypeFilter } from '../filters';
import { SalesByProductFieldPathMap } from '@features/opensearch/infra/ops/queries/sales-by-product.query';
import {
    boolQuery,
    termQuery,
    rangeQuery,
    existsQuery,
    sumAggregation,
    filterAggregation,
    nestedAggregation,
    requestBodySearch,
    dateHistogramAggregation,
    bucketScriptAggregation,
} from 'elastic-builder';
import {
    FieldSchema,
    QueryOptions,
    AggregationItem,
    buildAggregation,
    RequestBodySearchItem,
    buildQueryAndFilterAgg,
} from '@core';
import esb from 'elastic-builder';

export type SalesBarChartFields = 'h_key' | 'from_source' | 'type' | 'product_h_key';

export const SalesBarChartFieldSchema: FieldSchema<SalesBarChartFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },
    product_h_key: {
        key: 'product_h_key',
        filterable: true,
        filterClass: ProductHKeyFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    type: {
        key: 'type',
        filterable: true,
        filterClass: TypeFilter,
        sortable: false,
    },
};

export const SalesBarChartRequestBodySearch: RequestBodySearchItem = {
    key: 'sales_bar_chart',
    buildQuery: (options: QueryOptions) => {
        const { hKey } = options;

        const { filterQuery } = buildQueryAndFilterAgg({
            filter: { product_h_key: { eq: hKey } },
            fieldSchema: SalesBarChartFieldSchema,
        });

        const byVariantAgg = filterAggregation('by_variant')
            .filter(esb.termQuery('type', 'simple'))
            .agg(SalesDataAggregation.buildQuery(options, SalesDataAggregation.children));

        const byProductAgg = filterAggregation('by_product')
            .filter(esb.termQuery('type', 'configuration'))
            .agg(ForecastDataAggregation.buildQuery(options, ForecastDataAggregation.children));

        const query = requestBodySearch().size(0);

        query.query(filterQuery);
        query.aggs([byVariantAgg, byProductAgg]);

        return query;
    },
};

const SalesDataAggregation: AggregationItem = {
    key: 'sales_agg',
    children: [],
    buildQuery: (options, children) => {
        const { fromDate, toDate, timeFrame } = options;
        const filterAggQuery = boolQuery()
            .must([rangeQuery('sales_metrics.date').gte(fromDate!).lte(toDate!)])
            .mustNot([existsQuery(SalesByProductFieldPathMap.h_warehouse_key)]);
        const filterAgg = filterAggregation('nested', filterAggQuery).agg(
            dateHistogramAggregation('sales_histogram_agg', 'sales_metrics.date')
                .calendarInterval(timeFrame)
                .format('yyyy-MM-dd')
                .minDocCount(0)
                .extendedBounds(fromDate!, toDate!)
                .agg(sumAggregation('gross_qty', 'sales_metrics.gross_qty'))
                .agg(
                    bucketScriptAggregation('gross_qty_rounded').bucketsPath({
                        value: 'gross_qty',
                    }).script(`
                        if (params.value == 0 || params.value == null) {
                            return 0;
                        } else {
                            return Math.round(params.value * 100) / 100.00;
                        }
                    `),
                ),
        );
        const query = nestedAggregation('sales_agg', 'sales_metrics').agg(
            buildAggregation({
                aggregation: filterAgg,
                options,
                children,
            }),
        );

        return query;
    },
};

const ForecastDataAggregation: AggregationItem = {
    key: 'forecast_agg',
    children: [],
    buildQuery: (options, children) => {
        const { fromDate, toDate, timeFrame } = options;
        const filterAggQuery = boolQuery().must([
            termQuery('forecast_metrics.channel_id').value('0'),
            rangeQuery('forecast_metrics.date').gte(fromDate!).lte(toDate!),
        ]);
        const filterAgg = filterAggregation('nested', filterAggQuery).agg(
            dateHistogramAggregation('forecast_histogram_agg', 'forecast_metrics.date')
                .calendarInterval(timeFrame)
                .format('yyyy-MM-dd')
                .minDocCount(0)
                .extendedBounds(fromDate!, toDate!)
                .agg(sumAggregation('forecasted_gross_qty', 'forecast_metrics.forecast_value'))
                .agg(
                    bucketScriptAggregation('forecasted_gross_qty_rounded')
                        .bucketsPath({
                            value: 'forecasted_gross_qty',
                        })
                        .script('Math.round(params.value)'),
                ),
        );
        const query = nestedAggregation('forecast_agg', 'forecast_metrics').agg(
            buildAggregation({
                aggregation: filterAgg,
                options,
                children,
            }),
        );

        return query;
    },
};
