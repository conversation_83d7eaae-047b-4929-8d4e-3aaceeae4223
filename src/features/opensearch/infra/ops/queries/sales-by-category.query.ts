import {
    AggregationItem,
    buildAggregation,
    buildQueryAndFilterAgg,
    buildSortAgg,
    FieldSchema,
    MAX_BUCKET_SIZE,
    RequestBodySearchItem,
} from '@core';
import { SalesByCategoryDto } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import { DSLUtil } from '@utils';
import esb, {
    boolQuery,
    BoolQuery,
    bucketScriptAggregation,
    BucketSelectorAggregation,
    BucketSortAggregation,
    dateHistogramAggregation,
    existsQuery,
    filterAggregation,
    nestedAggregation,
    nestedQuery,
    rangeQuery,
    requestBodySearch,
    reverseNestedAggregation,
    script,
    sumAggregation,
    termQuery,
    termsAggregation,
    termsQuery,
    TopHitsAggregation,
} from 'elastic-builder';
import {
    AdSpendsFilter,
    ATSFilter,
    CategoryHKeyFilter,
    CategoryNameFilter,
    ClicksFilter,
    filterFactory,
    GrossQuantityFilter,
    GrossSalesFilter,
    GrossSalesPercentageFilter,
    NetQuantityFilter,
    NetSalesFilter,
    OnOrderFilter,
    ReturnQuantityFilter,
    ReturnValueFilter,
    SellThroughFilter,
    StockFilter,
    StockPercentageFilter,
    TotalInventoryValueAtsFilter,
    TotalInventoryValueStockFilter,
    TotalSalesFilter,
    ViewsFilter,
} from '../filters';

export type SalesByCategoryFields = keyof SalesByCategoryDto | 'max_score';
export const SalesByCategoryFieldSchema: FieldSchema<SalesByCategoryFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: CategoryHKeyFilter,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: CategoryNameFilter,
        sortable: false,
    },
    stock: {
        key: 'stock',
        filterable: true,
        filterClass: StockFilter,
        sortable: true,
    },
    on_order: {
        key: 'on_order',
        filterable: true,
        filterClass: OnOrderFilter,
        sortable: true,
    },
    ats: {
        key: 'ats',
        filterable: true,
        filterClass: ATSFilter,
        sortable: true,
        requiredKeys: ['on_order', 'stock'],
    },
    total_inventory_value_stock: {
        key: 'total_inventory_value_stock',
        filterable: true,
        filterClass: TotalInventoryValueStockFilter,
        sortable: true,
    },

    // sales_metrics
    gross_sales: {
        key: 'gross_sales',
        filterable: true,
        filterClass: GrossSalesFilter,
        sortable: true,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: true,
        filterClass: GrossQuantityFilter,
        sortable: true,
    },
    net_sales: {
        key: 'net_sales',
        filterClass: NetSalesFilter,
        filterable: true,
        sortable: true,
    },
    net_qty: {
        key: 'net_qty',
        filterable: true,
        filterClass: NetQuantityFilter,
        sortable: true,
    },
    total_sales: {
        key: 'total_sales',
        filterable: true,
        filterClass: TotalSalesFilter,
        sortable: true,
    },
    return_value: {
        key: 'return_value',
        filterable: true,
        filterClass: ReturnValueFilter,
        sortable: true,
    },
    return_qty: {
        key: 'return_qty',
        filterable: true,
        filterClass: ReturnQuantityFilter,
        sortable: true,
    },
    sell_through: {
        key: 'sell_through',
        filterable: true,
        filterClass: SellThroughFilter,
        sortable: true,
        requiredKeys: ['stock', 'gross_qty'],
    },
    sales_over_time: {
        key: 'sales_over_time',
        filterable: false,
        sortable: false,
    },

    // ads_metrics
    ad_spends: {
        key: 'ad_spends',
        filterable: true,
        filterClass: AdSpendsFilter,
        sortable: true,
    },
    clicks: {
        key: 'clicks',
        filterable: true,
        filterClass: ClicksFilter,
        sortable: true,
    },
    views: {
        key: 'views',
        filterable: true,
        filterClass: ViewsFilter,
        sortable: true,
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: true,
        filterClass: TotalInventoryValueAtsFilter,
        sortable: true,
    },
    stock_percentage: {
        key: 'stock_percentage',
        filterable: true,
        filterClass: StockPercentageFilter,
        sortable: true,
        requiredKeys: ['stock'],
    },
    gross_sales_percentage: {
        key: 'gross_sales_percentage',
        filterable: true,
        filterClass: GrossSalesPercentageFilter,
        sortable: true,
        requiredKeys: ['gross_sales'],
    },
    max_score: {
        key: 'max_score',
        filterable: false,
        sortable: true,
    },

    // forecast metrics
    forecasted_gross_qty: {
        key: 'forecasted_gross_qty',
        filterable: false,
        sortable: false,
    },
    forecast_over_time: {
        key: 'forecast_over_time',
        filterable: false,
        sortable: false,
    },
};

export const SalesByCategoryFieldPathMap = {
    h_key: 'categories.h_key',
    categories: 'categories',
    // metadata
    // name: 'name',

    // sales_metrics
    sales_metrics: 'sales_metrics',
    sales_metrics_date: 'sales_metrics.date',
    gross_sales: 'sales_metrics.gross_sales',
    gross_qty: 'sales_metrics.gross_qty',
    net_sales: 'sales_metrics.net_sales',
    net_qty: 'sales_metrics.net_qty',
    total_sales: 'sales_metrics.total_sales',
    return_qty: 'sales_metrics.return_qty',
    return_value: 'sales_metrics.return_value',
    h_warehouse_key: 'sales_metrics.h_warehouse_key',

    // ads metrics
    ads_metrics: 'ads_metrics',
    ads_metrics_date: 'ads_metrics.date',
    ad_spends: 'ads_metrics.ad_spends',
    clicks: 'ads_metrics.clicks',
    views: 'ads_metrics.views',

    stock: 'stock',
    on_order: 'on_order',
    ats: 'ats',
    total_inventory_value_stock: 'total_inventory_value_stock',
    total_inventory_value_ats: 'total_inventory_value_ats',

    // forecast metrics
    forecast_metrics: 'forecast_metrics',
    forecast_metrics_date: 'forecast_metrics.date',
    forecast_value: 'forecast_metrics.forecast_value',

    // metadata sort_orders
    name_order: 'categories.sort_orders.name',
};

export const SalesByCategoryQueryKeyTree = {
    _key: 'categories',
    categories: {
        _key: 'categories',
        filtered_categories: {
            _key: 'filtered_categories',
            categories: {
                _key: 'categories',
                metadata: {
                    _key: 'metadata',
                },
                to_root: {
                    _key: 'to_root',
                    sales_metrics: {
                        _key: 'sales_metrics',
                        nested: {
                            _key: 'nested',
                            gross_sales: {
                                _key: 'gross_sales',
                            },
                            gross_qty: {
                                _key: 'gross_qty',
                            },
                            net_sales: {
                                _key: 'net_sales',
                            },
                            net_qty: {
                                _key: 'net_qty',
                            },
                            total_sales: {
                                _key: 'total_sales',
                            },
                            return_value: {
                                _key: 'return_value',
                            },
                            return_qty: {
                                _key: 'return_qty',
                            },
                        },
                    },
                    ads_metrics: {
                        _key: 'ads_metrics',
                        nested: {
                            _key: 'nested',
                            ad_spends: {
                                _key: 'ad_spends',
                            },
                            clicks: {
                                _key: 'clicks',
                            },
                            views: {
                                _key: 'views',
                            },
                        },
                    },
                    forecast_metrics: {
                        _key: 'forecast_metrics',
                        nested: {
                            _key: 'nested',
                            forecasted_gross_qty: {
                                _key: 'forecasted_gross_qty',
                            },
                            forecast_over_time: {
                                _key: 'forecast_over_time',
                            },
                        },
                    },
                    nested: {
                        _key: 'nested',
                        stock: {
                            _key: 'stock',
                        },
                        on_order: {
                            _key: 'on_order',
                        },
                    },
                    total_inventory_value_stock: {
                        _key: 'total_inventory_value_stock',
                    },
                    total_inventory_value_ats: {
                        _key: 'total_inventory_value_ats',
                    },
                },
                gross_sales: {
                    _key: 'gross_sales',
                },
                gross_qty: {
                    _key: 'gross_qty',
                },
                return_value: {
                    _key: 'return_value',
                },
                return_qty: {
                    _key: 'return_qty',
                },
                total_sales: {
                    _key: 'total_sales',
                },
                net_sales: {
                    _key: 'net_sales',
                },
                net_qty: {
                    _key: 'net_qty',
                },

                ad_spends: {
                    _key: 'ad_spends',
                },
                clicks: {
                    _key: 'clicks',
                },
                views: {
                    _key: 'views',
                },

                stock: {
                    _key: 'stock',
                },
                on_order: {
                    _key: 'on_order',
                },
                ats: {
                    _key: 'ats',
                },
                total_inventory_value_stock: {
                    _key: 'total_inventory_value_stock',
                },
                total_inventory_value_ats: {
                    _key: 'total_inventory_value_ats',
                },

                sell_through: {
                    _key: 'sell_through',
                },

                // sort order
                name_order: {
                    _key: 'name_order',
                },
            },
        },
    },
    items: {
        _key: 'items',
        sales_metrics: {
            _key: 'sales_metrics',
            nested: {
                _key: 'nested',
                gross_sales: {
                    _key: 'gross_sales',
                },
            },
        },
        stock: {
            _key: 'stock',
        },
    },
};

const SalesByCategoriesMetadataFields = ['categories.name'];
const SalesByCategorySearchKeys = ['name'];

export const SalesByCategoryRequestBodySearch: RequestBodySearchItem = {
    key: SalesByCategoryQueryKeyTree._key,
    buildQuery: (options) => {
        const { ids, sort, filter, search, fields, defaultSort } = options;
        const { totalStock, totalGrossSales, bKeyToOnOrder } = options.metadata ?? {};
        let sortAgg: Optional<BucketSortAggregation>;
        let filterAgg: Optional<BucketSelectorAggregation>;
        let filterQuery: Optional<BoolQuery>;
        let metadataAgg: Optional<TopHitsAggregation>;

        if (sort) {
            sortAgg = buildSortAgg({ sort, fieldSchema: SalesByCategoryFieldSchema, defaultSort });
        } else {
            if (defaultSort)
                sortAgg = buildSortAgg({ sort: defaultSort, fieldSchema: SalesByCategoryFieldSchema });
        }
        if (filter) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                search,
                fieldSchema: SalesByCategoryFieldSchema,
                searchFields: SalesByCategorySearchKeys,
            });
            filterAgg = queyAndFilterData.filterAgg;
            filterQuery = queyAndFilterData.filterQuery;
        }

        if (fields && ids) metadataAgg = buildMetadataAgg(fields);

        const salesMetricsBucketPathPrefix = `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested._key}`;
        const adsMetricsBucketPathPrefix = `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics.nested._key}`;

        const categoriesAgg = buildAggregation({
            aggregation: termsAggregation(
                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories._key,
                'categories.h_key',
            ).size(MAX_BUCKET_SIZE), // Set max to unlimit bucket to get
            options,
            children: [
                CategoryMetadataAggregation,
                CategoryToRootAggregation,
                DSLUtil.MaxScoreAggregation,

                // sales_metrics
                {
                    key: SalesByCategoryFieldSchema.gross_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .gross_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.gross_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.gross_sales_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_sales_percentage')
                            .bucketsPath({
                                gross_sales: `gross_sales`,
                            })
                            .script(
                                `Math.round((params.gross_sales * 100 / ${totalGrossSales}) * 100) / 100.00`,
                            ),
                },
                {
                    key: SalesByCategoryFieldSchema.gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .gross_qty._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.gross_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.net_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .net_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.net_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.net_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.net_qty
                                    ._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.net_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.total_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .total_sales._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.total_sales._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.return_value.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .return_value._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.return_value._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.return_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .return_qty._key,
                            )
                            .bucketsPath({
                                value: `${salesMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics.nested.return_qty._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },

                // ads_metrics
                {
                    key: SalesByCategoryFieldSchema.ad_spends.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .ad_spends._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics.nested.ad_spends._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.clicks.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.clicks
                                    ._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics.nested.clicks._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.views.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.views
                                    ._key,
                            )
                            .bucketsPath({
                                value: `${adsMetricsBucketPathPrefix}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics.nested.views._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.stock
                                    ._key,
                            )
                            .bucketsPath({
                                value: `to_root>nested>stock`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByCategoryFieldSchema.stock_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('stock_percentage')
                            .bucketsPath({
                                stock: 'stock',
                            })
                            .script(`Math.round((params.stock * 100 / ${totalStock}) * 100) / 100.00`),
                },
                {
                    key: SalesByCategoryFieldSchema.on_order.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.on_order
                                    ._key,
                            )
                            .bucketsPath({
                                value: `to_root>nested>on_order`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByCategoryFieldSchema.ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.ats
                                    ._key,
                            )
                            .bucketsPath({
                                stock: `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.stock._key}`,
                                on_order: `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.on_order._key}`,
                            })
                            .script('params.stock + params.on_order'),
                },
                {
                    key: SalesByCategoryFieldSchema.sell_through.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .sell_through._key,
                            )
                            .bucketsPath({
                                gross_qty: `gross_qty`,
                                stock: `stock`,
                            })
                            .script(
                                '(params.gross_qty + params.stock) != 0 ? Math.round((params.gross_qty / (params.gross_qty + params.stock)) * 10000) / 100.00 : 0',
                            ),
                },
                {
                    key: SalesByCategoryFieldSchema.total_inventory_value_stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .total_inventory_value_stock._key,
                            )
                            .bucketsPath({
                                value: `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.total_inventory_value_stock._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByCategoryFieldSchema.total_inventory_value_ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation(
                                SalesByCategoryQueryKeyTree.categories.filtered_categories.categories
                                    .total_inventory_value_ats._key,
                            )
                            .bucketsPath({
                                value: `${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root._key}>${SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.total_inventory_value_ats._key}`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
            ],
            filterAgg,
            sortAgg,
            metadataAgg,
        });

        const filteredCategoriesConditions: esb.Query[] = [];
        const filteredCategoriesBoolQuery = boolQuery().must(filteredCategoriesConditions);

        if (filter) {
            const filterModels = filterFactory.create(filter, SalesByCategoryFieldSchema);
            filterModels.forEach((filterModel) => {
                switch (true) {
                    case filterModel instanceof CategoryNameFilter:
                    case filterModel instanceof CategoryHKeyFilter: {
                        filterModel.apply(filteredCategoriesBoolQuery, true);
                    }
                }
            });
        }

        const filteredCategoriesAgg = filterAggregation(
            'filtered_categories',
            filteredCategoriesBoolQuery,
        ).agg(categoriesAgg);

        const query = requestBodySearch().size(0);

        if (filterQuery) query.query(filterQuery);

        if (ids)
            query.query(
                nestedQuery(
                    termsQuery(SalesByCategoryFieldPathMap.h_key, ids),
                    SalesByCategoryFieldPathMap.categories,
                ),
            );

        query.agg(
            nestedAggregation(
                SalesByCategoryQueryKeyTree.categories._key,
                SalesByCategoryFieldPathMap.categories,
            ).agg(filteredCategoriesAgg),
        );

        // query.agg(
        //     CategorySalesMetricsByItemAggregation.buildQuery(
        //         options,
        //         CategorySalesMetricsByItemAggregation.children,
        //     ),
        // );

        // query.agg(CategoryTotalStockAggregation.buildQuery(options, CategoryTotalStockAggregation.children));

        return query;
    },
};

const buildMetadataAgg = (fields: string[]) => {
    const metadataKeys = fields
        .map((field) => `categories.${field}`)
        .filter((field) => SalesByCategoriesMetadataFields.includes(field as SalesByCategoryFields));
    if (fields.length) {
        return new esb.TopHitsAggregation(
            SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.metadata._key,
        )
            .size(1)
            .source(metadataKeys);
    } else {
        return new esb.TopHitsAggregation(
            SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.metadata._key,
        )
            .size(1)
            .source(metadataKeys);
    }
};

const CategoryMetadataAggregation: AggregationItem = {
    key: 'metadata',
    buildQuery: () => new esb.TopHitsAggregation('metadata').size(1).source(['categories.name']),
};

export const CategorySalesMetricsAggregation: AggregationItem = {
    key: SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics._key,
    children: [
        {
            key: SalesByCategoryFieldSchema.gross_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.gross_sales._key,
                    SalesByCategoryFieldPathMap.gross_sales,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.gross_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.gross_qty._key,
                    SalesByCategoryFieldPathMap.gross_qty,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.net_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.net_sales._key,
                    SalesByCategoryFieldPathMap.net_sales,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.net_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.net_qty._key,
                    SalesByCategoryFieldPathMap.net_qty,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.total_sales.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.total_sales._key,
                    SalesByCategoryFieldPathMap.total_sales,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.return_value.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.return_value._key,
                    SalesByCategoryFieldPathMap.return_value,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.return_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .sales_metrics.nested.return_qty._key,
                    SalesByCategoryFieldPathMap.return_qty,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.sales_over_time.key,
            buildQuery: (options) => {
                const { timeFrame } = options;
                return dateHistogramAggregation('sales_over_time', 'sales_metrics.date')
                    .calendarInterval(timeFrame)
                    .agg(sumAggregation('gross_sales', 'sales_metrics.gross_sales'))
                    .agg(
                        bucketScriptAggregation('gross_sales_rounded').bucketsPath({
                            value: 'gross_sales',
                        }).script(`
                            if (params.value == 0 || params.value == null) {
                                return 0;
                            } else {
                                return Math.round(params.value * 100) / 100.00;
                            }
                        `),
                    )
                    .agg(sumAggregation('gross_qty', 'sales_metrics.gross_qty'))
                    .agg(
                        bucketScriptAggregation('gross_qty_rounded').bucketsPath({
                            value: 'gross_qty',
                        }).script(`
                            if (params.value == 0 || params.value == null) {
                                return 0;
                            } else {
                                return Math.round(params.value * 100) / 100.00;
                            }
                        `),
                    );
            },
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery().mustNot([existsQuery(SalesByCategoryFieldPathMap.h_warehouse_key)]);
        if (fromDate && toDate)
            filterQuery.must([
                rangeQuery(SalesByCategoryFieldPathMap.sales_metrics_date).gte(fromDate).lte(toDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation(
            SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.sales_metrics._key,
            SalesByCategoryFieldPathMap.sales_metrics,
        ).agg(nestedQuery);

        return query;
    },
};

export const CategoryAdsMetricsAggregation: AggregationItem = {
    key: SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics._key,
    children: [
        {
            key: SalesByCategoryFieldSchema.ad_spends.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics
                        .nested.ad_spends._key,
                    SalesByCategoryFieldPathMap.ad_spends,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.clicks.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics
                        .nested.clicks._key,
                    SalesByCategoryFieldPathMap.clicks,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.views.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics
                        .nested.views._key,
                    SalesByCategoryFieldPathMap.views,
                ).missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery();
        if (fromDate && toDate)
            filterQuery.must([
                rangeQuery(SalesByCategoryFieldPathMap.ads_metrics_date).gte(fromDate).lte(toDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation(
            SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.ads_metrics._key,
            SalesByCategoryFieldPathMap.ads_metrics,
        ).agg(nestedQuery);

        return query;
    },
};

export const CategoryForecastMetricsAggregation: AggregationItem = {
    key: SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.forecast_metrics._key,
    children: [
        {
            key: SalesByCategoryFieldSchema.forecasted_gross_qty.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .forecast_metrics.nested.forecasted_gross_qty._key,
                    SalesByCategoryFieldPathMap.forecast_value,
                ).missing('0'),
        },
        {
            key: SalesByCategoryFieldSchema.forecast_over_time.key,
            buildQuery: (options) => {
                const { timeFrame } = options;
                return dateHistogramAggregation('forecast_over_time', 'forecast_metrics.date')
                    .calendarInterval(timeFrame)
                    .agg(sumAggregation('forecasted_gross_qty', 'forecast_metrics.forecast_value'))
                    .agg(
                        bucketScriptAggregation('forecasted_gross_qty_rounded').bucketsPath({
                            value: 'forecasted_gross_qty',
                        }).script(`
                            if (params.value == 0 || params.value == null) {
                                return 0;
                            } else {
                                return Math.round(params.value * 100) / 100.00;
                            }
                        `),
                    );
            },
        },
    ],
    buildQuery: (options, children) => {
        const { forecastFromDate, forecastToDate } = options;
        const filterQuery = boolQuery();
        if (forecastFromDate && forecastToDate)
            filterQuery.must([
                rangeQuery(SalesByCategoryFieldPathMap.forecast_metrics_date)
                    .gte(forecastFromDate)
                    .lte(forecastToDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation(
            SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root.forecast_metrics
                ._key,
            SalesByCategoryFieldPathMap.forecast_metrics,
        ).agg(nestedQuery);

        return query;
    },
};

const CategoryToRootAggregation: AggregationItem = {
    key: SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root._key,
    children: [
        CategorySalesMetricsAggregation,
        CategoryAdsMetricsAggregation,
        CategoryForecastMetricsAggregation,
        {
            key: 'filtered_product',
            buildQuery: (options, children) => {
                return buildAggregation({
                    aggregation: filterAggregation(
                        'nested',
                        boolQuery().must([termQuery('type').value('simple')]),
                    ),
                    options,
                    children,
                });
            },
            children: [
                {
                    key: SalesByCategoryFieldSchema.stock.key,
                    buildQuery: () =>
                        sumAggregation('stock').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                                    return doc['stock'].size() == 0 ? 0 : doc['stock'].value;
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByCategoryFieldSchema.on_order.key,
                    buildQuery: (options) =>
                        sumAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple') return 0;
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return 0;
                                `,
                            ).params({
                                bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                            }),
                        ),
                },
            ],
        },
        {
            key: SalesByCategoryFieldSchema.total_inventory_value_stock.key,
            buildQuery: () =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .total_inventory_value_stock._key,
                ).script(
                    script(
                        'inline',
                        `
                            if (doc['type'].value != 'simple' || doc['status'].value == 'deleted') return 0;
                            return (doc['price'].size() == 0 ? 0 : doc['price'].value) * (doc['stock'].size() == 0 ? 0 : doc['stock'].value)
                        `,
                    ),
                ),
        },
        {
            key: SalesByCategoryFieldSchema.total_inventory_value_ats.key,
            buildQuery: (options) =>
                sumAggregation(
                    SalesByCategoryQueryKeyTree.categories.filtered_categories.categories.to_root
                        .total_inventory_value_ats._key,
                ).script(
                    script(
                        'source',
                        `
                        if (doc['type'].value != 'simple') return 0;
                        Map bKeyToOnOrder = params.bKeyToOnOrder;
                        String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                        long onOrder = 0;
                        if (bKeyToOnOrder.containsKey(docBKey)) onOrder = bKeyToOnOrder.get(docBKey);
                        long stock = doc.containsKey('stock') && !doc['stock'].empty && doc['status'].value != 'deleted' ? doc['stock'].value : 0;
                        double ats = stock + onOrder;
                        double price = doc.containsKey('price') && !doc['price'].empty ? doc['price'].value : 0;
                        return ats*price;
                        `,
                    ).params({
                        bKeyToOnOrder: options.metadata?.bKeyToOnOrder,
                    }),
                ),
        },
    ],

    buildQuery: (options, children) => {
        const query = buildAggregation({
            aggregation: reverseNestedAggregation('to_root'),
            options,
            children,
        });
        return query;
    },
};
