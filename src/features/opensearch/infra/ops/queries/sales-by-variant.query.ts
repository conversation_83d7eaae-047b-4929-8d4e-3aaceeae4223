import {
    AggregationItem,
    buildAggregation,
    buildQueryAndFilterAgg,
    buildSortAgg,
    FieldSchema,
    MAX_BUCKET_SIZE,
    RequestBodySearchItem,
} from '@core';
import { SalesByVariantDTO } from '@features/opensearch/domain';
import { Optional } from '@heronjs/common';
import { DateUtil, DSLUtil } from '@utils';
import esb, {
    BoolQuery,
    boolQuery,
    bucketScriptAggregation,
    BucketSelectorAggregation,
    BucketSortAggregation,
    cardinalityAggregation,
    dateHistogramAggregation,
    existsQuery,
    filterAggregation,
    maxAggregation,
    minAggregation,
    nestedAggregation,
    rangeQuery,
    requestBodySearch,
    reverseNestedAggregation,
    script,
    sumAggregation,
    termQuery,
    termsAggregation,
    termsQuery,
    TopHitsAggregation,
} from 'elastic-builder';
import {
    AdSpendsFilter,
    ATSFilter,
    BarcodeFilter,
    CategoriesHKeyFilter,
    CategoriesNameFilter,
    ChannelIdFilter,
    ClicksFilter,
    CollectionsHKeyFilter,
    CollectionsNameFilter,
    ColorFilter,
    CompareAtPriceFilter,
    ConversionRateFilter,
    CostPerItemFilter,
    DayOnSiteFilter,
    DaysOfStockFilter,
    DiscountFilter,
    ForecastConfidenceFilter,
    ForecastedGrossQtyFilter,
    ForecastedGrossSalesFilter,
    ForecastedSalesDaysRemainingBasedOnAtsFilter,
    ForecastedSalesDaysRemainingBasedOnStockFilter,
    ForecastValueFilter,
    FromSourceFilter,
    GrossMarginFilter,
    GrossProfitFilter,
    GrossQuantityFilter,
    GrossSalesFilter,
    GrossSalesPercentageFilter,
    GrossSalesRankFilter,
    HKeyFilter,
    LeadTimeFilter,
    NameFilter,
    NetQuantityFilter,
    NetSalesFilter,
    OnOrderFilter,
    PriceFilter,
    ProductGradeFilter,
    ProductHKeyFilter,
    ProductIdFilter,
    ReOrderQtyFilter,
    ReplenishableFilter,
    ReturnQuantityFilter,
    ReturnRateFilter,
    ReturnValueFilter,
    RopFilter,
    SalePatternFilter,
    SalesDaysLeftFilter,
    SalesPerDayFilter,
    SellThroughFilter,
    ShippingFilter,
    SizeFilter,
    SKUFilter,
    StatusFilter,
    StockFilter,
    StockPercentageFilter,
    TagsHKeyFilter,
    TagsNameFilter,
    TaxFilter,
    TotalInventoryValueAtsFilter,
    TotalInventoryValueStockFilter,
    TotalSalesFilter,
    TrendFilter,
    TypeFilter,
    VariantIdFilter,
    VendorsHKeyFilter,
    ViewsFilter,
    WosFilter,
} from '../filters';

export type SalesByVariantFields =
    | keyof SalesByVariantDTO
    | 'b_key_order'
    | 'variant_id_order'
    | 'name_order'
    | 'sku_order'
    | 'vendors_h_key'
    | 'barcode_order'
    | 'channel_id'
    | 'color_order'
    | 'size_order'
    | 'trend_order'
    | 'sale_pattern_order'
    | 'status_order'
    | 'categories_h_key'
    | 'collections_h_key'
    | 'categories_name'
    | 'collections_name'
    | 'tags_h_key'
    | 'tags_name'
    | 'published_at'
    | 'max_score'
    | 'trend'
    | 'sale_pattern'
    | 'forecast_confidence'
    | 'product_grade'
    | 'product_h_key'
    | 'cost_with_default_value'
    | 'gross_sales_rank';

export const SalesByVariantFieldSchema: FieldSchema<SalesByVariantFields> = {
    h_key: {
        key: 'h_key',
        filterable: true,
        filterClass: HKeyFilter,
        sortable: false,
    },
    from_source: {
        key: 'from_source',
        filterable: true,
        filterClass: FromSourceFilter,
        sortable: false,
    },
    parent_h_key: {
        key: 'parent_h_key',
        filterable: false,
        sortable: false,
    },
    b_key: {
        key: 'b_key',
        filterable: false,
        sortable: true,
        overrideSortKey: 'b_key_order',
    },
    product_id: {
        key: 'product_id',
        filterable: true,
        filterClass: ProductIdFilter,
        sortable: false,
    },
    product_h_key: {
        key: 'product_h_key',
        filterable: true,
        filterClass: ProductHKeyFilter,
        sortable: false,
    },
    variant_id: {
        key: 'variant_id',
        filterable: true,
        filterClass: VariantIdFilter,
        sortable: true,
        overrideSortKey: 'variant_id_order',
    },
    type: {
        key: 'type',
        filterable: true,
        filterClass: TypeFilter,
        sortable: false,
    },
    name: {
        key: 'name',
        filterable: true,
        filterClass: NameFilter,
        sortable: true,
        overrideSortKey: 'name_order',
    },
    sku: {
        key: 'sku',
        filterable: true,
        filterClass: SKUFilter,
        sortable: true,
        overrideSortKey: 'sku_order',
    },
    barcode: {
        key: 'barcode',
        filterable: true,
        filterClass: BarcodeFilter,
        sortable: true,
        overrideSortKey: 'barcode_order',
    },
    vendors_h_key: {
        key: 'vendors_h_key',
        filterable: true,
        filterClass: VendorsHKeyFilter,
        sortable: false,
    },
    channel_id: {
        key: 'channel_id',
        filterable: true,
        filterClass: ChannelIdFilter,
        sortable: false,
    },
    price: {
        key: 'price',
        filterable: true,
        filterClass: PriceFilter,
        sortable: true,
    },
    compare_at_price: {
        key: 'compare_at_price',
        filterable: true,
        filterClass: CompareAtPriceFilter,
        sortable: true,
    },
    image: {
        key: 'image',
        filterable: false,
        sortable: false,
    },
    color: {
        key: 'color',
        filterable: true,
        filterClass: ColorFilter,
        sortable: true,
        overrideSortKey: 'color_order',
    },
    default_color: {
        key: 'default_color',
        filterable: true,
        sortable: false,
    },
    size: {
        key: 'size',
        filterable: true,
        filterClass: SizeFilter,
        sortable: true,
        overrideSortKey: 'size_order',
    },
    status: {
        key: 'status',
        filterable: true,
        filterClass: StatusFilter,
        sortable: true,
        overrideSortKey: 'status_order',
    },
    replenishable: {
        key: 'replenishable',
        filterClass: ReplenishableFilter,
        filterable: true,
        sortable: true,
    },
    stock: {
        key: 'stock',
        filterable: true,
        filterClass: StockFilter,
        sortable: true,
    },
    on_order: {
        key: 'on_order',
        filterable: true,
        filterClass: OnOrderFilter,
        sortable: true,
    },
    ats: {
        key: 'ats',
        filterable: true,
        filterClass: ATSFilter,
        sortable: true,
        requiredKeys: ['on_order', 'stock'],
    },
    categories: {
        key: 'categories',
        filterable: false,
        sortable: false,
    },
    collections: {
        key: 'collections',
        filterable: false,
        sortable: false,
    },
    tags: {
        key: 'tags',
        filterable: false,
        sortable: false,
    },
    vendors: {
        key: 'vendors',
        filterable: false,
        sortable: false,
    },
    days_on_site: {
        key: 'days_on_site',
        filterable: true,
        filterClass: DayOnSiteFilter,
        sortable: true,
        overrideSortKey: 'published_at',
        reverseSortOrder: true,
        requiredKeys: ['published_at'],
    },
    published_at: {
        key: 'published_at',
        filterable: false,
        sortable: false,
    },

    // sales_metrics
    gross_sales: {
        key: 'gross_sales',
        filterable: true,
        filterClass: GrossSalesFilter,
        sortable: true,
    },
    gross_qty: {
        key: 'gross_qty',
        filterable: true,
        filterClass: GrossQuantityFilter,
        sortable: true,
    },
    net_sales: {
        key: 'net_sales',
        filterClass: NetSalesFilter,
        filterable: true,
        sortable: true,
    },
    net_qty: {
        key: 'net_qty',
        filterable: true,
        filterClass: NetQuantityFilter,
        sortable: true,
    },
    total_sales: {
        key: 'total_sales',
        filterable: true,
        filterClass: TotalSalesFilter,
        sortable: true,
    },
    return_value: {
        key: 'return_value',
        filterable: true,
        filterClass: ReturnValueFilter,
        sortable: true,
    },
    return_qty: {
        key: 'return_qty',
        filterable: true,
        filterClass: ReturnQuantityFilter,
        sortable: true,
    },
    discount: {
        key: 'discount',
        filterable: true,
        filterClass: DiscountFilter,
        sortable: true,
    },
    tax: {
        key: 'tax',
        filterable: true,
        filterClass: TaxFilter,
        sortable: true,
    },
    shipping: {
        key: 'shipping',
        filterable: true,
        filterClass: ShippingFilter,
        sortable: true,
    },
    sales_per_day: {
        key: 'sales_per_day',
        filterable: true,
        filterClass: SalesPerDayFilter,
        sortable: true,
        requiredKeys: ['gross_qty'],
    },
    sales_days_left: {
        key: 'sales_days_left',
        filterable: true,
        filterClass: SalesDaysLeftFilter,
        sortable: true,
        requiredKeys: ['sales_per_day', 'ats'],
    },
    gross_profit: {
        key: 'gross_profit',
        filterable: true,
        filterClass: GrossProfitFilter,
        sortable: true,
        requiredKeys: ['net_sales', 'net_qty', 'cost_with_default_value'],
    },
    gross_margin: {
        key: 'gross_margin',
        filterable: true,
        filterClass: GrossMarginFilter,
        sortable: true,
        requiredKeys: ['net_sales', 'net_qty', 'cost_with_default_value'],
    },
    cost_per_item: {
        key: 'cost_per_item',
        filterable: true,
        filterClass: CostPerItemFilter,
        sortable: true,
    },
    total_inventory_value_stock: {
        key: 'total_inventory_value_stock',
        filterable: true,
        filterClass: TotalInventoryValueStockFilter,
        sortable: true,
        requiredKeys: ['stock', 'price'],
    },
    total_inventory_value_ats: {
        key: 'total_inventory_value_ats',
        filterable: true,
        filterClass: TotalInventoryValueAtsFilter,
        sortable: true,
        requiredKeys: ['ats', 'price'],
    },
    wos: {
        key: 'wos',
        filterable: true,
        filterClass: WosFilter,
        sortable: true,
        requiredKeys: ['ats', 'gross_qty'],
    },
    return_rate: {
        key: 'return_rate',
        filterable: true,
        filterClass: ReturnRateFilter,
        sortable: true,
        requiredKeys: ['gross_qty', 'return_qty'],
    },
    sell_through: {
        key: 'sell_through',
        filterable: true,
        filterClass: SellThroughFilter,
        sortable: true,
        requiredKeys: ['ats', 'gross_qty'],
    },
    re_order_qty: {
        key: 're_order_qty',
        filterable: true,
        filterClass: ReOrderQtyFilter,
        sortable: true,
        requiredKeys: ['forecasted_gross_qty', 'ats'],
    },
    stock_percentage: {
        key: 'stock_percentage',
        filterable: true,
        filterClass: StockPercentageFilter,
        sortable: true,
        requiredKeys: ['stock'],
    },
    gross_sales_percentage: {
        key: 'gross_sales_percentage',
        filterable: true,
        filterClass: GrossSalesPercentageFilter,
        sortable: true,
        requiredKeys: ['gross_sales'],
    },
    rop: {
        key: 'rop',
        filterable: true,
        filterClass: RopFilter,
        sortable: true,
        requiredKeys: ['sales_per_day', 'lead_time'],
    },
    lead_time: {
        key: 'lead_time',
        filterable: true,
        filterClass: LeadTimeFilter,
        sortable: true,
    },
    days_of_stock: {
        key: 'days_of_stock',
        filterable: true,
        filterClass: DaysOfStockFilter,
        sortable: true,
    },
    sales_over_time: {
        key: 'sales_over_time',
        filterable: false,
        sortable: false,
    },

    // ads_metrics
    ad_spends: {
        key: 'ad_spends',
        filterable: true,
        filterClass: AdSpendsFilter,
        sortable: true,
    },
    clicks: {
        key: 'clicks',
        filterable: true,
        filterClass: ClicksFilter,
        sortable: true,
    },
    views: {
        key: 'views',
        filterable: true,
        filterClass: ViewsFilter,
        sortable: true,
    },
    conversion_rate: {
        key: 'conversion_rate',
        filterable: true,
        filterClass: ConversionRateFilter,
        sortable: true,
        requiredKeys: ['views', 'gross_qty'],
    },
    b_key_order: {
        key: 'b_key_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    variant_id_order: {
        key: 'variant_id_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    name_order: {
        key: 'name_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    sku_order: {
        key: 'sku_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    barcode_order: {
        key: 'barcode_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    color_order: {
        key: 'color_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    size_order: {
        key: 'size_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    trend_order: {
        key: 'trend_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    sale_pattern_order: {
        key: 'sale_pattern_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    status_order: {
        key: 'status_order',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    categories_h_key: {
        key: 'categories_h_key',
        filterable: true,
        filterClass: CategoriesHKeyFilter,
        sortable: false,
        hidden: true,
    },
    collections_h_key: {
        key: 'collections_h_key',
        filterable: true,
        filterClass: CollectionsHKeyFilter,
        sortable: false,
        hidden: true,
    },
    categories_name: {
        key: 'categories_name',
        filterable: true,
        filterClass: CategoriesNameFilter,
        sortable: false,
        hidden: true,
    },
    collections_name: {
        key: 'collections_name',
        filterable: true,
        filterClass: CollectionsNameFilter,
        sortable: false,
        hidden: true,
    },
    tags_h_key: {
        key: 'tags_h_key',
        filterable: true,
        filterClass: TagsHKeyFilter,
        sortable: false,
        hidden: true,
    },
    tags_name: {
        key: 'tags_name',
        filterable: true,
        filterClass: TagsNameFilter,
        sortable: false,
        hidden: true,
    },
    max_score: {
        key: 'max_score',
        filterable: false,
        sortable: true,
    },
    trend: {
        key: 'trend',
        filterable: true,
        filterClass: TrendFilter,
        sortable: true,
        overrideSortKey: 'trend_order',
    },
    sale_pattern: {
        key: 'sale_pattern',
        filterable: true,
        filterClass: SalePatternFilter,
        sortable: true,
        overrideSortKey: 'sale_pattern_order',
    },
    forecast_confidence: {
        key: 'forecast_confidence',
        filterable: true,
        filterClass: ForecastConfidenceFilter,
        sortable: false,
    },
    forecast_value: {
        key: 'forecast_value',
        filterable: true,
        filterClass: ForecastValueFilter,
        sortable: true,
        requiredKeys: ['lead_time', 'days_of_stock'],
    },
    forecast_sales_per_day: {
        key: 'forecast_sales_per_day',
        filterable: false,
        sortable: false,
    },
    forecasted_gross_qty: {
        key: 'forecasted_gross_qty',
        filterable: true,
        filterClass: ForecastedGrossQtyFilter,
        sortable: true,
        requiredKeys: ['lead_time', 'days_of_stock'],
    },
    forecasted_gross_sales: {
        key: 'forecasted_gross_sales',
        filterable: true,
        filterClass: ForecastedGrossSalesFilter,
        sortable: true,
        requiredKeys: ['forecasted_gross_qty', 'price'],
    },
    forecasted_sales_days_remaining_based_on_stock: {
        key: 'forecasted_sales_days_remaining_based_on_stock',
        filterable: true,
        filterClass: ForecastedSalesDaysRemainingBasedOnStockFilter,
        sortable: true,
    },
    forecasted_sales_days_remaining_based_on_ats: {
        key: 'forecasted_sales_days_remaining_based_on_ats',
        filterable: true,
        filterClass: ForecastedSalesDaysRemainingBasedOnAtsFilter,
        sortable: true,
    },
    product_grade: {
        key: 'product_grade',
        filterable: true,
        filterClass: ProductGradeFilter,
        sortable: true,
        reverseSortOrder: true,
    },
    cost_with_default_value: {
        key: 'cost_with_default_value',
        filterable: false,
        sortable: true,
        hidden: true,
    },
    collection_position: {
        key: 'collection_position',
        filterable: false,
        sortable: true,
    },

    gross_sales_rank: {
        key: 'gross_sales_rank',
        filterable: true,
        filterClass: GrossSalesRankFilter,
        sortable: true,
    },
    forecast_over_time: {
        key: 'forecast_over_time',
        filterable: false,
        sortable: false,
    },

    // stock_metrics
    days_in_stock_over_time: {
        key: 'days_in_stock_over_time',
        filterable: false,
        sortable: false,
    },

    in_transit_stock: {
        key: 'in_transit_stock',
        filterable: false,
        sortable: true,
    },
};

export const SalesByVariantFieldPathMap = {
    // metadata
    b_key: 'b_key',
    variant_id: 'variant_id',
    sku: 'sku',
    name: 'name',
    barcode: 'barcode',
    size: 'size',
    color: 'color',
    default_color: 'default_color',
    product_id: 'product_id',
    product_h_key: 'product_h_key',
    trend: 'trend',
    sale_pattern: 'sale_pattern',
    forecast_confidence: 'forecast_confidence',
    price: 'price',
    compare_at_price: 'compare_at_price',
    cost: 'cost',
    stock: 'stock',
    // sales_metrics
    sales_metrics: 'sales_metrics',
    sales_metrics_date: 'sales_metrics.date',
    gross_sales: 'sales_metrics.gross_sales',
    gross_qty: 'sales_metrics.gross_qty',
    net_sales: 'sales_metrics.net_sales',
    net_qty: 'sales_metrics.net_qty',
    total_sales: 'sales_metrics.total_sales',
    return_qty: 'sales_metrics.return_qty',
    return_value: 'sales_metrics.return_value',
    discount: 'sales_metrics.discount',
    tax: 'sales_metrics.tax',
    shipping: 'sales_metrics.shipping',
    h_warehouse_key: 'sales_metrics.h_warehouse_key',
    sales_days_left: 'sales_metrics.sales_days_left',
    sales_over_time: 'sales_metrics.sales_over_time',

    // sort_orders
    b_key_order: 'sort_orders.b_key',
    variant_id_order: 'sort_orders.variant_id',
    sku_order: 'sort_orders.sku',
    name_order: 'sort_orders.name',
    barcode_order: 'sort_orders.barcode',
    size_order: 'sort_orders.size',
    color_order: 'sort_orders.color',
    trend_order: 'sort_orders.trend',
    sale_pattern_order: 'sort_orders.sale_pattern',

    // forecast_metrics
    forecast_metrics: 'forecast_metrics',
    forecast_metrics_date: 'forecast_metrics.date',
    forecast_metrics_channel_id: 'forecast_metrics.channel_id',
    forecast_metrics_h_key: 'forecast_metrics.h_key',
    forecast_value: 'forecast_metrics.forecast_value',
    forecast_sales_per_day: 'forecast_metrics.forecast_sales_per_day',
    forecast_over_time: 'forecast_metrics.forecast_over_time',

    // ads_metrics
    ads_metrics: 'ads_metrics',
    ads_metrics_date: 'ads_metrics.date',
    ad_spends: 'ads_metrics.ad_spends',
    clicks: 'ads_metrics.clicks',
    views: 'ads_metrics.views',
};

export const SalesByVariantMetadataFields: SalesByVariantFields[] = [
    'h_key',
    'parent_h_key',
    'from_source',
    'b_key',
    'variant_id',
    'product_id',
    'sku',
    'type',
    'name',
    'color',
    'default_color',
    'size',
    'barcode',
    'price',
    'image',
    'status',
    'compare_at_price',
    'on_order',
    'ats',
    'trend',
    'sale_pattern',
    'forecast_confidence',
    'categories',
    'collections',
    'tags',
    'vendors',
    'from_source',
    'product_h_key',
];
export const SalesByVariantSearchFields = ['name', 'sku', 'barcode'];

export const SalesByVariantRequestBodySearch: RequestBodySearchItem = {
    key: 'variants',
    buildQuery: (options) => {
        const { ids, sort, fields, filter, search, fromDate, toDate, defaultSort, isSearchingProduct } =
            options;
        const {
            bKeyToOnOrder,
            bKeyToDayOfLeadTimes,
            bKeyToReplenishables,
            bKeyToDayOfStock,
            totalStock,
            totalGrossSales,
            productGradeData,
            grossSalesRankData,
            totalInTransitStock,
        } = options.metadata ?? {};

        let sortAgg: Optional<BucketSortAggregation>;
        let filterAgg: Optional<BucketSelectorAggregation>;
        let filterQuery: BoolQuery = esb.boolQuery();
        let metadataAgg: Optional<TopHitsAggregation>;
        const totalDate = fromDate && toDate ? DateUtil.calculateDateDifference(fromDate, toDate) : null;
        const [fromSource] = filter?.from_source ? Object.values(filter.from_source) : ['amazon'];

        if (sort) {
            sortAgg = buildSortAgg({ sort, fieldSchema: SalesByVariantFieldSchema, defaultSort });
        } else {
            if (defaultSort)
                sortAgg = buildSortAgg({ sort: defaultSort, fieldSchema: SalesByVariantFieldSchema });
        }

        if (filter || search) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                search,
                fieldSchema: SalesByVariantFieldSchema,
                searchFields: SalesByVariantSearchFields,
            });
            filterAgg = queyAndFilterData.filterAgg;
            filterQuery = queyAndFilterData.filterQuery;
        }

        if (fields && ids) metadataAgg = buildMetadataAgg(fields);

        // Build aggregation
        const itemsAgg = buildAggregation({
            aggregation: termsAggregation(
                'items',
                isSearchingProduct ? 'product_h_key.keyword' : 'h_key',
            ).size(MAX_BUCKET_SIZE), // Set max to unlimit bucket to get
            options,
            children: [
                AdsMetricsAggregation,
                SalesMetricsAggregation,
                ForecastMetricsAggregation,
                CollecttionsAggregation,
                StockMetricsAggregation,
                DSLUtil.MaxScoreAggregation,
                // sort_orders
                {
                    key: SalesByVariantFieldSchema.b_key_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('b_key_order').field(SalesByVariantFieldPathMap.b_key_order),
                },
                {
                    key: SalesByVariantFieldSchema.variant_id_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('variant_id_order').field(
                            SalesByVariantFieldPathMap.variant_id_order,
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.name_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('name_order').field(SalesByVariantFieldPathMap.name_order),
                },
                {
                    key: SalesByVariantFieldSchema.sku_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('sku_order').field(SalesByVariantFieldPathMap.sku_order),
                },
                {
                    key: SalesByVariantFieldSchema.barcode_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('barcode_order').field(
                            SalesByVariantFieldPathMap.barcode_order,
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.size_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('size_order').field(SalesByVariantFieldPathMap.size_order),
                },
                {
                    key: SalesByVariantFieldSchema.color_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('color_order').field(SalesByVariantFieldPathMap.color_order),
                },
                {
                    key: SalesByVariantFieldSchema.trend_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('trend_order').field(SalesByVariantFieldPathMap.trend_order),
                },
                {
                    key: SalesByVariantFieldSchema.sale_pattern_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('sale_pattern_order').field(
                            SalesByVariantFieldPathMap.sale_pattern_order,
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.status_order.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('status_order').script(
                            script(
                                'inline',
                                `
                                    String status = doc['status'].value;
                                    if (status == 'active') return 1;
                                    if (status == 'archived') return 2;
                                    if (status == 'deleted') return 3;
                                    if (status == 'draft') return 4;
                                    return 5;
                                `,
                            ),
                        ),
                },
                // sales_metrics
                {
                    key: SalesByVariantFieldSchema.gross_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>gross_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.gross_sales_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_sales_percentage')
                            .bucketsPath({
                                gross_sales: `gross_sales`,
                            })
                            .script(
                                `Math.round((params.gross_sales * 100 / ${totalGrossSales}) * 100) / 100.00`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>gross_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.net_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('net_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>net_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.net_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('net_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>net_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.total_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('total_sales')
                            .bucketsPath({
                                value: `sales_metrics>nested>total_sales`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.return_value.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_value')
                            .bucketsPath({
                                value: `sales_metrics>nested>return_value`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.return_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_qty')
                            .bucketsPath({
                                value: `sales_metrics>nested>return_qty`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.discount.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('discount')
                            .bucketsPath({
                                value: `sales_metrics>nested>discount`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.tax.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('tax')
                            .bucketsPath({
                                value: `sales_metrics>nested>tax`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.shipping.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('shipping')
                            .bucketsPath({
                                value: `sales_metrics>nested>shipping`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.sales_per_day.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sales_per_day')
                            .bucketsPath({
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                totalDate !== null
                                    ? `${totalDate} != 0 ? Math.round((params.gross_qty / ${totalDate}) * 100) / 100.00 : 0`
                                    : `return null`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.sell_through.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sell_through')
                            .bucketsPath({
                                gross_qty: 'sales_metrics>nested>gross_qty',
                                stock: 'stock',
                            })
                            .script(
                                '(params.gross_qty + params.stock) != 0 ? Math.round((params.gross_qty*100 / (params.gross_qty + params.stock)) * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.return_rate.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('return_rate')
                            .bucketsPath({
                                return_qty: `sales_metrics>nested>return_qty`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                'params.gross_qty != 0 ? Math.round((params.return_qty * 100 / params.gross_qty) * 100) / 100.00 : 0',
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.published_at.key,
                    buildQuery: () =>
                        esb.maxAggregation(
                            SalesByVariantFieldSchema.published_at.key,
                            SalesByVariantFieldSchema.published_at.key,
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.days_on_site.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('days_on_site')
                            .bucketsPath({
                                published_at: SalesByVariantFieldSchema.published_at.key,
                            })
                            .script(
                                `long currentDateMillis = new Date().getTime();long createdAtMillis = (long) params.published_at;long dayDifference = (currentDateMillis - createdAtMillis) / 86400000;return Math.round(dayDifference);`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.sales_days_left.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('sales_days_left')
                            .bucketsPath({
                                ats: `ats`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                totalDate !== null
                                    ? `params.gross_qty != 0 ? Math.floor((params.ats * ${totalDate}) / params.gross_qty * 100) / 100.00 : 0`
                                    : `return null`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.gross_profit.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_profit')
                            .bucketsPath({
                                net_sales: `sales_metrics>nested>net_sales`,
                                net_qty: `sales_metrics>nested>net_qty`,
                                cost_per_item: `cost_with_default_value`,
                            })
                            .script(
                                `Math.round((params.net_sales - (params.cost_per_item * params.net_qty))* 100) / 100.00`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.gross_margin.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('gross_margin')
                            .bucketsPath({
                                net_sales: `sales_metrics>nested>net_sales`,
                                net_qty: `sales_metrics>nested>net_qty`,
                                cost_per_item: `cost_with_default_value`,
                            })
                            .script(
                                `params.net_sales != 0 ? Math.round(100 * (params.net_sales - (params.cost_per_item * params.net_qty))/ params.net_sales * 100) / 100.00 : 0`,
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.total_inventory_value_stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('total_inventory_value_stock')
                            .bucketsPath({
                                stock: `stock`,
                                price: `price`,
                            })
                            .script(`Math.round(params.stock * params.price * 100) / 100.00`),
                },
                {
                    key: SalesByVariantFieldSchema.total_inventory_value_ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('total_inventory_value_ats')
                            .bucketsPath({
                                ats: `ats`,
                                price: `price`,
                            })
                            .script(`Math.round(params.ats * params.price * 100) / 100.00`),
                },
                {
                    key: SalesByVariantFieldSchema.wos.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('wos')
                            .bucketsPath({
                                ats: `ats`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                totalDate !== null
                                    ? `params.gross_qty != 0 ? Math.round(params.ats  * 100/ (params.gross_qty * 7 / ${totalDate})) / 100.00 : 0`
                                    : `return null`,
                            ),
                },
                // forecast_metrics
                {
                    key: SalesByVariantFieldSchema.forecast_value.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecast_value')
                            .bucketsPath({
                                value: `forecast_metrics>nested>forecast_value`,
                            })
                            .script('Math.round(params.value)'),
                },
                {
                    key: SalesByVariantFieldSchema.forecast_sales_per_day.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecast_sales_per_day')
                            .bucketsPath({
                                value: `forecast_metrics>nested>forecast_sales_per_day`,
                            })
                            .script('Math.round(params.value)'),
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_gross_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecasted_gross_qty')
                            .bucketsPath({
                                value: `forecast_metrics>nested>forecasted_gross_qty`,
                            })
                            .script('Math.round(params.value)'),
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_gross_sales.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecasted_gross_sales')
                            .bucketsPath({
                                forecasted_gross_qty: `forecast_metrics>nested>forecasted_gross_qty`,
                                price: `price`,
                            })
                            .script('Math.round(params.forecasted_gross_qty * params.price * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_stock.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecasted_sales_days_remaining_based_on_stock')
                            .bucketsPath({
                                value: `forecast_metrics>to_parent>forecasted_sales_days_remaining_based_on_stock`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('forecasted_sales_days_remaining_based_on_ats')
                            .bucketsPath({
                                value: `forecast_metrics>to_parent>forecasted_sales_days_remaining_based_on_ats`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByVariantFieldSchema.re_order_qty.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('re_order_qty')
                            .bucketsPath({
                                forecasted_gross_qty: `forecast_metrics>nested>forecasted_gross_qty`,
                                ats: 'ats',
                            })
                            .script('Math.max(0, Math.round(params.forecasted_gross_qty) - params.ats)'),
                },
                // ads_metrics
                {
                    key: SalesByVariantFieldSchema.ad_spends.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('ad_spends')
                            .bucketsPath({
                                value: `ads_metrics>nested>ad_spends`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.clicks.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('clicks')
                            .bucketsPath({
                                value: `ads_metrics>nested>clicks`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.views.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('views')
                            .bucketsPath({
                                value: `ads_metrics>nested>views`,
                            })
                            .script('Math.round(params.value * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.conversion_rate.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('conversion_rate')
                            .bucketsPath({
                                views: `ads_metrics>nested>views`,
                                gross_qty: `sales_metrics>nested>gross_qty`,
                            })
                            .script(
                                'params.views != 0 ? Math.round((params.gross_qty/ params.views)* 100 * 100) / 100.00 : 0',
                            ),
                },
                // inventory_metrics
                {
                    key: SalesByVariantFieldSchema.stock.key,
                    buildQuery: () =>
                        maxAggregation('stock').script(
                            script(
                                'inline',
                                `
                                    if (doc['status'].value == 'deleted') return 0;
                                    return doc['stock'].size() == 0 ? 0 : doc['stock'].value;
                                `,
                            ),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.stock_percentage.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('stock_percentage')
                            .bucketsPath({
                                stock: 'stock',
                            })
                            .script(`Math.round((params.stock * 100 / ${totalStock}) * 100) / 100.00`),
                },
                {
                    key: SalesByVariantFieldSchema.on_order.key,
                    buildQuery: () =>
                        minAggregation('on_order').script(
                            script(
                                'inline',
                                `
                                    if (doc['type'].value != 'simple') return 0;
                                    Map bKeyToOnOrder = params.bKeyToOnOrder;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToOnOrder.containsKey(docBKey)) return bKeyToOnOrder.get(docBKey);
                                    return 0;
                                `,
                            ).params({
                                bKeyToOnOrder,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.replenishable.key,
                    buildQuery: () =>
                        minAggregation('replenishable').script(
                            script(
                                'inline',
                                `
                                    Map bKeyToReplenishable = params.bKeyToReplenishables;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToReplenishable.containsKey(docBKey)) return bKeyToReplenishable.get(docBKey);
                                    return 1;
                                `,
                            ).params({
                                bKeyToReplenishables,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.lead_time.key,
                    buildQuery: () =>
                        minAggregation('lead_time').script(
                            script(
                                'inline',
                                `
                                    Map bKeyToDayOfLeadTimes = params.bKeyToDayOfLeadTimes;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToDayOfLeadTimes.containsKey(docBKey)) return bKeyToDayOfLeadTimes.get(docBKey);
                                    return 45;
                                `,
                            ).params({
                                bKeyToDayOfLeadTimes,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.days_of_stock.key,
                    buildQuery: () =>
                        minAggregation('days_of_stock').script(
                            script(
                                'inline',
                                `
                                    Map bKeyToDayOfStock = params.bKeyToDayOfStock;
                                    String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
                                    if (bKeyToDayOfStock.containsKey(docBKey)) return bKeyToDayOfStock.get(docBKey);
                                    return 90;
                                `,
                            ).params({
                                bKeyToDayOfStock,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.rop.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('rop')
                            .bucketsPath({
                                lead_time: `lead_time`,
                                sales_per_day: `sales_per_day`,
                            })
                            .script('Math.round(params.lead_time*params.sales_per_day * 100) / 100.00'),
                },
                {
                    key: SalesByVariantFieldSchema.ats.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('ats')
                            .bucketsPath({
                                stock: 'stock',
                                on_order: 'on_order',
                            })
                            .script(`params.stock + params.on_order`),
                },
                {
                    key: SalesByVariantFieldSchema.price.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('price')
                            .field(SalesByVariantFieldPathMap.price)
                            .script(script('inline', 'Math.round(doc["price"].value * 100) / 100.00')),
                },
                {
                    key: SalesByVariantFieldSchema.compare_at_price.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('compare_at_price')
                            .field(SalesByVariantFieldPathMap.compare_at_price)
                            .script(
                                script('inline', 'Math.round(doc["compare_at_price"].value * 100) / 100.00'),
                            ),
                },
                {
                    key: SalesByVariantFieldSchema.cost_per_item.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('cost_per_item')
                            .field(SalesByVariantFieldPathMap.cost)
                            .script(script('inline', 'Math.round(doc["cost"].value * 100) / 100.00')),
                },
                {
                    key: SalesByVariantFieldSchema.cost_with_default_value.key,
                    buildQuery: () =>
                        new esb.MaxAggregation('cost_with_default_value')
                            .field(SalesByVariantFieldPathMap.cost)
                            .missing('0'),
                },
                {
                    key: SalesByVariantFieldSchema.product_grade.key,
                    buildQuery: () =>
                        minAggregation('product_grade').script(
                            script(
                                'inline',
                                `
                                    Map productGradeData = params.productGradeData;
                                    String docBKey = doc['h_key'].size() == 0 ? '' : doc['h_key'].value;
                                    if (productGradeData.containsKey(docBKey)) return productGradeData.get(docBKey);
                                    return 3;
                                `,
                            ).params({
                                productGradeData,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.collection_position.key,
                    buildQuery: () =>
                        esb
                            .bucketScriptAggregation('collection_position')
                            .bucketsPath({
                                value: `collections>nested>collection_position`,
                            })
                            .script('params.value'),
                },
                {
                    key: SalesByVariantFieldSchema.gross_sales_rank.key,
                    buildQuery: () =>
                        minAggregation('gross_sales_rank').script(
                            script(
                                'inline',
                                `
                                    Map grossSalesRankData = params.grossSalesRankData;
                                    String docBKey = doc['h_key'].size() == 0 ? '' : doc['h_key'].value;
                                    if (grossSalesRankData.containsKey(docBKey)) return grossSalesRankData.get(docBKey);
                                    return null;
                                `,
                            ).params({
                                grossSalesRankData,
                            }),
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.in_transit_stock.key,
                    buildQuery: () =>
                        nestedAggregation('in_transit_stock', 'stock_metrics').agg(
                            filterAggregation(
                                'filtered_by_date',
                                boolQuery().must([
                                    rangeQuery('stock_metrics.date').gte(totalInTransitStock[fromSource]),
                                ]),
                            ).agg(sumAggregation('in_transit_stock', 'stock_metrics.in_transit')),
                        ),
                },
            ],
            filterAgg,
            sortAgg,
            metadataAgg,
        });

        // Main query
        const query = requestBodySearch().size(0);

        filterQuery.filter(esb.termQuery('type', 'simple'));

        if (ids) filterQuery.filter(termsQuery('h_key', ids));

        query.query(filterQuery);

        query.agg(itemsAgg);

        return query;
    },
};

const buildMetadataAgg = (fields: string[]) => {
    const metadataKeys: string[] = [];

    fields.forEach((field) => {
        if (SalesByVariantMetadataFields.includes(field as SalesByVariantFields)) {
            metadataKeys.push(field);
        }
    });

    if (metadataKeys.length) {
        return new esb.TopHitsAggregation('metadata').size(1).source(metadataKeys);
    } else {
        return new esb.TopHitsAggregation('metadata').size(1).source(SalesByVariantMetadataFields);
    }
};

export const SalesMetricsAggregation: AggregationItem = {
    key: 'sales_metrics',
    children: [
        {
            key: SalesByVariantFieldSchema.gross_sales.key,
            buildQuery: () => sumAggregation('gross_sales', 'sales_metrics.gross_sales').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.gross_qty.key,
            buildQuery: () => sumAggregation('gross_qty', 'sales_metrics.gross_qty').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.net_sales.key,
            buildQuery: () => sumAggregation('net_sales', 'sales_metrics.net_sales').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.net_qty.key,
            buildQuery: () => sumAggregation('net_qty', 'sales_metrics.net_qty').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.total_sales.key,
            buildQuery: () => sumAggregation('total_sales', 'sales_metrics.total_sales').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.return_value.key,
            buildQuery: () => sumAggregation('return_value', 'sales_metrics.return_value').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.return_qty.key,
            buildQuery: () => sumAggregation('return_qty', 'sales_metrics.return_qty').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.discount.key,
            buildQuery: () => sumAggregation('discount', 'sales_metrics.discount').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.tax.key,
            buildQuery: () => sumAggregation('tax', 'sales_metrics.tax').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.shipping.key,
            buildQuery: () => sumAggregation('shipping', 'sales_metrics.shipping').missing('0'),
        },
        {
            key: 'to_parent',
            children: [
                {
                    key: SalesByVariantFieldSchema.gross_profit.key,
                    buildQuery: (options) => {
                        const { fromDate, toDate } = options;

                        return sumAggregation(SalesByVariantFieldSchema.gross_profit.key).script(
                            script(
                                'source',
                                `
                                double costPerItem = doc.containsKey('cost') && !doc['cost'].empty ? doc['cost'].value : 0;
                                double totalNetSales = 0;
                                double totalNetQty = 0;
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                                LocalDate startDate = LocalDate.parse("${fromDate}", formatter);
                                LocalDate endDate = LocalDate.parse("${toDate}", formatter);

                                for (def entry : params._source['sales_metrics']) {
                                    if ( entry['h_warehouse_key'] == null) {
                                        LocalDate docDate = LocalDate.parse(entry['date'], formatter);

                                        if ((docDate.isEqual(startDate) || docDate.isAfter(startDate)) &&
                                            (docDate.isEqual(endDate) || docDate.isBefore(endDate))) {
                                            totalNetSales += entry['net_sales'];
                                            totalNetQty += entry['net_qty'];
                                        }
                                    }
                                }
                                return totalNetSales - (costPerItem * totalNetQty);
                                `,
                            ),
                        );
                    },
                },
            ],
            buildQuery: (options, children) => {
                return buildAggregation({
                    aggregation: reverseNestedAggregation('to_parent'),
                    options,
                    children,
                });
            },
        },
        {
            key: SalesByVariantFieldSchema.sales_over_time.key,
            buildQuery: (options) => {
                const { timeFrame } = options;
                return dateHistogramAggregation('sales_over_time', 'sales_metrics.date')
                    .calendarInterval(timeFrame)
                    .agg(sumAggregation('gross_sales', 'sales_metrics.gross_sales'))
                    .agg(
                        bucketScriptAggregation('gross_sales_rounded').bucketsPath({
                            value: 'gross_sales',
                        }).script(`
                            if (params.value == 0 || params.value == null) {
                                return 0;
                            } else {
                                return Math.round(params.value * 100) / 100.00;
                            }
                        `),
                    )
                    .agg(sumAggregation('gross_qty', 'sales_metrics.gross_qty'))
                    .agg(
                        bucketScriptAggregation('gross_qty_rounded').bucketsPath({
                            value: 'gross_qty',
                        }).script(`
                            if (params.value == 0 || params.value == null) {
                                return 0;
                            } else {
                                return Math.round(params.value * 100) / 100.00;
                            }
                        `),
                    );
            },
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate, channelId } = options;

        const aggDateTime = rangeQuery('sales_metrics.date');
        if (fromDate && toDate) aggDateTime.gte(fromDate).lte(toDate);

        const queries = channelId ? [aggDateTime, termsQuery('channel_id', channelId)] : [aggDateTime];

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation(
                'nested',
                boolQuery()
                    .must([...queries])
                    .mustNot([existsQuery('sales_metrics.h_warehouse_key')]),
            ),
            options,
            children,
        });

        const query = nestedAggregation('sales_metrics', 'sales_metrics').agg(nestedQuery);

        return query;
    },
};

const ForecastMetricsAggregation: AggregationItem = {
    key: 'forecast_metrics',
    children: [
        {
            key: 'nested',
            children: [
                {
                    key: SalesByVariantFieldSchema.forecast_value.key,
                    buildQuery: () =>
                        sumAggregation('forecast_value', 'forecast_metrics.forecast_value').missing('0'),
                },
                {
                    key: SalesByVariantFieldSchema.forecast_sales_per_day.key,
                    buildQuery: () =>
                        sumAggregation(
                            'forecast_sales_per_day',
                            'forecast_metrics.forecast_sales_per_day',
                        ).missing('0'),
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_gross_qty.key,
                    buildQuery: () =>
                        sumAggregation('forecasted_gross_qty', 'forecast_metrics.forecast_value').missing(
                            '0',
                        ),
                },
                {
                    key: SalesByVariantFieldSchema.forecast_over_time.key,
                    buildQuery: (options) => {
                        const { timeFrame } = options;
                        return dateHistogramAggregation('forecast_over_time', 'forecast_metrics.date')
                            .calendarInterval(timeFrame)
                            .agg(sumAggregation('forecasted_gross_qty', 'forecast_metrics.forecast_value'))
                            .agg(
                                bucketScriptAggregation('forecasted_gross_qty_rounded').bucketsPath({
                                    value: 'forecasted_gross_qty',
                                }).script(`
                                    if (params.value == 0 || params.value == null) {
                                        return 0;
                                    } else {
                                        return Math.ceil(params.value);
                                    }
                                `),
                            );
                    },
                },
            ],
            buildQuery: (options, children) => {
                const { forecastFromDate, forecastToDate } = options;
                const { forecastDateRanges = [] } = options.metadata ?? {};

                // If forecastFromDate or forecastToDate is not provided
                if (!forecastFromDate || !forecastToDate) {
                    const currentDate = new Date().toISOString().split('T')[0];
                    const defaultToDate = new Date();
                    defaultToDate.setDate(defaultToDate.getDate() + 45 + 90); // Default leadtime (45) + default dayOfStock (90) + 1 day
                    const defaultToDateStr = defaultToDate.toISOString().split('T')[0];

                    // Build a boolean query with OR conditions for each date range
                    const shouldClauses: BoolQuery[] = [];

                    // Check if forecastDateRanges is available
                    if (forecastDateRanges && forecastDateRanges.length > 0) {
                        // Collect all h_keys that have specific date ranges
                        const allSpecificHKeys: string[] = [];

                        // Process each date range
                        forecastDateRanges.forEach(
                            (range: { fromDate: string; toDate: string; h_keys: string[]; key: string }) => {
                                if (range.h_keys.length > 0) {
                                    // Add these h_keys to our collection of all specific h_keys
                                    allSpecificHKeys.push(...range.h_keys);

                                    // This is a specific range for a group of items
                                    shouldClauses.push(
                                        boolQuery().must([
                                            termQuery(
                                                SalesByVariantFieldPathMap.forecast_metrics_channel_id,
                                            ).value('0'),
                                            rangeQuery(SalesByVariantFieldPathMap.forecast_metrics_date)
                                                .gte(range.fromDate)
                                                .lte(range.toDate),
                                            termsQuery(
                                                SalesByVariantFieldPathMap.forecast_metrics_h_key,
                                                range.h_keys,
                                            ),
                                        ]),
                                    );
                                }
                            },
                        );

                        shouldClauses.push(
                            boolQuery().must([
                                termQuery(SalesByVariantFieldPathMap.forecast_metrics_channel_id).value('0'),
                                rangeQuery(SalesByVariantFieldPathMap.forecast_metrics_date)
                                    .gte(currentDate)
                                    .lte(defaultToDateStr),
                                boolQuery().mustNot([
                                    termsQuery(
                                        SalesByVariantFieldPathMap.forecast_metrics_h_key,
                                        allSpecificHKeys,
                                    ),
                                ]),
                            ]),
                        );
                    } else {
                        shouldClauses.push(
                            boolQuery().must([
                                termQuery(SalesByVariantFieldPathMap.forecast_metrics_channel_id).value('0'),
                                rangeQuery(SalesByVariantFieldPathMap.forecast_metrics_date)
                                    .gte(currentDate)
                                    .lte(defaultToDateStr),
                            ]),
                        );
                    }

                    return buildAggregation({
                        aggregation: filterAggregation(
                            'nested',
                            boolQuery().should(shouldClauses).minimumShouldMatch(1),
                        ),
                        options,
                        children,
                    });
                } else {
                    // If forecastFromDate and forecastToDate are provided, use them directly
                    return buildAggregation({
                        aggregation: filterAggregation(
                            'nested',
                            boolQuery().must([
                                termQuery(SalesByVariantFieldPathMap.forecast_metrics_channel_id).value('0'),
                                rangeQuery(SalesByVariantFieldPathMap.forecast_metrics_date)
                                    .gte(forecastFromDate!)
                                    .lte(forecastToDate!),
                            ]),
                        ),
                        options,
                        children,
                    });
                }
            },
        },
        {
            key: 'to_parent',
            children: [
                {
                    key: SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_stock.key,
                    buildQuery: () => {
                        const currentDate = new Date().toISOString().split('T')[0];

                        return sumAggregation(
                            SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_stock.key,
                        ).script(
                            script(
                                'source',
                                `
if (doc['type'].value != 'simple') return 0;
double remainingStock = doc.containsKey('stock') && !doc['stock'].empty ? doc['stock'].value : 0;
if(remainingStock <= 0) return 0;
String outOfStockDate = null;
for (def entry : params._source['forecast_metrics']) {
    if (entry['date'].compareTo('${currentDate}') >= 0 && entry['channel_id'] == '0') {
        remainingStock -= entry['forecast_value'];
        if (remainingStock <= 0) {
            outOfStockDate = entry['date'];
            break;
        }
    }
}
if(remainingStock > 0) return 365;
if (outOfStockDate != null) {
    long todayMillis = ZonedDateTime.parse("${currentDate}T00:00:00Z").toInstant().toEpochMilli();
    long outOfStockMillis = ZonedDateTime.parse(outOfStockDate + "T00:00:00Z").toInstant().toEpochMilli();
    long daysRemaining = (outOfStockMillis - todayMillis) / (1000 * 60 * 60 * 24);
    return daysRemaining;
}
return null;
                                `,
                            ),
                        );
                    },
                },
                {
                    key: SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_ats.key,
                    buildQuery: (options) => {
                        const currentDate = new Date().toISOString().split('T')[0];
                        const { bKeyToOnOrder } = options.metadata ?? {};

                        return sumAggregation(
                            SalesByVariantFieldSchema.forecasted_sales_days_remaining_based_on_ats.key,
                        ).script(
                            script(
                                'source',
                                `
if (doc['type'].value != 'simple') return 0;
Map bKeyToOnOrder = params.bKeyToOnOrder;
String docBKey = doc['b_key'].size() == 0 ? '' : doc['b_key'].value;
long onOrder = 0;
if (doc['type'].value == 'simple' && bKeyToOnOrder.containsKey(docBKey))
    onOrder = bKeyToOnOrder.get(docBKey);
double remainingStock = doc.containsKey('stock') && !doc['stock'].empty ? doc['stock'].value + onOrder : 0;
if(remainingStock <= 0) return 0;
String outOfStockDate = null;
for (def entry : params._source['forecast_metrics']) {
    if (entry['date'].compareTo('${currentDate}') >= 0 && entry['channel_id'] == '0') {
        remainingStock -= entry['forecast_value'];
        if (remainingStock <= 0) {
            outOfStockDate = entry['date'];
            break;
        }
    }
}
if(remainingStock > 0) return 365;
if (outOfStockDate != null) {
    long todayMillis = ZonedDateTime.parse("${currentDate}T00:00:00Z").toInstant().toEpochMilli();
    long outOfStockMillis = ZonedDateTime.parse(outOfStockDate + "T00:00:00Z").toInstant().toEpochMilli();
    long daysRemaining = (outOfStockMillis - todayMillis) / (1000 * 60 * 60 * 24);
    return daysRemaining;
}
return null;
                                `,
                            ).params({
                                bKeyToOnOrder,
                            }),
                        );
                    },
                },
            ],
            buildQuery: (options, children) => {
                return buildAggregation({
                    aggregation: reverseNestedAggregation('to_parent'),
                    options,
                    children,
                });
            },
        },
    ],
    buildQuery: (options, children) => {
        return buildAggregation({
            aggregation: nestedAggregation('forecast_metrics', 'forecast_metrics'),
            options,
            children,
        });
    },
};

export const AdsMetricsAggregation: AggregationItem = {
    key: 'ads_metrics',
    children: [
        {
            key: SalesByVariantFieldSchema.ad_spends.key,
            buildQuery: () => sumAggregation('ad_spends', 'ads_metrics.ad_spends').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.clicks.key,
            buildQuery: () => sumAggregation('clicks', 'ads_metrics.clicks').missing('0'),
        },
        {
            key: SalesByVariantFieldSchema.views.key,
            buildQuery: () => sumAggregation('views', 'ads_metrics.views').missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate } = options;

        const filterQuery = boolQuery();
        if (fromDate && toDate)
            filterQuery.must([
                rangeQuery(SalesByVariantFieldPathMap.ads_metrics_date).gte(fromDate).lte(toDate),
            ]);

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery),
            options,
            children,
        });

        const query = nestedAggregation('ads_metrics', 'ads_metrics').agg(nestedQuery);

        return query;
    },
};

export const CollecttionsAggregation: AggregationItem = {
    key: 'collections',
    children: [
        {
            key: SalesByVariantFieldSchema.collection_position.key,
            buildQuery: () => minAggregation('collection_position', 'collections.position').missing('0'),
        },
    ],
    buildQuery: (options, children) => {
        const filter = options.filter as any;
        let filterAgg: Optional<BucketSelectorAggregation>;
        const filterQuery = boolQuery().must([]);

        const filtered = { collections_h_key: filter?.collections_h_key };

        if (filtered) {
            const queyAndFilterData = buildQueryAndFilterAgg({
                filter,
                fieldSchema: SalesByVariantFieldSchema,
                searchFields: SalesByVariantSearchFields,
            });
            filterAgg = queyAndFilterData.filterAgg;
        }

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', filterQuery), // Set max to unlimit bucket to get
            options,
            children,
            filterAgg,
        });

        const query = nestedAggregation('collections', 'collections').agg(nestedQuery);

        return query;
    },
};

export const StockMetricsAggregation: AggregationItem = {
    key: 'stock_metrics',
    children: [
        {
            key: SalesByVariantFieldSchema.days_in_stock_over_time.key,
            buildQuery: (options) => {
                const { timeFrame } = options;
                return dateHistogramAggregation('days_in_stock_over_time', 'stock_metrics.date')
                    .calendarInterval(timeFrame)
                    .agg(cardinalityAggregation('days_in_stock', 'stock_metrics.date'));
            },
        },
    ],
    buildQuery: (options, children) => {
        const { fromDate, toDate, channelId } = options;

        const aggDateTime = rangeQuery('stock_metrics.date');
        if (fromDate && toDate) aggDateTime.gte(fromDate).lte(toDate);

        const aggStock = rangeQuery('stock_metrics.stock').gte(0);
        const queries = [aggDateTime, aggStock];

        const nestedQuery = buildAggregation({
            aggregation: filterAggregation('nested', boolQuery().must([...queries])),
            options,
            children,
        });

        const query = nestedAggregation('stock_metrics', 'stock_metrics').agg(nestedQuery);

        return query;
    },
};
