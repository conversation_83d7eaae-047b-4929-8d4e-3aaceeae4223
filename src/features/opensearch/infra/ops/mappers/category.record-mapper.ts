import { CategoryDTO } from '@features/opensearch/domain';
import { StringUtil } from '@utils';
import { BaseRecordMapper, IRecordMapper } from '../../../../../core';
import { CategoryRecord } from '../records';

export type ICategoryRecordMapper = IRecordMapper<CategoryDTO, CategoryRecord>;
export class CategoryRecordMapper extends BaseRecordMapper implements ICategoryRecordMapper {
    fromRecordToDto(record: Partial<CategoryRecord>): Partial<CategoryDTO> {
        const data = record._source;
        if (!data) return {};

        const dto: Partial<CategoryDTO> = {
            h_key: data.h_key,
            from_source: data.from_source,
            b_key: data.b_key,
            name: StringUtil.escapePipe(data.name),
        };

        return dto;
    }
}
