import { StringUtil } from '@utils';
import { <PERSON><PERSON><PERSON>ordMapper, IRecordMapper } from '../../../../../core';
import { SalesByVariantDTO } from '../../../domain';
import { SalesByVariantRecord } from '../records';

export type ISalesByVariantRecordMapper = IRecordMapper<SalesByVariantDTO, SalesByVariantRecord>;
export class SalesByVariantRecordMapper extends BaseRecordMapper implements ISalesByVariantRecordMapper {
    fromRecordToDto(record: Partial<SalesByVariantRecord>): Partial<SalesByVariantDTO> {
        const metadata = record.metadata?.hits.hits ? record.metadata?.hits.hits[0]._source : undefined;

        const dto: Partial<SalesByVariantDTO> = {
            h_key: record.key,

            // metadata
            from_source: metadata?.from_source,
            b_key: metadata?.b_key,
            variant_id: metadata?.variant_id,
            parent_h_key: metadata?.parent_h_key,
            type: metadata?.type,
            sku: metadata?.sku,
            name: StringUtil.escapePipe(metadata?.name),
            product_id: metadata?.product_id,
            product_h_key: metadata?.product_h_key,
            default_color: metadata?.default_color,
            barcode: metadata?.barcode,
            status: metadata?.status,
            replenishable: record?.replenishable?.value === 1 ? true : false,
            price: metadata?.price,
            compare_at_price: metadata?.compare_at_price,
            image: metadata?.image,
            size: metadata?.size,
            color: metadata?.color,
            trend: metadata?.trend,
            sale_pattern: metadata?.sale_pattern,
            forecast_confidence: metadata?.forecast_confidence,
            categories: metadata?.categories ?? [],
            collections: metadata?.collections ?? [],
            tags: metadata?.tags ?? [],
            vendors: metadata?.vendors ?? [],
            days_on_site: record.days_on_site?.value,
            cost_per_item: record?.cost_per_item?.value,
            stock: record.stock?.value,
            on_order: record.on_order?.value,
            ats: record.ats?.value,
            product_grade:
                record.product_grade?.value === 1 ? 'A' : record.product_grade?.value === 2 ? 'B' : 'C',
            collection_position: record.collection_position?.value,

            // sales_metrics
            gross_sales: record?.gross_sales?.value,
            gross_qty: record?.gross_qty?.value,
            net_sales: record?.net_sales?.value,
            net_qty: record?.net_qty?.value,
            total_sales: record?.total_sales?.value,
            return_value: record?.return_value?.value,
            return_qty: record?.return_qty?.value,
            discount: record?.discount?.value,
            tax: record?.tax?.value,
            shipping: record?.shipping?.value,
            sales_per_day: record?.sales_per_day?.value,
            return_rate: record.return_rate?.value,
            sell_through: record.sell_through?.value,
            sales_days_left: record.sales_days_left?.value,
            gross_profit: record.gross_profit?.value,
            gross_margin: record.gross_margin?.value,
            total_inventory_value_stock: record.total_inventory_value_stock?.value,
            total_inventory_value_ats: record.total_inventory_value_ats?.value,
            wos: record.wos?.value,
            re_order_qty: record.re_order_qty?.value,
            rop: record.rop?.value,
            lead_time: record.lead_time?.value,
            days_of_stock: record.days_of_stock?.value,
            sales_over_time: record.sales_metrics?.nested.sales_over_time?.buckets.map((bucket) => ({
                period_start: bucket.key_as_string,
                gross_sales: bucket.gross_sales_rounded?.value,
                gross_qty: bucket.gross_qty_rounded?.value,
            })),

            // forecast_metrics
            forecast_value: record?.forecast_value?.value,
            forecast_sales_per_day: record?.forecast_sales_per_day?.value,
            forecasted_gross_qty: record?.forecasted_gross_qty?.value,
            forecasted_gross_sales: record?.forecasted_gross_sales?.value,
            forecasted_sales_days_remaining_based_on_stock:
                record?.forecasted_sales_days_remaining_based_on_stock?.value,
            forecasted_sales_days_remaining_based_on_ats:
                record?.forecasted_sales_days_remaining_based_on_ats?.value,
            forecast_over_time: record.forecast_metrics?.nested.forecast_over_time?.buckets.map((bucket) => ({
                period_start: bucket.key_as_string,
                forecasted_gross_qty: bucket.forecasted_gross_qty_rounded?.value,
            })),

            // ads_metrics
            ad_spends: record?.ad_spends?.value,
            clicks: record?.clicks?.value,
            views: record?.views?.value,
            conversion_rate: record?.conversion_rate?.value,
            stock_percentage: record?.stock_percentage?.value,
            gross_sales_percentage: record?.gross_sales_percentage?.value,
            gross_sales_rank: record?.gross_sales_rank?.value,

            // stock_metrics
            days_in_stock_over_time: record.stock_metrics?.nested.days_in_stock_over_time?.buckets.map(
                (bucket) => ({
                    period_start: bucket.key_as_string,
                    days_in_stock: bucket.days_in_stock.value,
                }),
            ),

            // in_transit_stock
            in_transit_stock: record?.in_transit_stock?.filtered_by_date.in_transit_stock?.value,
        };

        return dto;
    }
}
