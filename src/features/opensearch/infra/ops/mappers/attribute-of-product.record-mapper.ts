import { IR<PERSON>ordMapper, BaseRecordMapper } from '../../../../../core';
import { AttributesOfProductDTO } from '../../../domain';
import { AttributesOfProductRecord } from '../records';

export type IAttributesOfProductRecordMapper = IRecordMapper<
    AttributesOfProductDTO,
    AttributesOfProductRecord
>;
export class AttributesOfProductRecordMapper
    extends BaseRecordMapper
    implements IAttributesOfProductRecordMapper
{
    fromRecordToDto(record: Partial<AttributesOfProductRecord>): Partial<AttributesOfProductDTO> {
        const attributes = record.attributes?.hits.hits ? record.attributes?.hits.hits[0]._source : undefined;
        const dto: Partial<AttributesOfProductDTO> = {
            attribute_value: record.key,
            product_id: attributes?.product_id,
            categories: attributes?.categories,
            gross_sales: record?.gross_sales?.value,
            gross_qty: record?.gross_qty?.value,
            return_value: record?.return_value?.value,
            return_qty: record?.return_qty?.value,
            return_rate: record?.return_rate?.value,
            net_sales: record?.net_sales?.value,
            net_qty: record?.net_qty?.value,
            total_sales: record?.total_sales?.value,
            sales_per_day: record?.sales_per_day?.value,
            sales_day_left: record?.sales_day_left?.value,
            sell_through: record?.sell_through?.value,
            tax: record?.tax?.value,
            discount: record?.discount?.value,
            shipping: record?.shipping?.value,
            views: record?.ads_metrics?.nested.views?.value,
            stock: record?.stock?.value,
            on_order: record?.on_order?.value,
            ats: record?.ats?.value,
            total_inventory_value_ats: record?.total_inventory_value_ats?.value,
            price: record.price?.value,
            name: attributes?.name,
            image: attributes?.image,

            rop: record.rop?.value,
            conversion_rate: record.conversion_rate?.value,
            gross_sales_percentage: record.gross_sales_percentage,
            forecasted_gross_qty: record.forecasted_gross_qty?.value,
            re_order_qty: record.re_order_qty?.value,
            gross_profit: record.gross_profit?.value,
            gross_margin: record.gross_margin?.value,
            wos: record.wos?.value,
            forecast_sales_per_day: record.forecast_sales_per_day?.value,
            stock_percentage: record.stock_percentage,
        };

        return dto;
    }
}
