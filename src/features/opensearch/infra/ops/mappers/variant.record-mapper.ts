import { VariantDTO } from '@features/opensearch/domain';
import { StringUtil } from '@utils';
import { BaseR<PERSON>ordMapper, IRecordMapper } from '../../../../../core';
import { VariantRecord } from '../records';

export type IVariantRecordMapper = IRecordMapper<VariantDTO, VariantRecord>;
export class VariantRecordMapper extends BaseRecordMapper implements IVariantRecordMapper {
    fromRecordToDto(record: Partial<VariantRecord>): Partial<VariantDTO> {
        const data = record._source;
        if (!data) return {};

        const dto: Partial<VariantDTO> = {
            h_key: data.h_key,
            from_source: data.from_source,
            parent_h_key: data.parent_h_key,
            b_key: data.b_key,
            variant_id: data.variant_id,
            product_id: data.product_id,
            type: data.type,
            name: StringUtil.escapePipe(data.name),
            sku: data.sku,
            barcode: data.barcode,
            cost_per_item: data.cost_per_item,
            price: data.price,
            compare_at_price: data.compare_at_price,
            image: data.image,
            color: data.color,
            default_color: data.default_color,
            size: data.size,
            status: data.status,
            published_at: data.published_at,
            categories: data.categories,
            collections: data.collections,
            tags: data.tags,
            vendors: data.vendors,
        };

        return dto;
    }
}
