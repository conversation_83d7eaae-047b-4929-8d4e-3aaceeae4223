import { BaseR<PERSON>ordMapper, IRecordMapper } from '../../../../../core';
import { SalesByCategorySummaryDTO } from '../../../domain';
import { CategorySummaryRecord } from '../records';

export type ICategorySummaryRecordMapper = IRecordMapper<SalesByCategorySummaryDTO, CategorySummaryRecord>;
export class CategorySummaryRecordMapper extends BaseRecordMapper implements ICategorySummaryRecordMapper {
    fromRecordToDto(record: Partial<CategorySummaryRecord>): Partial<SalesByCategorySummaryDTO> {
        const gross_sales = record?.sales_metrics?.nested?.gross_sales?.value;
        const gross_qty = record?.sales_metrics?.nested?.gross_qty?.value;
        const net_sales = record?.sales_metrics?.nested?.net_sales?.value;
        const net_qty = record?.sales_metrics?.nested?.net_qty?.value;
        const total_sales = record?.sales_metrics?.nested?.total_sales?.value;
        const return_value = record?.sales_metrics?.nested?.return_value?.value;
        const return_qty = record?.sales_metrics?.nested?.return_qty?.value;

        const ad_spends = record?.ads_metrics?.nested?.ad_spends?.value;
        const clicks = record?.ads_metrics?.nested?.clicks?.value;
        const views = record?.ads_metrics?.nested?.views?.value;

        const stock = record.stock?.value;
        const on_order = record.on_order?.value;
        const total_inventory_value_stock = record?.total_inventory_value_stock?.value;
        const total_inventory_value_ats = record?.total_inventory_value_ats?.value;

        const dto: Partial<SalesByCategorySummaryDTO> = {
            gross_sales: gross_sales ? Math.round(gross_sales * 100) / 100 : gross_sales,
            gross_qty: gross_qty ? Math.round(gross_qty * 100) / 100 : gross_qty,
            net_sales: net_sales ? Math.round(net_sales * 100) / 100 : net_sales,
            net_qty: net_qty ? Math.round(net_qty * 100) / 100 : net_qty,
            total_sales: total_sales ? Math.round(total_sales * 100) / 100 : total_sales,
            return_value: return_value ? Math.round(return_value * 100) / 100 : return_value,
            return_qty: return_qty ? Math.round(return_qty * 100) / 100 : return_qty,
            ad_spends: ad_spends ? Math.round(ad_spends * 100) / 100 : ad_spends,
            clicks: clicks ? Math.round(clicks * 100) / 100 : clicks,
            views: views ? Math.round(views * 100) / 100 : views,
            stock: stock ? Math.round(stock * 100) / 100 : stock,
            on_order: on_order ? Math.round(on_order * 100) / 100 : on_order,
            total_inventory_value_stock: total_inventory_value_stock
                ? Math.round(total_inventory_value_stock * 100) / 100
                : total_inventory_value_stock,
            total_inventory_value_ats: total_inventory_value_ats
                ? Math.round(total_inventory_value_ats * 100) / 100
                : total_inventory_value_ats,
        };

        return dto;
    }
}
