import { CollectionDTO } from '@features/opensearch/domain';
import { StringUtil } from '@utils';
import { BaseRecordMapper, IRecordMapper } from '../../../../../core';
import { CollectionRecord } from '../records';

export type ICollectionRecordMapper = IRecordMapper<CollectionDTO, CollectionRecord>;
export class CollectionRecordMapper extends BaseRecordMapper implements ICollectionRecordMapper {
    fromRecordToDto(record: Partial<CollectionRecord>): Partial<CollectionDTO> {
        const data = record._source;
        if (!data) return {};

        const dto: Partial<CollectionDTO> = {
            h_key: data.h_key,
            from_source: data.from_source,
            b_key: data.b_key,
            name: StringUtil.escapePipe(data.name),
        };

        return dto;
    }
}
