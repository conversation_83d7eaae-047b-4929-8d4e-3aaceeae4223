import { StringUtil } from '@utils';
import { <PERSON><PERSON><PERSON>ord<PERSON>apper, IRecordMapper } from '../../../../../core';
import { SalesByProductDTO } from '../../../domain';
import { SalesByProductRecord } from '../records';

export type ISalesByProductRecordMapper = IRecordMapper<SalesByProductDTO, SalesByProductRecord>;
export class SalesByProductRecordMapper extends BaseRecordMapper implements ISalesByProductRecordMapper {
    fromRecordToDto(record: Partial<SalesByProductRecord>): Partial<SalesByProductDTO> {
        const metadata = record?.product_data?.metadata?.hits?.hits[0]?._source;
        const dto: Partial<SalesByProductDTO> = {
            // metadata
            b_key: metadata?.b_key,
            barcode: metadata?.barcode,
            categories: metadata?.categories ?? [],
            collections: metadata?.collections ?? [],
            color: metadata?.color,
            default_color: metadata?.default_color,
            compare_at_price: metadata?.compare_at_price,
            cost_per_item: metadata?.cost,
            dda_code: metadata?.dda_code,
            dda_value: metadata?.dda_value,
            from_source: metadata?.from_source,
            h_key: metadata?.h_key ?? record.key,
            image: metadata?.image,
            name: StringUtil.escapePipe(metadata?.name),
            price: record.price?.value,
            product_h_key: metadata?.product_h_key,
            product_id: metadata?.product_id,
            replenishable: record?.replenishable?.value === 1 ? true : false,
            size: metadata?.size,
            sku: metadata?.sku,
            status: metadata?.status,
            tags: metadata?.tags ?? [],
            type: metadata?.type,
            vendors: metadata?.vendors ?? [],
            collection_position: record.collection_position?.value,

            days_on_site: record.days_on_site?.value,
            on_order: record.on_order?.value,
            source_created_at: record.source_created_at?.value
                ? new Date(record.source_created_at.value).toISOString()
                : undefined,
            stock: record.stock?.value,
            product_grade:
                record.product_grade?.value === 1 ? 'A' : record.product_grade?.value === 2 ? 'B' : 'C',
            gross_sales_rank: record.gross_sales_rank?.value,
            // sales metrics
            ats: record.ats?.value,
            discount: record.discount?.value,
            gross_qty: record.gross_qty?.value,
            gross_sales: record.gross_sales?.value,
            net_qty: record.net_qty?.value,
            net_sales: record.net_sales?.value,
            return_qty: record.return_qty?.value,
            return_rate: record.return_rate?.value,
            return_value: record.return_value?.value,
            sales_per_day: record.sales_per_day?.value,
            shipping: record.shipping?.value,
            tax: record.tax?.value,
            total_sales: record.total_sales?.value,
            sales_days_left: record.sales_days_left?.value,
            gross_profit: record.gross_profit?.value,
            gross_margin: record.gross_margin?.value,

            total_inventory_value_stock: record.total_inventory_value_stock?.value,
            total_inventory_value_ats: record.total_inventory_value_ats?.value,
            wos: record.wos?.value,
            stock_percentage: record.stock_percentage?.value,
            re_order_qty: record.re_order_qty?.value,
            rop: record.rop?.value,
            sell_through: record.sell_through?.value,
            lead_time: record.lead_time?.value,
            days_of_stock: record.days_of_stock?.value,
            gross_sales_percentage: record.gross_sales_percentage?.value,

            // forecast metrics
            trend: metadata?.trend,
            sale_pattern: metadata?.sale_pattern,
            forecast_confidence: metadata?.forecast_confidence,
            forecast_sales_per_day: record.forecast_sales_per_day?.value,
            forecast_value: record.forecast_value?.value,
            forecasted_gross_qty: record.forecasted_gross_qty?.value,
            forecasted_gross_sales: record.forecasted_gross_sales?.value,
            forecasted_sales_days_remaining_based_on_stock:
                record.forecasted_sales_days_remaining_based_on_stock?.value,
            forecasted_sales_days_remaining_based_on_ats:
                record.forecasted_sales_days_remaining_based_on_ats?.value,

            // ads metrics
            ad_spends: record.ad_spends?.value,
            clicks: record.clicks?.value,
            views: record.views?.value,
            conversion_rate: record?.conversion_rate?.value,
        };

        return dto;
    }
}
