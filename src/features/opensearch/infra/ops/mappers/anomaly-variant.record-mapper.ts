import { <PERSON><PERSON><PERSON>ord<PERSON><PERSON><PERSON>, BaseRecordMapper } from '../../../../../core';
import { AnomalyVariantDTO } from '../../../domain';
import { AnomalyVariantRecord } from '../records';

export type IAnomalyVariantRecordMapper = IRecordMapper<AnomalyVariantDTO, AnomalyVariantRecord>;
export class AnomalyVariantRecordMapper extends BaseRecordMapper implements IAnomalyVariantRecordMapper {
    fromRecordToDto(record: Partial<AnomalyVariantRecord>): Partial<AnomalyVariantDTO> {
        const metadata = record.metadata?.hits.hits ? record.metadata?.hits.hits[0]._source : undefined;

        const dto: Partial<AnomalyVariantDTO> = {
            h_key: record.key,

            // metadata
            from_source: metadata?.from_source,
            b_key: metadata?.b_key,
            variant_id: metadata?.variant_id,
            parent_h_key: metadata?.parent_h_key,
            type: metadata?.type,
            sku: metadata?.sku,
            name: metadata?.name,
            barcode: metadata?.barcode,
            status: metadata?.status,
            replenishable: metadata?.replenishable,
            price: metadata?.price,
            stock: metadata?.stock,
            size: metadata?.size,

            // sales_metrics
            gross_sales: record?.gross_sales?.value,
            compared_gross_sales: record?.compared_gross_sales?.value,
            gross_sales_delta: record?.gross_sales_delta?.value,

            // ads_metrics
            views: record?.views?.value,
            compared_views: record?.compared_views?.value,
            views_delta: record?.views_delta?.value,
            ad_spends: record?.ad_spends?.value,
            compared_ad_spends: record?.compared_ad_spends?.value,
            ad_spends_delta: record?.ad_spends_delta?.value,
        };

        return dto;
    }
}
