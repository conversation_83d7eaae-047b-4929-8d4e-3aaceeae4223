import { StringUtil } from '@utils';
import { <PERSON><PERSON><PERSON>ord<PERSON>apper, IRecordMapper } from '../../../../../core';
import { VariantDTO } from '../../../domain';
import { ProductRecord } from '../records';

export type IProductRecordMapper = IRecordMapper<VariantDTO, ProductRecord>;
export class ProductRecordMapper extends BaseRecordMapper implements IProductRecordMapper {
    fromRecordToDto(record: Partial<ProductRecord>): Partial<VariantDTO> {
        const data = record._source;
        if (!data) return {};

        const dto: Partial<VariantDTO> = {
            h_key: data.h_key,
            from_source: data.from_source,
            b_key: data.b_key,
            product_id: data.product_id,
            type: data.type,
            name: StringUtil.escapePipe(data.name),
            cost_per_item: data.cost_per_item,
            price: data.price,
            compare_at_price: data.compare_at_price,
            image: data.image,
            status: data.status,
            published_at: data.published_at,
            categories: data.categories,
            collections: data.collections,
            tags: data.tags,
            vendors: data.vendors,
        };

        return dto;
    }
}
