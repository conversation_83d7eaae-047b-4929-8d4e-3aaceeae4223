import { SalesByCollectionDto, SalesByCollectionRecord } from '@features/opensearch';
import { StringUtil } from '@utils';
import { BaseRecordMapper, IRecordMapper } from '../../../../../core';

export type ISalesByCollectionRecordMapper = IRecordMapper<SalesByCollectionDto, SalesByCollectionRecord>;
export class SalesByCollectionRecordMapper
    extends BaseRecordMapper
    implements ISalesByCollectionRecordMapper
{
    fromRecordToDto(record: Partial<SalesByCollectionRecord>): Partial<SalesByCollectionDto> {
        const dto: Partial<SalesByCollectionDto> = {
            h_key: record?.key,
            name: StringUtil.escapePipe(record?.metadata?.hits?.hits[0]?._source?.name),

            // sales metrics
            gross_sales: record?.gross_sales?.value,
            gross_qty: record?.gross_qty?.value,
            return_value: record?.return_value?.value,
            return_qty: record?.return_qty?.value,
            total_sales: record?.total_sales?.value,
            net_sales: record?.net_sales?.value,
            net_qty: record?.net_qty?.value,

            // ads metrics
            ad_spends: record?.ad_spends?.value,
            clicks: record?.clicks?.value,
            views: record?.views?.value,

            stock: record?.stock?.value,
            on_order: record?.on_order?.value,
            ats: record?.ats?.value,
            sell_through: record?.sell_through?.value,
            total_inventory_value_stock: record?.total_inventory_value_stock?.value,
            total_inventory_value_ats: record?.total_inventory_value_ats?.value,
            gross_sales_percentage: record?.gross_sales_percentage?.value,
            stock_percentage: record?.stock_percentage?.value,
        };
        return dto;
    }
}
