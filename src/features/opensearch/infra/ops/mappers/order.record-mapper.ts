import { OrderDTO, OrderItemDTO } from '@features/opensearch/domain';
import { Base<PERSON><PERSON>ord<PERSON>apper, IRecordMapper } from '../../../../../core';
import { OrderItemRecord, OrderRecord } from '../records';

export type IOrderRecordMapper = IRecordMapper<OrderDTO, OrderRecord>;

export class OrderRecordMapper extends BaseRecordMapper implements IOrderRecordMapper {
    fromRecordToDto(record: Partial<OrderRecord>): Partial<OrderDTO> {
        const data = record._source;
        if (!data) return {};

        const dto: Partial<OrderDTO> = {
            order_id: data.order_id,
            from_source: data.from_source,
            channel_id: data.channel_id,
            date: data.date,
            total_sales: parseFloat(data.total_sales || '0'),
            net_sales: parseFloat(data.net_sales || '0'),
            gross_sales: parseFloat(data.gross_sales || '0'),
            gross_qty: parseInt(data.gross_qty || '0'),
            net_qty: parseInt(data.net_qty || '0'),
            return_qty: parseInt(data.return_qty || '0'),
            return_value: parseFloat(data.return_value || '0'),
            discount: parseFloat(data.discount || '0'),
            tax: parseFloat(data.tax || '0'),
            shipping: parseFloat(data.shipping || '0'),
            unique_item_count: parseInt(data.unique_item_count || '0'),
            gross_profit: parseFloat(data.gross_profit || '0'),
            order_items: data.order_items?.map(this.mapOrderItem) || [],
        };

        return dto;
    }

    private mapOrderItem(item: OrderItemRecord): OrderItemDTO {
        return {
            h_item_key: item.h_item_key,
            item_sku: item.item_sku,
            item_name: item.item_name,
            item_categories: item.categories ?? [],
            item_price: item.item_price ? parseFloat(String(item.item_price)) : item.item_price,
            cost_per_item: item.cost_per_item ? parseFloat(String(item.cost_per_item)) : item.cost_per_item,
            tax: item.tax ? parseFloat(String(item.tax)) : item.tax,
            net_qty: item.net_qty ? parseInt(String(item.net_qty)) : item.net_qty,
            discount: item.discount ? parseFloat(String(item.discount)) : item.discount,
            shipping: item.shipping ? parseFloat(String(item.shipping)) : item.shipping,
            gross_qty: item.gross_qty ? parseInt(String(item.gross_qty)) : item.gross_qty,
            net_sales: item.net_sales ? parseFloat(String(item.net_sales)) : item.net_sales,
            return_qty: item.return_qty ? parseInt(String(item.return_qty)) : item.return_qty,
            gross_sales: item.gross_sales ? parseFloat(String(item.gross_sales)) : item.gross_sales,
            total_sales: item.total_sales ? parseFloat(String(item.total_sales)) : item.total_sales,
            return_value: item.return_value ? parseFloat(String(item.return_value)) : item.return_value,
            cost: item.cost ? parseFloat(String(item.cost)) : item.cost,
            gross_profit: item.gross_profit ? parseFloat(String(item.gross_profit)) : item.gross_profit,
        };
    }
}
