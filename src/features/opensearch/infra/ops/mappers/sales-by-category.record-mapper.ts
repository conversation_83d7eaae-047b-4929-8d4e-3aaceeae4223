import { SalesByCategoryDto, SalesByCategoryRecord } from '@features/opensearch';
import { BaseRecordMapper, IRecordMapper } from '../../../../../core';

export type ISalesByCategoryRecordMapper = IRecordMapper<SalesByCategoryDto, SalesByCategoryRecord>;
export class SalesByCategoryRecordMapper extends BaseRecordMapper implements ISalesByCategoryRecordMapper {
    fromRecordToDto(record: Partial<SalesByCategoryRecord>): Partial<SalesByCategoryDto> {
        const dto: Partial<SalesByCategoryDto> = {
            h_key: record?.key,
            name: record?.metadata?.hits?.hits[0]?._source?.name,

            // sales metrics
            gross_sales: record?.gross_sales?.value,
            gross_qty: record?.gross_qty?.value,
            return_value: record?.return_value?.value,
            return_qty: record?.return_qty?.value,
            total_sales: record?.total_sales?.value,
            net_sales: record?.net_sales?.value,
            net_qty: record?.net_qty?.value,
            gross_sales_percentage: record.gross_sales_percentage?.value,
            stock_percentage: record.stock_percentage?.value,
            sales_over_time: record?.to_root?.sales_metrics?.nested.sales_over_time?.buckets.map((item) => ({
                period_start: item.key_as_string,
                gross_sales: item.gross_sales_rounded?.value,
                gross_qty: item.gross_qty_rounded?.value,
            })),

            // ads metrics
            ad_spends: record?.ad_spends?.value,
            clicks: record?.clicks?.value,
            views: record?.views?.value,

            stock: record?.stock?.value,
            on_order: record?.on_order?.value,
            ats: record?.ats?.value,
            sell_through: record?.sell_through?.value,
            total_inventory_value_stock: record?.total_inventory_value_stock?.value,
            total_inventory_value_ats: record?.total_inventory_value_ats?.value,

            // forecast metrics
            forecasted_gross_qty: record?.to_root?.forecast_metrics?.nested.forecasted_gross_qty?.value,
            forecast_over_time: record?.to_root?.forecast_metrics?.nested.forecast_over_time?.buckets.map(
                (item) => ({
                    period_start: item.key_as_string,
                    forecasted_gross_qty: item.forecasted_gross_qty_rounded?.value,
                }),
            ),
        };
        return dto;
    }
}
