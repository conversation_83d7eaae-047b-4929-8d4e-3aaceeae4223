import { Bucket<PERSON>ilter, FieldSchema, Filter, MetadataFilter } from '@core';

export interface IFilterFactory {
    create(payload: Record<string, any[] | object>, fieldSchema: FieldSchema): Filter[];
}

export class FilterFactory implements IFilterFactory {
    create(filterBlock: Record<string, any>, fieldSchema: FieldSchema): Filter[] {
        const keys = Object.keys(filterBlock);
        const filters: (MetadataFilter | BucketFilter)[] = [];

        for (const key of keys) {
            const filterBlockItem = filterBlock[key];
            if (!filterBlockItem) continue;

            const operators = Object.keys(filterBlockItem);
            operators.forEach((operator) => {
                const filterClass = fieldSchema[key]?.filterClass;
                if (filterClass)
                    filters.push(new filterClass({ operator, value: filterBlockItem[operator] } as any));
            });
        }

        return filters;
    }
}

export const filterFactory = new FilterFactory();
