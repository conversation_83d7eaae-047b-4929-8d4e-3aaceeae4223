import { BucketFilter, FilterPayload, FilterTypes, FilterValue } from '@core';

const convertGradeToNumber = (grade: FilterValue): number => {
    switch (grade) {
        case 'A':
            return 1;
        case 'B':
            return 2;
        case 'C':
            return 3;
        default:
            return 4;
    }
};
export class ProductGradeFilter extends BucketFilter {
    constructor(payload: FilterPayload) {
        if (Array.isArray(payload.value)) {
            payload.value = payload.value.map(convertGradeToNumber);
        } else {
            payload.value = convertGradeToNumber(payload.value);
        }

        super({
            type: FilterTypes.Numeric,
            compareField: 'product_grade',
            ...payload,
        });
    }
}
