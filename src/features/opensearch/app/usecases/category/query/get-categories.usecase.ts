import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { CategoryDTO } from '@features/opensearch/domain';
import { CategoryDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetCategoriesUseCaseInput = PaginationInput<CategoryDTO>;
export type GetCategoriesUseCaseOutput = PaginationOutput<Partial<CategoryDTO>>;
export type IGetCategoriesUseCase = IUseCase<
    GetCategoriesUseCaseInput,
    GetCategoriesUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_CATEGORIES, scope: Lifecycle.Transient })
export class GetCategoriesUseCase
    extends UseCase<GetCategoriesUseCaseInput, GetCategoriesUseCaseOutput, UseCaseContext>
    implements IGetCategoriesUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.CATEGORY)
        protected readonly categoryDao: CategoryDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetCategoriesUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetCategoriesUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;

        const res = await this.categoryDao.getCategories(
            {
                ...input,
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (
        input: ResultOf<GetCategoriesUseCase, 'processing'>,
    ): Promise<GetCategoriesUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.categories,
        };
    };
}
