import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { SalesByCategoryDto } from '@features/opensearch/domain';
import { CategoryDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetSalesByCategoryUseCaseInput = PaginationInput<SalesByCategoryDto>;
export type GetSalesByCategoryUseCaseOutput = PaginationOutput<Partial<SalesByCategoryDto>>;
export type IGetSalesByCategoryUseCase = IUseCase<
    GetSalesByCategoryUseCaseInput,
    GetSalesByCategoryUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_CATEGORY, scope: Lifecycle.Transient })
export class GetSalesByCategoryUseCase
    extends UseCase<GetSalesByCategoryUseCaseInput, GetSalesByCategoryUseCaseOutput, UseCaseContext>
    implements IGetSalesByCategoryUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.CATEGORY)
        protected readonly categoryDao: CategoryDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesByCategoryUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesByCategoryUseCase, 'validate'>) => {
        const res = await this.categoryDao.getSalesByCategory(input, {
            tenantId: this.context.tenantId!,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetSalesByCategoryUseCase, 'processing'>,
    ): Promise<GetSalesByCategoryUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
