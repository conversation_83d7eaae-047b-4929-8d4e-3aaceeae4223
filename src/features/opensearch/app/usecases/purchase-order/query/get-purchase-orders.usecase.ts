import { IUseCase, QueryInput, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationOutput } from '@core';
import { PurchaseOrderDTO } from '@features/opensearch/domain';
import { GetPurchaseOrdersResponse, IInventoryServiceProvider } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetPurchaseOrdersUseCaseInput = QueryInput<PurchaseOrderDTO> & {
    sort?: string;
};
export type GetPurchaseOrdersUseCaseOutput = PaginationOutput<Partial<PurchaseOrderDTO>>;
export type IGetPurchaseOrdersUseCase = IUseCase<
    GetPurchaseOrdersUseCaseInput,
    GetPurchaseOrdersUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_PURCHASE_ORDERS, scope: Lifecycle.Transient })
export class GetPurchaseOrdersUseCase
    extends UseCase<GetPurchaseOrdersUseCaseInput, GetPurchaseOrdersUseCaseOutput, UseCaseContext>
    implements IGetPurchaseOrdersUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        protected readonly inventoryService: IInventoryServiceProvider,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetPurchaseOrdersUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetPurchaseOrdersUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        try {
            const response: GetPurchaseOrdersResponse = await this.inventoryService.getPurchaseOrders({
                ...input,
                tenant: tenantId,
            });
            return response;
        } catch (error: unknown) {
            throw new Error('Error fetching purchase orders from inventory service: ' + error);
        }
    };

    map = async (
        input: ResultOf<GetPurchaseOrdersUseCase, 'processing'>,
    ): Promise<GetPurchaseOrdersUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.purchaseOrders || [],
        };
    };
}
