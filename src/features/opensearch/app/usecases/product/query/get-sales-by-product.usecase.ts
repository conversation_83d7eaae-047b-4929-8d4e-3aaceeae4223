import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { Inject, Lifecycle, Provider } from '@heronjs/common';
import { SalesByProductDTO } from '@features/opensearch/domain';
import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { IAggregateEDWDao, ITenantSettingsProvider, ProductDAO } from '@features/opensearch/infra';

export type GetSalesByProductUseCaseInput = PaginationInput<SalesByProductDTO>;
export type GetSalesByProductUseCaseOutput = PaginationOutput<Partial<SalesByProductDTO>>;
export type IGetSalesByProductUseCase = IUseCase<
    GetSalesByProductUseCaseInput,
    GetSalesByProductUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_PRODUCT, scope: Lifecycle.Transient })
export class GetSalesByProductUseCase
    extends UseCase<GetSalesByProductUseCaseInput, GetSalesByProductUseCaseOutput, UseCaseContext>
    implements IGetSalesByProductUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT)
        protected readonly productDao: ProductDAO,
        @Inject(OPENSEARCH_INJECT_TOKENS.EDW_DAO.AGGREGATE)
        protected readonly aggregateDao: IAggregateEDWDao,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.TENANT_SETTINGS)
        protected readonly tenantSettingsProvider: ITenantSettingsProvider,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesByProductUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesByProductUseCase, 'validate'>) => {
        const res = await this.productDao.getSalesByProduct(input, {
            tenantId: this.context.tenantId,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetSalesByProductUseCase, 'processing'>,
    ): Promise<GetSalesByProductUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
