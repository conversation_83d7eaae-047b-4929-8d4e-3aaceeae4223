import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { SalesByProductDTO } from '@features/opensearch/domain';
import { ProductDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetProductsUseCaseInput = PaginationInput<SalesByProductDTO>;
export type GetProductsUseCaseOutput = PaginationOutput<Partial<SalesByProductDTO>>;
export type IGetProductsUseCase = IUseCase<GetProductsUseCaseInput, GetProductsUseCaseOutput, UseCaseContext>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_PRODUCTS, scope: Lifecycle.Transient })
export class GetProductsUseCase
    extends UseCase<GetProductsUseCaseInput, GetProductsUseCaseOutput, UseCaseContext>
    implements IGetProductsUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT)
        protected readonly productDao: ProductDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetProductsUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetProductsUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;

        const res = await this.productDao.getProducts(
            {
                ...input,
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (input: ResultOf<GetProductsUseCase, 'processing'>): Promise<GetProductsUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
