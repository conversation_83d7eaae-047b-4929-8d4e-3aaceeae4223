import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { Inject, Lifecycle, Provider } from '@heronjs/common';
import { AttributesOfProductDAO } from '@features/opensearch/infra';
import { AttributesOfProductDTO } from '@features/opensearch/domain';
import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';

export type GetListAttributesOfProductUseCaseInput = {
    fromDate?: string;
    toDate?: string;
    productHKey: string;
    attribute: string;
    fields?: string[];
    sort?: Record<string, 'asc' | 'desc'>[];
    forecastFromDate?: string;
    forecastToDate?: string;
};
export type GetListAttributesOfProductUseCaseOutput = { items: Partial<AttributesOfProductDTO>[] };
export type IGetListAttributesOfProductUseCase = IUseCase<
    GetListAttributesOfProductUseCaseInput,
    GetListAttributesOfProductUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_ATTRIBUTE_OF_PRODUCT,
    scope: Lifecycle.Transient,
})
export class GetListAttributesOfProductUseCase
    extends UseCase<
        GetListAttributesOfProductUseCaseInput,
        GetListAttributesOfProductUseCaseOutput,
        UseCaseContext
    >
    implements IGetListAttributesOfProductUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.ATTRIBUTE)
        protected readonly attributeDao: AttributesOfProductDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetListAttributesOfProductUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetListAttributesOfProductUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        const res = await this.attributeDao.find(input, { tenantId });

        return res;
    };

    map = async (
        input: ResultOf<GetListAttributesOfProductUseCase, 'processing'>,
    ): Promise<GetListAttributesOfProductUseCaseOutput> => {
        return {
            items: input.items,
        };
    };
}
