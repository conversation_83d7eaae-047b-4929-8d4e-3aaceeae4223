import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput } from '@core';
import { SalesByVariantDTO, SalesByVariantSummaryDTO } from '@features/opensearch/domain';
import { VariantDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetSalesByVariantSummaryUseCaseInput = PaginationInput<SalesByVariantDTO>;
export type GetSalesByVariantSummaryUseCaseOutput = Partial<SalesByVariantSummaryDTO>;

export type IGetSalesByVariantSummaryUseCase = IUseCase<
    GetSalesByVariantSummaryUseCaseInput,
    GetSalesByVariantSummaryUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_VARIANT_SUMMARY,
    scope: Lifecycle.Transient,
})
export class GetSalesByVariantSummaryUseCase
    extends UseCase<
        GetSalesByVariantSummaryUseCaseInput,
        GetSalesByVariantSummaryUseCaseOutput,
        UseCaseContext
    >
    implements IGetSalesByVariantSummaryUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        protected readonly variantDao: VariantDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesByVariantSummaryUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesByVariantSummaryUseCase, 'validate'>) => {
        const res = await this.variantDao.getSalesSummary(input, {
            tenantId: this.context.tenantId,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetSalesByVariantSummaryUseCase, 'processing'>,
    ): Promise<GetSalesByVariantSummaryUseCaseOutput> => {
        const {
            gross_sales,
            gross_qty,
            net_sales,
            net_qty,
            total_sales,
            return_value,
            return_qty,
            discount,
            tax,
            shipping,
            ad_spends,
            clicks,
            views,
            stock,
            on_order,
        } = input;

        const ats = this.input.fields?.includes('ats') ? (stock ?? 0) + (on_order ?? 0) : undefined;
        const return_rate = this.input.fields?.includes('return_rate')
            ? gross_qty
                ? ((return_qty ?? 0) * 100) / gross_qty
                : null
            : undefined;

        return {
            ...input,
            gross_sales: gross_sales ? Math.round(gross_sales * 100) / 100 : gross_sales,
            gross_qty: gross_qty ? Math.round(gross_qty * 100) / 100 : gross_qty,
            net_sales: net_sales ? Math.round(net_sales * 100) / 100 : net_sales,
            net_qty: net_qty ? Math.round(net_qty * 100) / 100 : net_qty,
            total_sales: total_sales ? Math.round(total_sales * 100) / 100 : total_sales,
            return_value: return_value ? Math.round(return_value * 100) / 100 : return_value,
            return_qty: return_qty ? Math.round(return_qty * 100) / 100 : return_qty,
            discount: discount ? Math.round(discount * 100) / 100 : discount,
            tax: tax ? Math.round(tax * 100) / 100 : tax,
            shipping: shipping ? Math.round(shipping * 100) / 100 : shipping,
            ad_spends: ad_spends ? Math.round(ad_spends * 100) / 100 : ad_spends,
            clicks: clicks ? Math.round(clicks * 100) / 100 : clicks,
            views: views ? Math.round(views * 100) / 100 : views,
            stock: stock ? Math.round(stock * 100) / 100 : stock,
            on_order: on_order ? Math.round(on_order * 100) / 100 : on_order,
            ats: ats ? Math.round(ats * 100) / 100 : ats,
            return_rate: return_rate ? Math.round(return_rate * 100) / 100 : return_rate,
        };
    };
}
