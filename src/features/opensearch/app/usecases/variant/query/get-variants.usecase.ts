import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { VariantDTO } from '@features/opensearch/domain';
import { VariantDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetVariantsUseCaseInput = PaginationInput<VariantDTO>;
export type GetVariantsUseCaseOutput = PaginationOutput<Partial<VariantDTO>>;
export type IGetVariantsUseCase = IUseCase<GetVariantsUseCaseInput, GetVariantsUseCaseOutput, UseCaseContext>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_VARIANTS, scope: Lifecycle.Transient })
export class GetVariantsUseCase
    extends UseCase<GetVariantsUseCaseInput, GetVariantsUseCaseOutput, UseCaseContext>
    implements IGetVariantsUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        protected readonly variantDao: VariantDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetVariantsUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetVariantsUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;

        const res = await this.variantDao.getVariants(
            {
                ...input,
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (input: ResultOf<GetVariantsUseCase, 'processing'>): Promise<GetVariantsUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
