import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { SalesByVariantDTO } from '@features/opensearch/domain';
import { VariantDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetListVariantUseCaseInput = PaginationInput<SalesByVariantDTO>;
export type GetListVariantUseCaseOutput = PaginationOutput<Partial<SalesByVariantDTO>>;
export type IGetListVariantUseCase = IUseCase<
    GetListVariantUseCaseInput,
    GetListVariantUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_VARIANT, scope: Lifecycle.Transient })
export class GetListVariantUseCase
    extends UseCase<GetListVariantUseCaseInput, GetListVariantUseCaseOutput, UseCaseContext>
    implements IGetListVariantUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        protected readonly variantDao: VariantDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetListVariantUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetListVariantUseCase, 'validate'>) => {
        const res = await this.variantDao.getSalesByVariant(input, {
            tenantId: this.context.tenantId,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetListVariantUseCase, 'processing'>,
    ): Promise<GetListVariantUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
