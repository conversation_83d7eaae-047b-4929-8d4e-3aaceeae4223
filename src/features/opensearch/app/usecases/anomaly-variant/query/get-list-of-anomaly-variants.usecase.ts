import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { SalesByVariantDTO } from '@features/opensearch/domain';
import { VariantDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetListAnomalyVariantsUseCaseInput = PaginationInput<SalesByVariantDTO>;
export type GetListAnomalyVariantsUseCaseOutput = PaginationOutput<Partial<SalesByVariantDTO>>;
export type IGetListAnomalyVariantsUseCase = IUseCase<
    GetListAnomalyVariantsUseCaseInput,
    GetListAnomalyVariantsUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_LIST_OF_ANOMALY_VARIANTS,
    scope: Lifecycle.Transient,
})
export class GetListAnomalyVariantsUseCase
    extends UseCase<GetListAnomalyVariantsUseCaseInput, GetListAnomalyVariantsUseCaseOutput, UseCaseContext>
    implements IGetListAnomalyVariantsUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        protected readonly variantDao: VariantDAO,
    ) {
        super();
        this.setMethods(this.processing, this.map);
    }

    processing = async (input: GetListAnomalyVariantsUseCaseInput) => {
        const res = await this.variantDao.findAnomalyVariant(input, {
            tenantId: this.context.tenantId!,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetListAnomalyVariantsUseCase, 'processing'>,
    ): Promise<GetListAnomalyVariantsUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
