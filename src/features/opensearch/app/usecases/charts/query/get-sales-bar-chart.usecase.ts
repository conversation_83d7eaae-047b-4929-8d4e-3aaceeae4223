import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { ItemTypes, ProductDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Nullable, Provider } from '@heronjs/common';
import { TimeFrame } from '@features/opensearch/app/usecases/charts/enums';
import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';

export type GetSalesBarChartUseCaseInput = {
    hKey: string;
    type: ItemTypes;
    fromDate: string;
    toDate: string;
    timeFrame: TimeFrame;
};

export type SalesBarChartItem = {
    period_start: string;
    gross_qty: Nullable<number>;
    forecasted_gross_qty: Nullable<number>;
};
export type GetSalesBarChartUseCaseOutput = { items: SalesBarChartItem[] };
export type IGetSalesBarChartUseCase = IUseCase<
    GetSalesBarChartUseCaseInput,
    GetSalesBarChartUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BAR_CHART, scope: Lifecycle.Transient })
export class GetSalesBarChartUseCase
    extends UseCase<GetSalesBarChartUseCaseInput, GetSalesBarChartUseCaseOutput, UseCaseContext>
    implements IGetSalesBarChartUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT)
        protected readonly productDao: ProductDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesBarChartUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesBarChartUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        if (input.type === ItemTypes.Product) {
            const res = await this.productDao.getSalesBarChart(input, { tenantId });
            const salesBuckets =
                res.aggregations?.by_variant?.sales_agg?.nested?.sales_histogram_agg?.buckets;
            const forecastedBuckets =
                res.aggregations?.by_product?.forecast_agg?.nested?.forecast_histogram_agg?.buckets;
            return { salesBuckets, forecastedBuckets };
        } else {
            return null;
        }
    };

    map = async (
        input: ResultOf<GetSalesBarChartUseCase, 'processing'>,
    ): Promise<GetSalesBarChartUseCaseOutput> => {
        if (input === null) return { items: [] };
        const { salesBuckets, forecastedBuckets } = input;
        const data = new Map<string, SalesBarChartItem>(
            salesBuckets.map((item: Record<string, any>) => [
                item.key_as_string,
                {
                    period_start: item.key_as_string,
                    gross_qty: item.gross_qty_rounded !== undefined ? item.gross_qty_rounded.value : null,
                    forecasted_gross_qty: null,
                },
            ]),
        );

        forecastedBuckets.forEach((item: Record<string, any>) => {
            const periodStart = item.key_as_string;
            const forecastedGrossQty =
                item.forecasted_gross_qty_rounded !== undefined
                    ? item.forecasted_gross_qty_rounded.value
                    : null;
            const existingItem = data.get(periodStart);
            if (existingItem) {
                existingItem.forecasted_gross_qty = forecastedGrossQty;
            } else {
                data.set(periodStart, {
                    period_start: periodStart,
                    gross_qty: null,
                    forecasted_gross_qty: forecastedGrossQty,
                });
            }
        });
        const items = Array.from(data.values());
        return { items };
    };
}
