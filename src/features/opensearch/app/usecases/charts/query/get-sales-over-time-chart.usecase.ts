import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { ProductDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Nullable, Provider } from '@heronjs/common';
import { TimeFrame } from '@features/opensearch/app/usecases/charts/enums';
import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';

export type GetSalesOverTimeChartUseCaseInput = {
    fromDate: string;
    toDate: string;
    fromSource?: string;
    timeFrame: TimeFrame;
};
export type SalesOverTimeChartItem = {
    period_start: string;
    gross_qty: Nullable<number>;
    gross_sales: Nullable<number>;
    net_sales: Nullable<number>;
    net_qty: Nullable<number>;
    return_qty: Nullable<number>;
    avg_selling_price: Nullable<number>;
};
export type GetSalesOverTimeChartUseCaseOutput = {
    items: SalesOverTimeChartItem[];
    summary: Partial<{
        avg_selling_price: Nullable<number>;
        total_gross_qty: Nullable<number>;
        total_gross_sales: Nullable<number>;
        total_net_sales: Nullable<number>;
        total_net_qty: Nullable<number>;
        total_return_qty: Nullable<number>;
    }>;
};
export type IGetSalesOverTimeChartUseCase = IUseCase<
    GetSalesOverTimeChartUseCaseInput,
    GetSalesOverTimeChartUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_OVER_TIME_CHART, scope: Lifecycle.Transient })
export class GetSalesOverTimeChartUseCase
    extends UseCase<GetSalesOverTimeChartUseCaseInput, GetSalesOverTimeChartUseCaseOutput, UseCaseContext>
    implements IGetSalesOverTimeChartUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT)
        protected readonly productDao: ProductDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesOverTimeChartUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesOverTimeChartUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        const res = await this.productDao.getSalesOverTimeChart(input, { tenantId });
        const { total_gross_qty, total_gross_sales, total_net_sales, total_net_qty, total_return_qty } =
            res.aggregations.sales_agg.nested;
        const summary = {
            avg_selling_price: total_gross_qty?.value
                ? Math.round(((total_gross_sales?.value ?? 0) / (total_gross_qty?.value ?? 1)) * 100) / 100
                : 0,
            total_gross_qty: total_gross_qty?.value ?? 0,
            total_gross_sales: total_gross_sales?.value ?? 0,
            total_net_sales: total_net_sales?.value ?? 0,
            total_net_qty: total_net_qty?.value ?? 0,
            total_return_qty: total_return_qty?.value ?? 0,
        };

        const buckets = res.aggregations.sales_agg.nested.sales_histogram_agg.buckets;
        const data = new Map<string, SalesOverTimeChartItem>(
            buckets.map((item: Record<string, any>) => [
                item.key_as_string,
                {
                    period_start: item.key_as_string,
                    gross_qty: item.gross_qty?.value ?? 0,
                    gross_sales: item.gross_sales?.value ?? 0,
                    net_sales: item.net_sales?.value ?? 0,
                    net_qty: item.net_qty?.value ?? 0,
                    return_qty: item.return_qty?.value ?? 0,
                    avg_selling_price: item.avg_selling_price?.value ?? 0,
                },
            ]),
        );

        return { items: Array.from(data.values()), summary };
    };

    map = async (
        input: ResultOf<GetSalesOverTimeChartUseCase, 'processing'>,
    ): Promise<GetSalesOverTimeChartUseCaseOutput> => {
        const { items, summary } = input;
        return { summary, items };
    };
}
