import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { Inject, Lifecycle, Nullable, Provider } from '@heronjs/common';
import { ItemTypes, ProductDAO, VariantDAO } from '@features/opensearch/infra';
import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';

export type GetProductPerformanceChartUseCaseInput = {
    fromDate: string;
    toDate: string;
    compareFromDate: string;
    compareToDate: string;
    fromSource?: string;
    type: ItemTypes;
    fields: string[];
    offset?: number;
    limit?: number;
};

export type ProductPerformanceChartItem = {
    h_key: string;
    name: string;
    ac_gross_qty: Nullable<number>;
    ac_gross_sales: Nullable<number>;
    ac_net_sales: Nullable<number>;
    ac_net_qty: Nullable<number>;
    ac_return_qty: Nullable<number>;
    prev_gross_sales: Nullable<number>;
    prev_gross_qty: Nullable<number>;
    prev_net_sales: Nullable<number>;
    prev_net_qty: Nullable<number>;
    prev_return_qty: Nullable<number>;
    delta_gross_sales: Nullable<number>;
    delta_gross_qty: Nullable<number>;
    delta_net_sales: Nullable<number>;
    delta_net_qty: Nullable<number>;
    delta_return_qty: Nullable<number>;
    delta_percentage_gross_sales: Nullable<number>;
    delta_percentage_gross_qty: Nullable<number>;
    delta_percentage_net_sales: Nullable<number>;
    delta_percentage_net_qty: Nullable<number>;
    delta_percentage_return_qty: Nullable<number>;
};
export type GetProductPerformanceChartUseCaseOutput = {
    items: ProductPerformanceChartItem[];
};

export type IGetProductPerformanceChartUseCase = IUseCase<
    GetProductPerformanceChartUseCaseInput,
    GetProductPerformanceChartUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_PRODUCT_PERFORMANCE_CHART,
    scope: Lifecycle.Transient,
})
export class GetProductPerformanceChartUseCase
    extends UseCase<
        GetProductPerformanceChartUseCaseInput,
        GetProductPerformanceChartUseCaseOutput,
        UseCaseContext
    >
    implements IGetProductPerformanceChartUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.PRODUCT)
        protected readonly productDao: ProductDAO,
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.VARIANT)
        protected readonly variantDao: VariantDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetProductPerformanceChartUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetProductPerformanceChartUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        const { compareFromDate, compareToDate, type } = input;
        if (type === ItemTypes.Product) {
            const [selectedRangeItems, prevRangeItems] = await Promise.all([
                this.productDao.getSalesByProduct(input, { tenantId }),
                this.productDao.getSalesByProduct(
                    { ...input, fromDate: compareFromDate, toDate: compareToDate },
                    { tenantId },
                ),
            ]);

            return { selectedRangeItems, prevRangeItems };
        } else if (type === ItemTypes.Variant) {
            const [selectedRangeItems, prevRangeItems] = await Promise.all([
                this.variantDao.getSalesByVariant(input, { tenantId }),
                this.variantDao.getSalesByVariant(
                    { ...input, fromDate: compareFromDate, toDate: compareToDate },
                    { tenantId },
                ),
            ]);

            return { selectedRangeItems, prevRangeItems };
        }
        return null;
    };

    map = async (
        input: ResultOf<GetProductPerformanceChartUseCase, 'processing'>,
    ): Promise<GetProductPerformanceChartUseCaseOutput> => {
        if (input === null) return { items: [] };
        const { selectedRangeItems, prevRangeItems } = input;
        const map = new Map(
            selectedRangeItems.items.map((item) => {
                const chartItem: ProductPerformanceChartItem = {
                    h_key: item.h_key ?? '',
                    name: item.name ?? '',
                    ac_gross_qty: item.gross_qty ?? 0,
                    ac_gross_sales: item.gross_sales ?? 0,
                    ac_net_sales: item.net_sales ?? 0,
                    ac_net_qty: item.net_qty ?? 0,
                    ac_return_qty: item.return_qty ?? 0,
                    prev_gross_sales: null,
                    prev_gross_qty: null,
                    prev_net_sales: null,
                    prev_net_qty: null,
                    prev_return_qty: null,
                    delta_gross_sales: null,
                    delta_gross_qty: null,
                    delta_net_sales: null,
                    delta_net_qty: null,
                    delta_return_qty: null,
                    delta_percentage_gross_sales: null,
                    delta_percentage_gross_qty: null,
                    delta_percentage_net_sales: null,
                    delta_percentage_net_qty: null,
                    delta_percentage_return_qty: null,
                };
                return [item.h_key, chartItem];
            }),
        );

        prevRangeItems.items.forEach((item) => {
            const existingItem = map.get(item.h_key);
            if (existingItem) {
                existingItem.prev_gross_sales = item.gross_sales ?? 0;
                existingItem.prev_gross_qty = item.gross_qty ?? 0;
                existingItem.prev_net_sales = item.net_sales ?? 0;
                existingItem.prev_net_qty = item.net_qty ?? 0;
                existingItem.prev_return_qty = item.return_qty ?? 0;
                existingItem.delta_gross_qty = !item.gross_qty
                    ? null
                    : Math.round(existingItem.ac_gross_qty! - item.gross_qty) * 100;
                existingItem.delta_gross_sales = !item.gross_sales
                    ? null
                    : Math.round(existingItem.ac_gross_sales! - item.gross_sales) * 100;
                existingItem.delta_net_sales = !item.net_sales
                    ? null
                    : Math.round(existingItem.ac_net_sales! - item.net_sales) * 100;
                existingItem.delta_net_qty = !item.net_qty
                    ? null
                    : Math.round(existingItem.ac_net_qty! - item.net_qty) * 100;
                existingItem.delta_return_qty = !item.return_qty
                    ? null
                    : Math.round(existingItem.ac_return_qty! - item.return_qty) * 100;
                existingItem.delta_percentage_gross_sales = !item.gross_sales
                    ? null
                    : Math.round(
                          ((existingItem.ac_gross_sales! - item.gross_sales) / item.gross_sales) * 100,
                      );
                existingItem.delta_percentage_gross_qty = !item.gross_qty
                    ? null
                    : Math.round(((existingItem.ac_gross_qty! - item.gross_qty) / item.gross_qty) * 100);
                existingItem.delta_percentage_net_sales = !item.net_sales
                    ? null
                    : Math.round(((existingItem.ac_net_sales! - item.net_sales) / item.net_sales) * 100);
                existingItem.delta_percentage_net_qty = !item.net_qty
                    ? null
                    : Math.round(((existingItem.ac_net_qty! - item.net_qty) / item.net_qty) * 100);
                existingItem.delta_percentage_return_qty = !item.return_qty
                    ? null
                    : Math.round(((existingItem.ac_return_qty! - item.return_qty) / item.return_qty) * 100);
            }
        });

        const items = Array.from(map.values());

        return { items };
    };
}
