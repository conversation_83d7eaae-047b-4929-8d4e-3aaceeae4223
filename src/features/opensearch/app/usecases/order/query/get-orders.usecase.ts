import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput } from '@core';
import { OrderDTO, OrdersQueryOutputDTO } from '@features/opensearch/domain';
import { OrderDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetOrdersUseCaseInput = PaginationInput<OrderDTO> & {
    groupBy?: string;
};
export type GetOrdersUseCaseOutput = OrdersQueryOutputDTO;
export type IGetOrdersUseCase = IUseCase<GetOrdersUseCaseInput, GetOrdersUseCaseOutput, UseCaseContext>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_ORDERS, scope: Lifecycle.Transient })
export class GetOrdersUseCase
    extends UseCase<GetOrdersUseCaseInput, GetOrdersUseCaseOutput, UseCaseContext>
    implements IGetOrdersUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.ORDER)
        protected readonly orderDao: OrderDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetOrdersUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetOrdersUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;

        const res = await this.orderDao.getOrders(
            {
                ...input,
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (input: ResultOf<GetOrdersUseCase, 'processing'>): Promise<GetOrdersUseCaseOutput> => {
        return {
            total_count: input.total_count,
            total_gross_sales: input.total_gross_sales,
            total_gross_qty: input.total_gross_qty,
            total_net_sales: input.total_net_sales,
            total_net_qty: input.total_net_qty,
            total_total_sales: input.total_total_sales,
            total_tax: input.total_tax,
            total_discount: input.total_discount,
            total_return_qty: input.total_return_qty,
            total_return_value: input.total_return_value,
            total_gross_profit: input.total_gross_profit,
            total_order_count: input.total_order_count,
            total_return_order_count: input.total_return_order_count,
            items: input.items,
            aggs: input.aggs,
        };
    };
}
