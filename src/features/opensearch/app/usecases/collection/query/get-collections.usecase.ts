import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { CollectionDTO } from '@features/opensearch/domain';
import { CollectionDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetCollectionsUseCaseInput = PaginationInput<CollectionDTO>;
export type GetCollectionsUseCaseOutput = PaginationOutput<Partial<CollectionDTO>>;
export type IGetCollectionsUseCase = IUseCase<
    GetCollectionsUseCaseInput,
    GetCollectionsUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_COLLECTIONS, scope: Lifecycle.Transient })
export class GetCollectionsUseCase
    extends UseCase<GetCollectionsUseCaseInput, GetCollectionsUseCaseOutput, UseCaseContext>
    implements IGetCollectionsUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.COLLECTION)
        protected readonly collectionDao: CollectionDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetCollectionsUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetCollectionsUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;

        const res = await this.collectionDao.getCollections(
            {
                ...input,
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (
        input: ResultOf<GetCollectionsUseCase, 'processing'>,
    ): Promise<GetCollectionsUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.collections,
        };
    };
}
