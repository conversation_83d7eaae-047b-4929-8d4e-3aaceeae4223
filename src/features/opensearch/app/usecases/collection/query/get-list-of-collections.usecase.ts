import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput, PaginationOutput } from '@core';
import { SalesByCollectionDto } from '@features/opensearch/domain';
import { CollectionDAO } from '@features/opensearch/infra';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetListCollectionUseCaseInput = PaginationInput<SalesByCollectionDto>;
export type GetListCollectionUseCaseOutput = PaginationOutput<Partial<SalesByCollectionDto>>;
export type IGetListCollectionUseCase = IUseCase<
    GetListCollectionUseCaseInput,
    GetListCollectionUseCaseOutput,
    UseCaseContext
>;

@Provider({ token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_COLLECTION, scope: Lifecycle.Transient })
export class GetListCollectionUseCase
    extends UseCase<GetListCollectionUseCaseInput, GetListCollectionUseCaseOutput, UseCaseContext>
    implements IGetListCollectionUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.COLLECTION)
        protected readonly collectionDao: CollectionDAO,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetListCollectionUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetListCollectionUseCase, 'validate'>) => {
        const res = await this.collectionDao.getSalesByCollection(input, {
            tenantId: this.context.tenantId!,
        });

        return res;
    };

    map = async (
        input: ResultOf<GetListCollectionUseCase, 'processing'>,
    ): Promise<GetListCollectionUseCaseOutput> => {
        return {
            total_count: input.totalCount,
            items: input.items,
        };
    };
}
