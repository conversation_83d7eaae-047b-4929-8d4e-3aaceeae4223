import { IUseCase, ResultOf, UseCase, UseCaseContext } from '@cbidigital/aqua-ddd';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { PaginationInput } from '@core';
import { SalesByCollectionDto, SalesByCollectionSummaryDTO } from '@features/opensearch/domain';
import { CollectionDAO } from '@features/opensearch/infra';
import { IInventoryServiceProvider, ItemTypes } from '@features/opensearch/infra/providers';
import { Inject, Lifecycle, Provider } from '@heronjs/common';

export type GetSalesByCollectionSummaryUseCaseInput = PaginationInput<SalesByCollectionDto>;
export type GetSalesByCollectionSummaryUseCaseOutput = Partial<SalesByCollectionSummaryDTO>;

export type IGetSalesByCollectionSummaryUseCase = IUseCase<
    GetSalesByCollectionSummaryUseCaseInput,
    GetSalesByCollectionSummaryUseCaseOutput,
    UseCaseContext
>;

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_COLLECTION_SUMMARY,
    scope: Lifecycle.Transient,
})
export class GetSalesByCollectionSummaryUseCase
    extends UseCase<
        GetSalesByCollectionSummaryUseCaseInput,
        GetSalesByCollectionSummaryUseCaseOutput,
        UseCaseContext
    >
    implements IGetSalesByCollectionSummaryUseCase
{
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.OPS_DAO.COLLECTION)
        protected readonly collectionDao: CollectionDAO,
        @Inject(OPENSEARCH_INJECT_TOKENS.PROVIDER.INVENTORY_SERVICE)
        protected readonly inventoryService: IInventoryServiceProvider,
    ) {
        super();
        this.setMethods(this.validate, this.processing, this.map);
    }

    validate = async (input: GetSalesByCollectionSummaryUseCaseInput) => {
        const model = input;
        return model;
    };

    processing = async (input: ResultOf<GetSalesByCollectionSummaryUseCase, 'validate'>) => {
        const tenantId = this.context.tenantId!;
        const [onOrders] = await Promise.all([
            this.inventoryService.getOnOrders({
                tenantId,
                type: ItemTypes.Variant,
            }),
        ]);

        const bKeyToOnOrder = onOrders.reduce(
            (previous: Record<string, number>, current: { variantId: string; onOrder: number }) => {
                return { ...previous, [current.variantId]: current.onOrder };
            },
            {},
        );

        const res = await this.collectionDao.getSalesSummary(
            {
                ...input,
                metadata: {
                    bKeyToOnOrder,
                },
            },
            {
                tenantId,
            },
        );

        return res;
    };

    map = async (
        input: ResultOf<GetSalesByCollectionSummaryUseCase, 'processing'>,
    ): Promise<GetSalesByCollectionSummaryUseCaseOutput> => {
        const {
            gross_sales,
            gross_qty,
            net_sales,
            net_qty,
            total_sales,
            return_value,
            return_qty,
            ad_spends,
            clicks,
            views,
            stock,
            on_order,
            total_inventory_value_stock,
            total_inventory_value_ats,
        } = input;

        const ats = this.input.fields?.includes('ats') ? (stock ?? 0) + (on_order ?? 0) : undefined;
        const sell_through = this.input.fields?.includes('sell_through')
            ? (100 * (gross_qty ?? 0)) / ((gross_qty ?? 0) + (stock ?? 0))
            : 0;

        return {
            ...input,
            gross_sales: gross_sales ? Math.round(gross_sales * 100) / 100 : gross_sales,
            gross_qty: gross_qty ? Math.round(gross_qty * 100) / 100 : gross_qty,
            net_sales: net_sales ? Math.round(net_sales * 100) / 100 : net_sales,
            net_qty: net_qty ? Math.round(net_qty * 100) / 100 : net_qty,
            total_sales: total_sales ? Math.round(total_sales * 100) / 100 : total_sales,
            return_value: return_value ? Math.round(return_value * 100) / 100 : return_value,
            return_qty: return_qty ? Math.round(return_qty * 100) / 100 : return_qty,
            ad_spends: ad_spends ? Math.round(ad_spends * 100) / 100 : ad_spends,
            clicks: clicks ? Math.round(clicks * 100) / 100 : clicks,
            views: views ? Math.round(views * 100) / 100 : views,
            stock: stock ? Math.round(stock * 100) / 100 : stock,
            on_order: on_order ? Math.round(on_order * 100) / 100 : on_order,
            ats: ats ? Math.round(ats * 100) / 100 : ats,
            total_inventory_value_stock: total_inventory_value_stock
                ? Math.round(total_inventory_value_stock * 100) / 100
                : total_inventory_value_stock,
            total_inventory_value_ats: total_inventory_value_ats
                ? Math.round(total_inventory_value_ats * 100) / 100
                : total_inventory_value_ats,
            sell_through: sell_through ? Math.round(sell_through * 100) / 100 : sell_through,
        };
    };
}
