import {
    AttributesOfProductController,
    CategoryController,
    ChartController,
    CollectionController,
    OrderController,
    ProductController,
    PurchaseOrderController,
    VariantController,
} from '@features/opensearch/presentation';
import { Module } from '@heronjs/common';
import { OPSClusterDataSource } from '../../data-sources';
import {
    GetCategoriesUseCase,
    GetCollectionsUseCase,
    GetListAnomalyVariantsUseCase,
    GetListAttributesOfProductUseCase,
    GetListCollectionUseCase,
    GetListVariantUseCase,
    GetOrdersUseCase,
    GetProductPerformanceChartUseCase,
    GetProductsUseCase,
    GetPurchaseOrdersUseCase,
    GetSalesBarChartUseCase,
    GetSalesByCategorySummaryUseCase,
    GetSalesByCategoryUseCase,
    GetSalesByCollectionSummaryUseCase,
    GetSalesByProductSummaryUseCase,
    GetSalesByProductUseCase,
    GetSalesByVariantSummaryUseCase,
    GetSalesOverTimeChartUseCase,
    GetVariantsUseCase,
} from './app';
import {
    AttributesOfProductDAO,
    CategoryDAO,
    CollectionDAO,
    OrderDAO,
    ProductDAO,
    VariantDAO,
} from './infra';
import { AggregateEDWDao } from './infra/edw/daos';
import { InventoryServiceProvider, OpenAIProvider, TenantSettingsProvider } from './infra/providers';

@Module({
    graphqls: [
        VariantController,
        CollectionController,
        CategoryController,
        ProductController,
        AttributesOfProductController,
        OrderController,
        ChartController,
        PurchaseOrderController,
    ],
    providers: [
        // Data Source
        OPSClusterDataSource,

        // OPS DAO
        VariantDAO,
        CategoryDAO,
        CollectionDAO,
        ProductDAO,
        AttributesOfProductDAO,
        OrderDAO,

        // EDW DAO
        AggregateEDWDao,

        // Providers
        InventoryServiceProvider,
        TenantSettingsProvider,
        OpenAIProvider,

        // Usecase
        GetListVariantUseCase,
        GetVariantsUseCase,
        GetSalesByCategoryUseCase,
        GetListCollectionUseCase,
        GetListAnomalyVariantsUseCase,
        GetSalesByVariantSummaryUseCase,
        GetSalesByProductUseCase,
        GetProductsUseCase,
        GetListAttributesOfProductUseCase,
        GetCollectionsUseCase,
        GetCategoriesUseCase,
        GetSalesByCollectionSummaryUseCase,
        GetSalesByCategorySummaryUseCase,
        GetSalesByProductSummaryUseCase,
        GetOrdersUseCase,
        GetSalesBarChartUseCase,
        GetSalesOverTimeChartUseCase,
        GetProductPerformanceChartUseCase,
        GetPurchaseOrdersUseCase,
    ],
})
export class OpensearchModule {}
