import { SortType } from '@cbidigital/aqua-ddd';
import { GraphQLSchemaNames, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { IGetListAttributesOfProductUseCase } from '@features/opensearch/app';
import { Inject, Logger, Optional } from '@heronjs/common';
import { Context, GraphQL, Info, Params, Resolver } from '@heronjs/gql';
import { GraphQLUtils, withCache } from '@utils';
import { GraphQLResolveInfo } from 'graphql';
import { SampleSecurityType } from '../../../../../context';

@GraphQL()
export class AttributesOfProductController {
    private readonly logger = new Logger(this.constructor.name);
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_ATTRIBUTE_OF_PRODUCT)
        protected readonly getListOfAttributesOfProductsUseCase: IGetListAttributesOfProductUseCase,
    ) {}

    @Resolver()
    async attributes(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                from_date: string;
                to_date: string;
                product_h_key: string;
                attribute: string;
                forecast_from_date?: string;
                forecast_to_date?: string;
                sort?: { field: string; order: SortType }[];
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.Attribute);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'attributes',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const productHKey = query.product_h_key;
                const attribute = query.attribute;
                const forecastFromDate = query.forecast_from_date;
                const forecastToDate = query.forecast_to_date;
                const sort: Optional<Record<string, SortType>[]> = query.sort?.map((sortItem) => {
                    const { field, order } = sortItem;
                    return { [field]: order };
                });

                const output = await this.getListOfAttributesOfProductsUseCase.exec(
                    {
                        fromDate,
                        toDate,
                        productHKey,
                        attribute,
                        fields,
                        sort,
                        forecastFromDate,
                        forecastToDate,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output.items;
            },
        );
    }
}
