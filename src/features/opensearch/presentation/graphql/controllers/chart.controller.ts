import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import {
    IGetProductPerformanceChartUseCase,
    IGetSalesBarChartUseCase,
    IGetSalesOverTimeChartUseCase,
    TimeFrame,
} from '@features/opensearch/app';
import { ItemTypes } from '@features/opensearch/infra';
import { Inject, Logger } from '@heronjs/common';
import { Context, GraphQL, Info, Params, Resolver } from '@heronjs/gql';
import { withCache } from '@utils';
import { GraphQLResolveInfo } from 'graphql';
import { SampleSecurityType } from '../../../../../context';

@GraphQL()
export class ChartController {
    private readonly logger = new Logger(this.constructor.name);
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BAR_CHART)
        protected readonly getSalesBarChartUseCase: IGetSalesBarChartUseCase,
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_OVER_TIME_CHART)
        protected readonly getSalesOverTimeChartUseCase: IGetSalesOverTimeChartUseCase,
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_PRODUCT_PERFORMANCE_CHART)
        protected readonly getProductPerformanceChartUseCase: IGetProductPerformanceChartUseCase,
    ) {}

    @Resolver()
    async sales_bar_chart(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                h_key: string;
                type: ItemTypes;
                from_date: string;
                to_date: string;
                time_frame: TimeFrame;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'sales_bar_chart',
                userId,
                query,
                fields: [],
                logger: this.logger,
            },
            async () => {
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const hKey = query.h_key;
                const type = query.type;
                const timeFrame = query.time_frame;
                const result = await this.getSalesBarChartUseCase.exec(
                    { hKey, type, fromDate, toDate, timeFrame },
                    { tenantId, auth: { authId: userId } },
                );

                return result;
            },
        );
    }

    @Resolver()
    async sales_over_time_chart(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                type: ItemTypes;
                from_date: string;
                to_date: string;
                from_source?: string;
                time_frame: TimeFrame;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'sales_over_time_chart',
                userId,
                query,
                fields: [],
                logger: this.logger,
            },
            async () => {
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const fromSource = query.from_source;
                const timeFrame = query.time_frame;
                const result = await this.getSalesOverTimeChartUseCase.exec(
                    { fromDate, toDate, timeFrame, fromSource },
                    { tenantId, auth: { authId: userId } },
                );

                return result;
            },
        );
    }

    @Resolver()
    async product_performance_chart(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                from_date: string;
                to_date: string;
                compare_from_date: string;
                compare_to_date: string;
                from_source?: string;
                type: ItemTypes;
                offset?: number;
                limit?: number;
                fields?: string[];
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'product_performance_chart',
                userId,
                query,
                fields: query.fields ?? [],
                logger: this.logger,
            },
            async () => {
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const compareFromDate = query.compare_from_date;
                const compareToDate = query.compare_to_date;
                const fromSource = query.from_source;
                const type = query.type;
                const offset = query.offset ?? 0;
                const limit = query.limit ?? 20;
                const fields = query.fields ?? [];
                const result = await this.getProductPerformanceChartUseCase.exec(
                    {
                        fromDate,
                        toDate,
                        compareFromDate,
                        compareToDate,
                        fromSource,
                        type,
                        limit,
                        offset,
                        fields,
                    },
                    { tenantId, auth: { authId: userId } },
                );

                return result;
            },
        );
    }
}
