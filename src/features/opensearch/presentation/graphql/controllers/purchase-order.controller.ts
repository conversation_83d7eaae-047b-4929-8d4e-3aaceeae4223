import { SortType } from '@cbidigital/aqua-ddd';
import { DEFAULT_LIMIT, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { IGetPurchaseOrdersUseCase } from '@features/opensearch/app/usecases/purchase-order/query/get-purchase-orders.usecase';
import { Inject, Logger } from '@heronjs/common';
import { Context, GraphQL, Info, Params, Resolver } from '@heronjs/gql';
import { withCache } from '@utils';
import { GraphQLResolveInfo } from 'graphql';
import { SampleSecurityType } from '../../../../../context';

@GraphQL()
export class PurchaseOrderController {
    private readonly logger = new Logger(this.constructor.name);
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_PURCHASE_ORDERS)
        protected readonly getPurchaseOrdersUseCase: IGetPurchaseOrdersUseCase,
    ) {}

    @Resolver()
    async purchase_orders(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                offset?: string;
                limit?: string;
                sort?: { field: string; order: SortType }[];
                search?: string;
                filter?: any;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'purchase_orders',
                userId,
                query,
                fields: [],
                logger: this.logger,
            },
            async () => {
                const { filter } = query;
                const offset = query.offset !== undefined ? +query.offset : 0;
                const limit = query.limit !== undefined ? +query.limit : DEFAULT_LIMIT;
                let sort: string | undefined;
                query.sort?.forEach((sortItem) => {
                    const { field, order } = sortItem;
                    if (sort) {
                        sort += `,${field}:${order}`;
                    } else {
                        sort = `${field}:${order}`;
                    }
                });

                const output = await this.getPurchaseOrdersUseCase.exec(
                    {
                        offset,
                        limit,
                        sort,
                        filter,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output;
            },
        );
    }
}
