import { SortType } from '@cbidigital/aqua-ddd';
import { DEFAULT_LIMIT, GraphQLSchemaNames, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { SemanticSearch } from '@core';
import {
    IGetListVariantUseCase,
    IGetSalesByVariantSummaryUseCase,
    IGetVariantsUseCase,
    TimeFrame,
} from '@features/opensearch/app';
import { Inject, Logger, Optional } from '@heronjs/common';
import { Context, GraphQL, Info, Params, Resolver } from '@heronjs/gql';
import { GraphQLUtils, withCache } from '@utils';
import { GraphQLResolveInfo } from 'graphql';
import { SampleSecurityType } from '../../../../../context';

@GraphQL()
export class VariantController {
    private readonly logger = new Logger(this.constructor.name);
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_VARIANTS)
        protected readonly getVariantsUseCase: IGetVariantsUseCase,
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_VARIANT)
        protected readonly getListOfVariantsUseCase: IGetListVariantUseCase,
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_SALES_BY_VARIANT_SUMMARY)
        protected readonly getSalesByVariantSummaryUseCase: IGetSalesByVariantSummaryUseCase,
    ) {}

    @Resolver()
    async variants(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                offset?: string;
                limit?: string;
                sort?: { field: string; order: SortType }[];
                filter?: any;
                fields?: string;
                semantic_search?: {
                    text: string;
                    min_score?: number;
                };
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.Variant);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'variants',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const { filter, semantic_search } = query;
                const offset = query.offset !== undefined ? +query.offset : 0;
                const limit = query.limit !== undefined ? +query.limit : DEFAULT_LIMIT;
                const sort: Optional<Record<string, SortType>[]> = query.sort?.map((sortItem) => {
                    const { field, order } = sortItem;
                    return { [field]: order };
                });
                const semanticSearch: Optional<SemanticSearch> = semantic_search
                    ? {
                          text: semantic_search.text,
                          minScore: semantic_search.min_score,
                      }
                    : undefined;

                const output = await this.getVariantsUseCase.exec(
                    {
                        offset,
                        limit,
                        sort,
                        filter,
                        fields,
                        semanticSearch,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output;
            },
        );
    }

    @Resolver()
    async sales_by_variant(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                offset?: string;
                limit?: string;
                sort?: { field: string; order: SortType }[];
                search?: string;
                filter?: any;
                from_date: string;
                to_date: string;
                forecast_from_date?: string;
                forecast_to_date?: string;
                fields?: string;
                channel_id?: string;
                time_frame?: TimeFrame;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.Variant);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'sales_by_variant',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const { search, filter } = query;
                const offset = query.offset !== undefined ? +query.offset : 0;
                const limit = query.limit !== undefined ? +query.limit : DEFAULT_LIMIT;
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const forecastFromDate = query.forecast_from_date;
                const forecastToDate = query.forecast_to_date;
                const channelId = query.channel_id;
                const timeFrame = query.time_frame ?? TimeFrame.Month;
                const sort: Optional<Record<string, SortType>[]> = query.sort?.map((sortItem) => {
                    const { field, order } = sortItem;
                    return { [field]: order };
                });
                const output = await this.getListOfVariantsUseCase.exec(
                    {
                        offset,
                        limit,
                        sort,
                        search,
                        filter,
                        fromDate,
                        toDate,
                        forecastFromDate,
                        forecastToDate,
                        fields,
                        channelId,
                        timeFrame,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output.items;
            },
        );
    }

    @Resolver()
    async sales_by_variant_summary(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                search?: string;
                filter?: any;
                from_date: string;
                to_date: string;
                forecast_from_date?: string;
                forecast_to_date?: string;
                fields?: string;
                channel_id?: string;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.VariantSummary);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'sales_by_variant_summary',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const { search, filter } = query;
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const forecastFromDate = query.forecast_from_date;
                const forecastToDate = query.forecast_to_date;
                const channelId = query.channel_id;

                const output = await this.getSalesByVariantSummaryUseCase.exec(
                    {
                        search,
                        filter,
                        fromDate,
                        toDate,
                        forecastFromDate,
                        forecastToDate,
                        fields,
                        channelId,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output;
            },
        );
    }

    /**
     * @deprecated This endpoint is deprecated. Please use sales_by_variant_summary instead.
     */
    @Resolver()
    async sales_summary(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                offset?: string;
                limit?: string;
                sort?: { field: string; order: SortType }[];
                search?: string;
                filter?: any;
                from_date: string;
                to_date: string;
                forecast_from_date?: string;
                forecast_to_date?: string;
                fields?: string;
                channel_id?: string;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.VariantSummary);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'sales_summary',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const { search, filter } = query;
                const offset = query.offset !== undefined ? +query.offset : 0;
                const limit = query.limit !== undefined ? +query.limit : undefined;
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const forecastFromDate = query.forecast_from_date;
                const forecastToDate = query.forecast_to_date;
                const channelId = query.channel_id;

                const sort: Optional<Record<string, SortType>[]> = query.sort?.map((sortItem) => {
                    const { field, order } = sortItem;
                    return { [field]: order };
                });

                const output = await this.getSalesByVariantSummaryUseCase.exec(
                    {
                        offset,
                        limit,
                        sort,
                        search,
                        filter,
                        fromDate,
                        toDate,
                        forecastFromDate,
                        forecastToDate,
                        fields,
                        channelId,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );
                return output;
            },
        );
    }
}
