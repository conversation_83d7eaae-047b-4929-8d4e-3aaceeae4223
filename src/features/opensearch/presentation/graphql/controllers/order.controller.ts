import { SortType } from '@cbidigital/aqua-ddd';
import { DEFAULT_LIMIT, GraphQLSchemaNames, OPENSEARCH_INJECT_TOKENS } from '@constants';
import { IGetOrdersUseCase } from '@features/opensearch/app/usecases/order/query/get-orders.usecase';
import { Inject, Logger, Optional } from '@heronjs/common';
import { Context, GraphQL, Info, Params, Resolver } from '@heronjs/gql';
import { GraphQLUtils, withCache } from '@utils';
import { GraphQLResolveInfo } from 'graphql';
import { SampleSecurityType } from '../../../../../context';

@GraphQL()
export class OrderController {
    private readonly logger = new Logger(this.constructor.name);
    constructor(
        @Inject(OPENSEARCH_INJECT_TOKENS.USECASE.GET_ORDERS)
        protected readonly getOrdersUseCase: IGetOrdersUseCase,
    ) {}

    @Resolver()
    async orders(
        @Context() context: { principal: Partial<SampleSecurityType> },
        @Info() info: GraphQLResolveInfo,
        @Params()
        params: {
            query: {
                offset?: string;
                limit?: string;
                from_date?: string;
                to_date?: string;
                sort?: { field: string; order: SortType }[];
                filter?: any;
                fields?: string;
                group_by?: string;
            };
        },
    ) {
        const tenantId = context.principal.organization;
        const userId = context.principal.sub;
        const { query } = params;
        const fields = GraphQLUtils.extractGraphQLFields(info, GraphQLSchemaNames.Order);

        return withCache(
            {
                tenantId: tenantId!,
                entityName: this.constructor.name,
                subType: 'orders',
                userId,
                query,
                fields,
                logger: this.logger,
            },
            async () => {
                const { filter } = query;
                const offset = query.offset !== undefined ? +query.offset : 0;
                const limit = query.limit !== undefined ? +query.limit : DEFAULT_LIMIT;
                const fromDate = query.from_date;
                const toDate = query.to_date;
                const groupBy = query.group_by ?? 'month';
                const sort: Optional<Record<string, SortType>[]> = query.sort?.map((sortItem) => {
                    const { field, order } = sortItem;
                    return { [field]: order };
                });

                const output = await this.getOrdersUseCase.exec(
                    {
                        offset,
                        limit,
                        sort,
                        filter,
                        fields,
                        fromDate,
                        toDate,
                        groupBy,
                    },
                    {
                        tenantId,
                        auth: { authId: userId },
                    },
                );

                return output;
            },
        );
    }
}
