import { CategoryItemDTO, CollectionItemDTO, TagItemDTO, VendorItemDTO } from './variant.dto';

export type SalesByVariantDTO = {
    h_key: string;

    // metadata
    from_source: string;
    parent_h_key: string;
    b_key: string;
    variant_id: string;
    product_id: string;
    product_h_key: string;
    type: string;
    name: string;
    sku: string;
    barcode: string;
    price: number;
    compare_at_price: number;
    image: string;
    color: string;
    default_color: string;
    size: string;
    status: string;
    replenishable: boolean;
    stock: number;
    on_order: number;
    ats: number;
    categories: CategoryItemDTO[];
    collections: CollectionItemDTO[];
    tags: TagItemDTO[];
    vendors: VendorItemDTO[];
    days_on_site: number;
    collection_position: number;

    // sales_metrics
    gross_sales: number;
    gross_qty: number;
    net_sales: number;
    net_qty: number;
    total_sales: number;
    return_value: number;
    return_qty: number;
    discount: number;
    tax: number;
    shipping: number;
    sales_per_day: number;
    return_rate: number;
    sell_through: number;
    sales_days_left: number;
    gross_profit: number;
    gross_margin: number;
    cost_per_item: number;
    total_inventory_value_stock: number;
    total_inventory_value_ats: number;
    wos: number;
    re_order_qty: number;
    stock_percentage: number;
    rop: number;
    lead_time: number;
    days_of_stock: number;
    gross_sales_percentage: number;
    product_grade: string;
    gross_sales_rank: number;
    sales_over_time?: SalesOverTimeDTO[];

    // forecast_metrics
    trend: string;
    sale_pattern: string;
    forecast_confidence: string;
    forecast_value: number;
    forecast_sales_per_day: number;
    forecasted_gross_sales: number;
    forecasted_gross_qty: number;
    forecasted_sales_days_remaining_based_on_stock: number;
    forecasted_sales_days_remaining_based_on_ats: number;
    forecast_over_time?: ForecastOverTimeDTO[];

    // ads_metrics
    ad_spends: number;
    clicks: number;
    views: number;
    conversion_rate: number;

    // stock_metrics
    days_in_stock_over_time?: DaysInStockOverTimeDTO[];
    in_transit_stock?: number;
};

export type SalesOverTimeDTO = {
    period_start: string;
    gross_sales?: number;
    gross_qty?: number;
};

export type ForecastOverTimeDTO = {
    period_start: string;
    forecasted_gross_qty?: number;
};

export type DaysInStockOverTimeDTO = {
    period_start: string;
    days_in_stock?: number;
};
