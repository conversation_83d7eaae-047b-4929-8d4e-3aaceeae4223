export type PurchaseOrderDTO = {
    id: string;
    source: string;
    status: string;
    archived: boolean;
    po_number: string;
    vendor_id?: string;
    updated_at?: string;
    created_at: string;
    vendor_name?: string;
    warehouse_id?: string;
    expected_date?: string;
    warehouse_name?: string;
    received_status?: string;
    total_order_quantity?: number;
    total_received_quantity?: number;
};

export type PurchaseOrdersQueryOutputDTO = {
    total_count: number;
    items: Partial<PurchaseOrderDTO>[];
};
