export type AnomalyVariantDTO = {
    h_key: string;

    // metadata
    type: string;
    replenishable: boolean;
    b_key: string;
    variant_id: string;
    price: number;
    parent_h_key: string;
    sku: string;
    stock: number;
    barcode: string;
    from_source: string;
    size: string;
    name: string;
    status: string;

    gross_sales: number;
    compared_gross_sales: number;
    gross_sales_delta: number;

    views: number;
    compared_views: number;
    views_delta: number;

    ad_spends: number;
    compared_ad_spends: number;
    ad_spends_delta: number;
};

export type KeyOfAnomalyVariantDTO = keyof AnomalyVariantDTO;
export const AnomalyVariantDTOFieldMap: Record<KeyOfAnomalyVariantDTO, KeyOfAnomalyVariantDTO> = {
    h_key: 'h_key',

    // metadata
    from_source: 'from_source',
    parent_h_key: 'parent_h_key',
    b_key: 'b_key',
    variant_id: 'variant_id',
    type: 'type',
    name: 'name',
    sku: 'sku',
    barcode: 'barcode',
    price: 'price',
    size: 'size',
    status: 'status',
    replenishable: 'replenishable',
    stock: 'stock',

    // sales_metrics
    gross_sales: 'gross_sales',
    compared_gross_sales: 'compared_gross_sales',
    gross_sales_delta: 'gross_sales_delta',

    //ads_metrics
    views: 'views',
    compared_views: 'compared_views',
    views_delta: 'views_delta',

    ad_spends: 'ad_spends',
    compared_ad_spends: 'compared_ad_spends',
    ad_spends_delta: 'ad_spends_delta',
};
