type Collection = {
    from_source: string;
    name: string;
    position: number;
    h_key: string;
};

type Category = {
    from_source: string;
    name: string;
    h_key: string;
};

type Tag = {
    from_source: string;
    name: string;
    h_key: string;
};

type Vendor = {
    from_source: string;
    name: string;
    h_key: string;
};

export type SalesByProductDTO = {
    // metadata
    b_key: string;
    barcode: string;
    categories: Category[];
    collections: Collection[];
    color: string;
    default_color: string;
    compare_at_price: number;
    days_on_site: number;
    dda_code: string;
    dda_value: string;
    from_source: string;
    h_key: string;
    image: string;
    name: string;
    on_order: number;
    price: number;
    product_h_key: string;
    product_id: string;
    replenishable: boolean;
    size: string;
    sku: string;
    source_created_at: string;
    status: string;
    stock: number;
    tags: Tag[];
    total_inventory_value_stock: number;
    type: string;
    vendors: Vendor[];
    collection_position: number;

    // sales metrics
    ats: number;
    discount: number;
    gross_qty: number;
    gross_sales: number;
    net_qty: number;
    net_sales: number;
    return_qty: number;
    return_rate: number;
    return_value: number;
    sales_per_day: number;
    shipping: number;
    tax: number;
    total_sales: number;
    sell_through: number;

    sales_days_left: number;
    gross_profit: number;
    gross_margin: number;
    cost_per_item: number;
    total_inventory_value_ats: number;
    wos: number;
    re_order_qty: number;
    stock_percentage: number;
    rop: number;
    lead_time: number;
    days_of_stock: number;
    gross_sales_percentage: number;

    // forecast metrics
    trend: string;
    sale_pattern: string;
    forecast_confidence: string;
    forecast_sales_per_day: number;
    forecast_value: number;
    forecasted_gross_qty: number;
    forecasted_gross_sales: number;
    forecasted_sales_days_remaining_based_on_stock: number;
    forecasted_sales_days_remaining_based_on_ats: number;

    // ads metrics
    ad_spends: number;
    clicks: number;
    views: number;

    conversion_rate: number;
    product_grade: string;
    gross_sales_rank: number;
};
