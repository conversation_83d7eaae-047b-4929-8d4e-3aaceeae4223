type Category = {
    name: string;
    h_key: string;
};

export type AttributesOfProductDTO = {
    h_key: string;
    attribute_value?: string;
    product_id?: string;
    categories?: Category[];
    gross_sales?: number;
    gross_qty?: number;
    return_value?: number;
    return_qty?: number;
    return_rate?: number;
    net_sales?: number;
    net_qty?: number;
    total_sales?: number;
    sales_per_day?: number;
    sales_day_left?: number;
    sell_through?: number;
    tax?: number;
    discount?: number;
    shipping?: number;
    views?: number;
    stock?: number;
    on_order?: number;
    ats?: number;
    total_inventory_value_ats?: number;
    price?: number;
    name?: string;
    image?: string;
    rop?: number;
    conversion_rate?: number;
    gross_sales_percentage?: number;
    forecasted_gross_qty?: number;
    re_order_qty?: number;
    gross_profit?: number;
    gross_margin?: number;
    wos?: number;
    forecast_sales_per_day?: number;
    stock_percentage?: number;
};
