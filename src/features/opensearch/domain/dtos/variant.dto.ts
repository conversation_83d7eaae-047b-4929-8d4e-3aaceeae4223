export type CollectionItemDTO = {
    from_source: string;
    name: string;
    position: number;
    h_key: string;
};

export type CategoryItemDTO = {
    from_source: string;
    name: string;
    h_key: string;
};

export type TagItemDTO = {
    from_source: string;
    name: string;
    h_key: string;
};

export type VendorItemDTO = {
    from_source: string;
    name: string;
    h_key: string;
};

export type VariantDTO = {
    h_key: string;
    from_source: string;
    parent_h_key: string;
    b_key: string;
    variant_id: string;
    product_id: string;
    type: string;
    name: string;
    sku: string;
    barcode: string;
    cost_per_item: number;
    price: number;
    compare_at_price: number;
    image: string;
    color: string;
    default_color: string;
    size: string;
    status: string;
    published_at: string;
    categories: CategoryItemDTO[];
    collections: CollectionItemDTO[];
    tags: TagItemDTO[];
    vendors: VendorItemDTO[];
};
