export type OrderItemDTO = {
    h_item_key: string;
    item_sku: string;
    item_name: string;
    item_categories: {
        name: string;
        h_key: string;
    }[];
    item_price: number;
    cost_per_item: number;
    tax: number;
    net_qty: number;
    discount: number;
    shipping: number;
    gross_qty: number;
    net_sales: number;
    return_qty: number;
    gross_sales: number;
    total_sales: number;
    return_value: number;
    cost: number;
    gross_profit: number;
};

export type OrderDTO = {
    order_id: string;
    from_source: string;
    channel_id: string;
    date: string;
    total_sales: number;
    net_sales: number;
    gross_sales: number;
    gross_qty: number;
    net_qty: number;
    return_qty: number;
    return_value: number;
    discount: number;
    tax: number;
    shipping: number;
    unique_item_count: number;
    gross_profit: number;
    order_items: OrderItemDTO[];
};

export type OrderAggregationDTO = {
    key: string;
    gross_sales: number;
    gross_qty: number;
    net_sales: number;
    net_qty: number;
    total_sales: number;
    tax: number;
    discount: number;
    return_qty: number;
    return_value: number;
    gross_profit: number;
};

export type OrdersQueryOutputDTO = {
    total_count: number;
    total_gross_sales: number;
    total_gross_qty: number;
    total_net_sales: number;
    total_net_qty: number;
    total_total_sales: number;
    total_tax: number;
    total_discount: number;
    total_return_qty: number;
    total_return_value: number;
    total_gross_profit: number;
    total_order_count: number;
    total_return_order_count: number;
    items: Partial<OrderDTO>[];
    aggs: Partial<OrderAggregationDTO>[];
};
