export enum GraphQLSchemaNames {
    Variant = 'Variant',
    Product = 'ProductFragment',
    Collection = 'Collection',
    Category = 'SalesByCategoryFields',
    Order = 'Order',
    VariantSummary = 'VariantSummary',
    Attribute = 'Attribute',
    CollectionSummary = 'CollectionSummary',
    CategorySummary = 'CategorySummary',
}

export const DEFAULT_LIMIT = 100;

export const DefaultFields = {
    relevanceScore: {
        fields: ['max_score'],
        sort: [{ max_score: 'desc' }],
    },
};
