export const OPENSEARCH_INJECT_TOKENS = {
    EVENT_EMITTER: {
        OPENSEARCH: 'opensearch-module.event-emitter.opensearch',
    },
    AGGREGATOR: {
        VARIANT: 'opensearch-module.aggregator.variant',
    },
    PROVIDER: {
        INVENTORY_SERVICE: 'opensearch-module.provider.inventory-service',
        TENANT_SETTINGS: 'opensearch-module.provider.tenant-settings',
        OPENAI: 'opensearch-module.provider.openai',
        OPS: 'opensearch-module.provider.ops',
    },
    EDW_DAO: {
        AGGREGATE: 'opensearch-module.edw-dao.aggregate',
    },
    OPS_DAO: {
        ITEM_METADATA: 'opensearch-module.ops-dao.item-metadata',
        ITEM_METRICS: 'opensearch-module.ops-dao.item-metrics',
        VARIANT: 'opensearch-module.ops-dao.variant',
        CATEGORY: 'opensearch-module.ops-dao.category',
        COLLECTION: 'opensearch-module.ops-dao.collection',
        PRODUCT: 'opensearch-module.ops-dao.product',
        ATTRIBUTE: 'opensearch-module.ops-dao.attribute',
        ORDER: 'opensearch-module.ops-dao.order',
    },
    MAPPER: {},
    BUILDER: {},
    REPOSITORY: {},
    USECASE: {
        GET_SALES_BY_VARIANT: 'opensearch-module.usecase.get-sales-by-variant',
        GET_VARIANT: 'opensearch-module.usecase.get-variant',
        GET_VARIANTS: 'opensearch-module.usecase.get-variants',
        GET_PRODUCTS: 'opensearch-module.usecase.get-products',
        GET_CATEGORIES: 'opensearch-module.usecase.get-categories',
        GET_SALES_BY_CATEGORY: 'opensearch-module.usecase.get-sales-by-category',
        GET_SALES_BY_CATEGORY_SUMMARY: 'opensearch-module.usecase.get-sales-by-category-summary',
        GET_COLLECTIONS: 'opensearch-module.usecase.get-collections',
        GET_SALES_BY_COLLECTION: 'opensearch-module.usecase.get-sales-by-collection',
        GET_SALES_BY_COLLECTION_SUMMARY: 'opensearch-module.usecase.get-sales-by-collection-summary',
        GET_LIST_OF_ANOMALY_VARIANTS: 'opensearch-module.usecase.get-list-of-anomaly-variants',
        GET_SALES_BY_VARIANT_SUMMARY: 'opensearch-module.usecase.get-sales-by-variant-summary',
        GET_SALES_BY_PRODUCT: 'opensearch-module.usecase.get-sales-by-product',
        GET_SALES_BY_PRODUCT_SUMMARY: 'opensearch-module.usecase.get-sales-by-product-summary',
        GET_SALES_BY_ATTRIBUTE_OF_PRODUCT: 'opensearch-module.usecase.get-sales-by-attribute-of-product',
        GET_ORDERS: 'opensearch-module.usecase.get-orders',
        GET_SALES_BAR_CHART: 'opensearch-module.usecase.get-sales-bar-chart',
        GET_SALES_OVER_TIME_CHART: 'opensearch-module.usecase.get-sales-over-time-chart',
        GET_PURCHASE_ORDERS: 'opensearch-module.usecase.get-purchase-orders',
        GET_PRODUCT_PERFORMANCE_CHART: 'opensearch-module.usecase.get-product-performance-chart',
    },
    OPS_DATA_SOURCE: 'opensearch-module.ops-data-source',
    OPS_SERVERLESS_DATA_SOURCE: 'opensearch-module.ops-serverless-data-source',
} as const;
