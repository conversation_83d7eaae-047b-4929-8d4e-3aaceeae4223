import moment from 'moment';

export class DateUtil {
    static calculateDateDifference(fromDate: string, toDate: string) {
        const msInDay = 24 * 60 * 60 * 1000;
        const diff = new Date(toDate).getTime() - new Date(fromDate).getTime();
        return Math.round(diff / msInDay) + 1;
    }

    static getFormattedDate(date: Date, template = 'YYYY-MM-DD') {
        return moment(date).format(template);
    }
}
