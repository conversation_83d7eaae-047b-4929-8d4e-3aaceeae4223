export class ObjectUtil {
    static stableStringify(obj: Record<string, unknown>): string {
        if (obj === null || typeof obj !== 'object') {
            return JSON.stringify(obj);
        }

        if (Array.isArray(obj)) {
            return `[${obj.map(ObjectUtil.stableStringify).join(',')}]`;
        }

        const keys = Object.keys(obj)
            .filter((k) => obj[k] !== undefined)
            .sort();
        const keyValuePairs = keys.map(
            (key) => `"${key}":${ObjectUtil.stableStringify(obj[key] as Record<string, unknown>)}`,
        );

        return `{${keyValuePairs.join(',')}}`;
    }
}
