import { GraphQLResolveInfo } from 'graphql';

export class GraphQLUtils {
    static extractGraphQLFields = (info: GraphQLResolveInfo, fragmentName?: string): string[] => {
        const nodefields: string[] = [];
        info.fieldNodes[0].selectionSet?.selections.forEach((selection: any) => {
            if (selection.kind === 'Field') {
                nodefields.push(selection.name.value);
            }

            if (Array.isArray(selection.selectionSet?.selections)) {
                selection.selectionSet.selections.forEach((subSelection: any) => {
                    if (subSelection.kind === 'Field') {
                        nodefields.push(subSelection.name.value);
                    }
                });
            }
        });

        const fragmentFields = fragmentName
            ? info.fragments[fragmentName]?.selectionSet?.selections.reduce<string[]>((acc, selection) => {
                  if (selection.kind === 'Field') {
                      acc.push(selection.name.value);
                  }
                  return acc;
              }, []) || []
            : [];

        return [...nodefields, ...fragmentFields];
    };
}
