import { ObjectUtil } from './object.util';
import { StringUtil } from './string.util';

export class RedisUtil {
    static generateKey(
        tenantId: string,
        entityName: string,
        subType: string,
        query: Record<string, unknown>,
        fields?: string[],
    ): string {
        const serviceName = 'search-service';
        const queryString = ObjectUtil.stableStringify(query);
        const hashedQuery = StringUtil.hash(queryString);
        let key = `${tenantId}:${serviceName}:${entityName}:${subType}:${hashedQuery}`;
        if (fields && fields.length > 0) {
            const sortedFieldsString = fields.sort().join(',');
            key += `:${sortedFieldsString}`;
        }
        return key;
    }
}
