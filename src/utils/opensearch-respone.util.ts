import { IOPSDataSource } from '../data-sources';
import { DateUtil } from './date.util';
import { DSLUtil } from './dsl.util';

export class OpensearchResponseUtil {
    static async getMaxStockDate(
        opsDataSource: IOPSDataSource,
        tenantId: string,
        index: string,
        input: any,
    ): Promise<string> {
        const maxStockDateResponse = await opsDataSource.search({
            tenantId,
            params: {
                index,
                body: DSLUtil.getMaxStockDate()
                    .buildQuery({ ...input })
                    .toJSON(),
            },
        });
        const maxStockDate = maxStockDateResponse.body.aggregations.date.max_cost_date.value;
        return DateUtil.getFormattedDate(new Date(maxStockDate));
    }

    static async getTotalStock(
        opsDataSource: IOPSDataSource,
        tenantId: string,
        index: string,
    ): Promise<number> {
        const totalStockResponse = await opsDataSource.search({
            tenantId,
            params: {
                index,
                body: DSLUtil.TotalStockAggregation.buildQuery({}).toJSON(),
            },
        });
        const totalStock = totalStockResponse.body.aggregations.stock.value;
        return totalStock;
    }

    static async getTotalGrossSales(
        opsDataSource: IOPSDataSource,
        tenantId: string,
        index: string,
        input: any,
    ): Promise<number> {
        const totalGrossSaleResponse = await opsDataSource.search({
            tenantId,
            params: {
                index,
                body: DSLUtil.TotalGrossSalesAggregation.buildQuery({ ...input }).toJSON(),
            },
        });

        const totalGrossSales =
            totalGrossSaleResponse.body.aggregations.sales_metrics.nested.total_gross_sales.value;
        return totalGrossSales;
    }

    static async getTotalInTransitStock(
        opsDataSource: IOPSDataSource,
        tenantId: string,
        index: string,
        input: any,
    ): Promise<number> {
        const totalInTransitStockResponse = await opsDataSource.search({
            tenantId,
            params: {
                index,
                body: DSLUtil.TotalInTransitStockAggregation.buildQuery({ ...input }).toJSON(),
            },
        });
        const buckets = totalInTransitStockResponse.body.aggregations.by_source.buckets;
        const totalInTransitStock = buckets.reduce((acc: Record<string, number>, item: any) => {
            acc[item.key] = item.stock_metrics.max_date.value;
            return acc;
        }, {} as Record<string, number>);
        return totalInTransitStock;
    }
}
