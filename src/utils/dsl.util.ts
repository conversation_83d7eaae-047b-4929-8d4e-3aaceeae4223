import { AggregationItem, MAX_BUCKET_SIZE, RequestBodySearchItem } from '@core';
import esb, { requestBodySearch, SumAggregation } from 'elastic-builder';

export class DSLUtil {
    static getMaxStockDate(): RequestBodySearchItem {
        return {
            key: 'max_cost_date',
            buildQuery: () => {
                return requestBodySearch()
                    .size(0)
                    .agg(
                        esb
                            .nestedAggregation('date', 'stock_metrics')
                            .agg(esb.maxAggregation('max_cost_date', 'stock_metrics.date')),
                    );
            },
        };
    }

    static StockMetricsAggregation: AggregationItem = {
        key: 'stock_metrics_aggregation',
        children: [
            {
                key: 'stock_per_date',
                buildQuery: () =>
                    esb
                        .termsAggregation('stock_per_date', 'stock_metrics.date')
                        .agg(esb.sumAggregation('total_stock', 'stock_metrics.stock')),
            },
            {
                key: 'stock',
                buildQuery: () => esb.sumBucketAggregation('stock', 'stock_per_date>total_stock'),
            },
        ],
        buildQuery: (options, children = []) => {
            const { maxStockDate } = options;

            const nestedQuery = esb.filterAggregation(
                'nested',
                esb.boolQuery().must([esb.termQuery('stock_metrics.date', maxStockDate)]),
            );

            children.forEach((child) => nestedQuery.agg(child.buildQuery(options, child.children)));

            return esb.nestedAggregation('stock_metrics', 'stock_metrics').agg(nestedQuery);
        },
    };

    static TotalStockAggregation: RequestBodySearchItem = {
        key: 'total_stock_aggregation',
        buildQuery: () => {
            const boolQuery = esb
                .boolQuery()
                .must(esb.termQuery('type', 'simple'))
                .mustNot(esb.termQuery('status', 'deleted'));

            return requestBodySearch()
                .size(0)
                .query(boolQuery)
                .agg(new SumAggregation('stock').field('stock').missing('0'));
        },
    };

    static TotalGrossSalesAggregation: RequestBodySearchItem = {
        key: 'total_gross_sales_aggregation',
        buildQuery: (options) => {
            const { fromDate, toDate } = options;

            const filterQuery = esb.boolQuery().mustNot(esb.existsQuery('sales_metrics.h_warehouse_key'));
            if (fromDate && toDate) {
                filterQuery.must(esb.rangeQuery('sales_metrics.date').gte(fromDate).lte(toDate));
            }

            return requestBodySearch()
                .size(0)
                .agg(
                    esb
                        .nestedAggregation('sales_metrics', 'sales_metrics')
                        .agg(
                            esb
                                .filterAggregation('nested', filterQuery)
                                .agg(
                                    esb
                                        .sumAggregation('total_gross_sales', 'sales_metrics.gross_sales')
                                        .missing('0'),
                                ),
                        ),
                );
        },
    };

    static MaxScoreAggregation: AggregationItem = {
        key: 'max_score',
        buildQuery: () => {
            return new esb.MaxAggregation('max_score').script(esb.script('inline', '_score'));
        },
    };

    static TotalInTransitStockAggregation: RequestBodySearchItem = {
        key: 'total_in_transit_stock_aggregation',
        buildQuery: () => {
            return requestBodySearch()
                .size(0)
                .agg(
                    esb
                        .termsAggregation('by_source', 'from_source')
                        .size(MAX_BUCKET_SIZE)
                        .agg(
                            esb
                                .nestedAggregation('stock_metrics', 'stock_metrics')
                                .agg(esb.maxAggregation('max_date', 'stock_metrics.date').missing('0')),
                        ),
                );
        },
    };
}
