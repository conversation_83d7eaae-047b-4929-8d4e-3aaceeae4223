import { RedisCacheConfig } from '@configs';
import { Logger } from '@heronjs/common';
import { RedisUtil } from '@utils';

interface CacheParams {
    tenantId: string;
    entityName: string;
    subType: string;
    userId?: string;
    query: Record<string, unknown>;
    fields?: string[];
    logger?: Logger;
    ttl?: number;
}

export async function withCache<T>(params: CacheParams, handler: () => Promise<T>): Promise<T> {
    const {
        tenantId,
        entityName,
        subType,
        query,
        fields = [],
        logger,
        ttl = RedisCacheConfig.config.config.ttl,
    } = params;

    const redisKey = RedisUtil.generateKey(tenantId, entityName, subType, query, fields);

    const cache = useCache();

    try {
        const cacheResult = await cache.store.get(redisKey);
        if (cacheResult) {
            logger?.info?.(`Cache hit ${redisKey}`);
            return cacheResult;
        }
    } catch (error) {
        logger?.error?.(`Cache utility error: ${error}`);
        return handler();
    }

    // cache miss -> run handler
    const output = await handler();

    // Cache the result
    cache.store.set(redisKey, output, ttl).catch((err: Error) => {
        logger?.error?.(`Failed to cache result: ${err}`);
    });

    return output;
}
