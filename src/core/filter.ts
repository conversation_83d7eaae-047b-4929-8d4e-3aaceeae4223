import { FilterInput, SortType } from '@cbidigital/aqua-ddd';
import { filterFactory } from '@features/opensearch';
import { Optional } from '@heronjs/common';
import esb, { boolQuery, BucketSelectorAggregation, BucketSortAggregation, Sort } from 'elastic-builder';
import { FieldSchema } from './dao';

export enum FilterTypes {
    Numeric,
    String,
    Boolean,
}

export enum Operators {
    Equal = 'eq',
    NotEqual = 'neq',
    LessThan = 'lt',
    LessThanOrEqual = 'lte',
    GreaterThan = 'gt',
    GreaterThanOrEqual = 'gte',
    Contains = 'contains',
    Startswith = 'startswith',
    Endswith = 'endswith',
    InsensitiveContains = 'icontains',
    InsensitiveStarswith = 'istartswith',
    InsensitiveEndswith = 'iendswith',
    Match = 'match',
    In = 'in',
    NotIn = 'nin',
    Exists = 'exists',
    NotExists = 'nexists',
}
export enum NumericOperators {
    Equal = 'eq',
    NotEqual = 'neq',
    LessThan = 'lt',
    LessThanOrEqual = 'lte',
    GreaterThan = 'gt',
    GreaterThanOrEqual = 'gte',
    In = 'in',
    NotIn = 'nin',
    Exists = 'exists',
    NotExists = 'nexists',
}
export enum StringOperators {
    Equal = 'eq',
    NotEqual = 'neq',
    Contains = 'contains',
    Startswith = 'startswith',
    Endswith = 'endswith',
    InsensitiveContains = 'icontains',
    InsensitiveStarswith = 'istartswith',
    InsensitiveEndswith = 'iendswith',
    Match = 'match',
    In = 'in',
    NotIn = 'nin',
    Exists = 'exists',
    NotExists = 'nexists',
}
export enum BooleanOperators {
    Equal = 'eq',
    NotEqual = 'neq',
    Exists = 'exists',
    NotExists = 'nexists',
}

export type FilterValue = number | string | boolean;

export type FilterPayload = {
    operator: string;
    value: FilterValue | FilterValue[];
};

export abstract class Filter {
    readonly type: FilterTypes;
    readonly operator: string;
    readonly value: FilterValue | FilterValue[];
    readonly compareField: string;
    readonly nestedPath?: string;
    readonly autoApply?: boolean;
    readonly customFilter?: (query: esb.BoolQuery) => void;
    constructor({
        type,
        operator,
        value,
        compareField,
        nestedPath,
        autoApply,
        customFilter,
    }: FilterPayload & {
        type: FilterTypes;
        compareField: string;
        nestedPath?: string;
        autoApply?: boolean;
        customFilter?: (query: esb.BoolQuery) => void;
    }) {
        this.type = type;
        this.operator = operator;
        this.value = value;
        this.compareField = compareField;
        this.nestedPath = nestedPath;
        this.autoApply = autoApply ?? true;
        this.customFilter = customFilter;
    }

    abstract apply(query: esb.BoolQuery): any;
}

export class MetadataFilter extends Filter {
    constructor({
        type,
        operator,
        value,
        compareField,
        nestedPath,
        autoApply,
        customFilter,
    }: FilterPayload & {
        type: FilterTypes;
        compareField: string;
        nestedPath?: string;
        autoApply?: boolean;
        customFilter?: (query: esb.BoolQuery) => void;
    }) {
        super({ type, operator, value, compareField, nestedPath, autoApply, customFilter });
    }

    apply(query: esb.BoolQuery, force?: boolean) {
        if (!this.autoApply && !force) return;

        const value = this.value;

        // Process handler of filter
        switch (this.type) {
            case FilterTypes.Numeric:
                numericOperatorHandler(
                    query,
                    this.operator,
                    value,
                    this.compareField,
                    this.nestedPath,
                    this.customFilter,
                );
                break;
            case FilterTypes.String:
                stringOperatorHandler(
                    query,
                    this.operator,
                    value,
                    this.compareField,
                    this.nestedPath,
                    this.customFilter,
                );
                break;
            case FilterTypes.Boolean:
                booleanOperatorHandler(
                    query,
                    this.operator,
                    value,
                    this.compareField,
                    this.nestedPath,
                    this.customFilter,
                );
                break;
        }
    }
}

export class BucketFilter extends Filter {
    constructor({
        type,
        operator,
        value,
        compareField,
    }: FilterPayload & {
        type: FilterTypes;
        compareField: string;
    }) {
        super({ type, operator, value, compareField });
    }

    apply() {
        return buildScriptCondition(this.compareField, this.operator, this.value);
    }
}

const buildScriptCondition = (compareField: string, operator: string, value: any) => {
    switch (operator) {
        case Operators.GreaterThan:
            return `(params.${compareField} != null && params.${compareField} > ${value})`;
        case Operators.GreaterThanOrEqual:
            return `(params.${compareField} != null && params.${compareField} >= ${value})`;
        case Operators.LessThan:
            return `(params.${compareField} != null && params.${compareField} < ${value})`;
        case Operators.LessThanOrEqual:
            return `(params.${compareField} != null && params.${compareField} <= ${value})`;
        case Operators.Equal:
            return `(params.${compareField} != null && params.${compareField} == ${value})`;
        case Operators.NotEqual:
            return `(params.${compareField} != null && params.${compareField} != ${value})`;
        case Operators.In: {
            const script = value.map((v: any) => `params.${compareField} == ${v}`).join(' || ');
            return `(${script})`;
        }
        case Operators.NotIn: {
            const script = value.map((v: any) => `params.${compareField} != ${v}`).join(' && ');
            return `(${script})`;
        }
        case Operators.Exists: {
            if (value === true) return `(params.${compareField} != null)`;
            else return `(params.${compareField} == null)`;
        }
        default:
            throw new Error(`Unknown operator: ${operator}`);
    }
};

const numericOperatorHandler = (
    builder: esb.BoolQuery,
    operator: string,
    value: any,
    compareField: string,
    nestedPath?: string,
    customFilter?: (query: esb.BoolQuery) => void,
) => {
    const query = esb.boolQuery();
    if (customFilter) customFilter(query);
    builder.filter(query);

    switch (operator) {
        case NumericOperators.Equal: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.filter(esb.termQuery(compareField, value));
            }
            break;
        }

        case NumericOperators.NotEqual: {
            if (nestedPath) {
                query.mustNot(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.mustNot(esb.termQuery(compareField, value));
            }
            break;
        }

        case NumericOperators.LessThan: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.rangeQuery(compareField).lt(value), nestedPath));
            } else {
                query.filter(esb.rangeQuery(compareField).lt(value));
            }
            break;
        }

        case NumericOperators.LessThanOrEqual: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.rangeQuery(compareField).lte(value), nestedPath));
            } else {
                query.filter(esb.rangeQuery(compareField).lte(value));
            }
            break;
        }

        case NumericOperators.GreaterThan: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.rangeQuery(compareField).gt(value), nestedPath));
            } else {
                query.filter(esb.rangeQuery(compareField).gt(value));
            }
            break;
        }

        case NumericOperators.GreaterThanOrEqual: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.rangeQuery(compareField).gte(value), nestedPath));
            } else {
                query.filter(esb.rangeQuery(compareField).gte(value));
            }
            break;
        }

        case NumericOperators.In: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.termsQuery(compareField, value), nestedPath));
            } else {
                query.filter(esb.termsQuery(compareField, value));
            }
            break;
        }

        case NumericOperators.NotIn: {
            if (nestedPath) {
                query.mustNot(esb.nestedQuery(esb.termsQuery(compareField, value), nestedPath));
            } else {
                query.mustNot(esb.termsQuery(compareField, value));
            }
            break;
        }

        case NumericOperators.Exists: {
            if (value === true) {
                if (nestedPath) {
                    query.filter(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
                } else {
                    query.filter(esb.existsQuery(compareField));
                }
            } else {
                if (nestedPath) {
                    query.mustNot(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
                } else {
                    query.mustNot(esb.existsQuery(compareField));
                }
            }
            break;
        }

        case NumericOperators.NotExists: {
            if (value === true) {
                if (nestedPath) {
                    query.mustNot(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
                } else {
                    query.mustNot(esb.existsQuery(compareField));
                }
            } else {
                if (nestedPath) {
                    query.filter(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
                } else {
                    query.filter(esb.existsQuery(compareField));
                }
            }
            break;
        }

        default:
            throw new Error(`Operator "${operator}" isn't numeric operator`);
    }
};

const stringOperatorHandler = (
    builder: esb.BoolQuery,
    operator: string,
    value: any,
    compareField: string,
    nestedPath?: string,
    customFilter?: (query: esb.BoolQuery) => void,
) => {
    if (typeof value === 'string') value = value.trim();

    const query = esb.boolQuery();
    if (customFilter) customFilter(query);
    builder.filter(query);

    switch (operator) {
        case StringOperators.Match: {
            if (nestedPath) {
                query.must(
                    esb.nestedQuery(
                        esb
                            .matchQuery(compareField.replace('.keyword', ''), value)
                            .fuzziness('AUTO')
                            .operator('and'),
                        nestedPath,
                    ),
                );
            } else {
                query.must(
                    esb
                        .matchQuery(compareField.replace('.keyword', ''), value)
                        .fuzziness('AUTO')
                        .operator('and'),
                );
            }
            break;
        }

        case StringOperators.Equal: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.filter(esb.termQuery(compareField, value));
            }
            break;
        }

        case StringOperators.NotEqual: {
            if (nestedPath) {
                query.mustNot(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.mustNot(esb.termQuery(compareField, value));
            }
            break;
        }

        case StringOperators.Contains: {
            if (nestedPath) {
                query.must(esb.nestedQuery(esb.wildcardQuery(compareField, `*${value}*`), nestedPath));
            } else {
                query.must(esb.wildcardQuery(compareField, `*${value}*`));
            }
            break;
        }

        case StringOperators.InsensitiveContains: {
            if (nestedPath) {
                query.must(
                    esb.nestedQuery(
                        esb.wildcardQuery(compareField, `*${value}*`).caseInsensitive(true),
                        nestedPath,
                    ),
                );
            } else {
                query.must(esb.wildcardQuery(compareField, `*${value}*`).caseInsensitive(true));
            }
            break;
        }

        case StringOperators.Startswith: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.wildcardQuery(compareField, `${value}*`), nestedPath));
            } else {
                query.filter(esb.wildcardQuery(compareField, `${value}*`));
            }
            break;
        }

        case StringOperators.InsensitiveStarswith: {
            if (nestedPath) {
                query.filter(
                    esb.nestedQuery(
                        esb.wildcardQuery(compareField, `${value}*`).caseInsensitive(true),
                        nestedPath,
                    ),
                );
            } else {
                query.filter(esb.wildcardQuery(compareField, `${value}*`).caseInsensitive(true));
            }
            break;
        }

        case StringOperators.Endswith: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.wildcardQuery(compareField, `*${value}`), nestedPath));
            } else {
                query.filter(esb.wildcardQuery(compareField, `*${value}`));
            }
            break;
        }

        case StringOperators.InsensitiveEndswith: {
            if (nestedPath) {
                query.filter(
                    esb.nestedQuery(
                        esb.wildcardQuery(compareField, `*${value}`).caseInsensitive(true),
                        nestedPath,
                    ),
                );
            } else {
                query.filter(esb.wildcardQuery(compareField, `*${value}`).caseInsensitive(true));
            }
            break;
        }

        case StringOperators.Exists: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
            } else {
                query.filter(esb.existsQuery(compareField));
            }
            break;
        }

        case StringOperators.In: {
            if (typeof value === 'string') {
                value = value.split(',').map((i) => i.trim());
            }
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.termsQuery(compareField, value), nestedPath));
            } else {
                query.filter(esb.termsQuery(compareField, value));
            }
            break;
        }

        case StringOperators.NotIn: {
            if (typeof value === 'string') {
                value = value.split(',').map((i) => i.trim());
            }
            if (nestedPath) {
                builder.mustNot(esb.nestedQuery(esb.termsQuery(compareField, value), nestedPath));
            } else {
                builder.mustNot(esb.termsQuery(compareField, value));
            }
            break;
        }

        default:
            throw new Error(`Operator "${operator}" isn't string operator`);
    }
};

const booleanOperatorHandler = (
    builder: esb.BoolQuery,
    operator: string,
    value: any,
    compareField: string,
    nestedPath?: string,
    customFilter?: (query: esb.BoolQuery) => void,
) => {
    const query = esb.boolQuery();
    if (customFilter) customFilter(query);
    builder.filter(query);

    switch (operator) {
        case BooleanOperators.Equal: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.filter(esb.termQuery(compareField, value));
            }
            break;
        }

        case BooleanOperators.NotEqual: {
            if (nestedPath) {
                query.mustNot(esb.nestedQuery(esb.termQuery(compareField, value), nestedPath));
            } else {
                query.mustNot(esb.termQuery(compareField, value));
            }
            break;
        }

        case BooleanOperators.Exists: {
            if (nestedPath) {
                query.filter(esb.nestedQuery(esb.existsQuery(compareField), nestedPath));
            } else {
                query.filter(esb.existsQuery(compareField));
            }
            break;
        }

        default:
            throw new Error(`Operator "${operator}" isn't boolean operator`);
    }
};

export const buildQueryAndFilterAgg = ({
    filter,
    search,
    fieldSchema,
    searchFields,
}: {
    filter?: FilterInput;
    search?: string;
    fieldSchema: FieldSchema;
    searchFields?: string[];
    nestedPath?: string;
}) => {
    const filterQuery = boolQuery();
    let filterAgg: Optional<BucketSelectorAggregation>;
    let filterAggScript = '';
    let filterAggBucketsPath: Record<string, string> = {};

    if (search && searchFields)
        filterQuery.must(esb.multiMatchQuery().fields(searchFields).query(search).type('phrase'));

    if (filter) {
        const filterModels = filterFactory.create(filter, fieldSchema);

        filterModels.forEach((filterModel) => {
            if (filterModel instanceof MetadataFilter) filterModel.apply(filterQuery);
            if (filterModel instanceof BucketFilter) {
                filterAggScript = filterAggScript += `${
                    filterAggScript.length ? ' && ' : ''
                }${filterModel.apply()}`;
                filterAggBucketsPath = {
                    ...filterAggBucketsPath,
                    [filterModel.compareField]: filterModel.compareField,
                };
            }
        });
    }

    if (filterAggScript.length)
        filterAgg = esb
            .bucketSelectorAggregation('filtered_results')
            .bucketsPath(filterAggBucketsPath)
            .script(filterAggScript);

    return { filterAgg, filterQuery };
};

export const buildSortAgg = ({
    sort,
    fieldSchema,
    defaultSort,
}: {
    sort: Record<string, SortType>[];
    fieldSchema: FieldSchema;
    defaultSort?: Record<string, SortType>[];
}) => {
    const sortFields: Sort[] = [...getSort({ sort, fieldSchema })];

    if (defaultSort) sortFields.push(...getSort({ sort: defaultSort, fieldSchema }));

    if (sortFields.length) return new BucketSortAggregation('sorted_results').sort(sortFields);
};

export const getSort = ({
    sort,
    fieldSchema,
}: {
    sort: Record<string, SortType>[];
    fieldSchema: FieldSchema;
}) => {
    const sortFields: Sort[] = [];

    sort.forEach((sortItem) => {
        const [sortBy, sortType] = Object.entries(sortItem)[0];
        const schema = fieldSchema[sortBy];
        if (schema && schema.sortable) {
            const sortKey = schema.overrideSortKey ?? schema.key;
            let sortOrder = sortType;
            if (schema.reverseSortOrder) {
                if (sortOrder === 'asc') sortOrder = 'desc';
                else sortOrder = 'asc';
            }
            if (sortKey) sortFields.push(new Sort(sortKey, sortOrder));
        }
    });

    return sortFields;
};

export const addMetadataSortToQuery = ({
    query,
    sort,
    fieldSchema,
    defaultSort,
}: {
    query: esb.RequestBodySearch;
    sort: Record<string, SortType>[];
    fieldSchema: FieldSchema;
    defaultSort?: Record<string, SortType>[];
}) => {
    const sortFields: Sort[] = [...getSort({ sort, fieldSchema })];

    if (defaultSort) sortFields.push(...getSort({ sort: defaultSort, fieldSchema }));

    if (sortFields.length) {
        sortFields.forEach((sortField) => {
            query.sort(sortField);
        });
    }
};
