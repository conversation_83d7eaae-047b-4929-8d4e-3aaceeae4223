import { FilterInput } from '@cbidigital/aqua-ddd';

export type PaginationInput<T = unknown> = {
    offset?: number;
    limit?: number;
    sort?: Record<string, 'asc' | 'desc'>[];
    search?: string;
    filter?: FilterInput<T>;
    fromDate?: string;
    toDate?: string;
    forecastFromDate?: string;
    forecastToDate?: string;
    fields?: string[];
    semanticSearch?: SemanticSearch;
} & Record<string, any>;

export type PaginationOutput<T = unknown> = {
    total_count: number;
    items: T[];
};

export type SemanticSearch = {
    text: string;
    minScore?: number;
};
