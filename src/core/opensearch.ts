import { FilterInput } from '@cbidigital/aqua-ddd';
import { Optional } from '@heronjs/common';
import esb, { BucketSelectorAggregation, BucketSortAggregation, TopHitsAggregation } from 'elastic-builder';

export const MAX_BUCKET_SIZE = 2147483647;

export type QueryOptions = {
    from?: number;
    size?: number;
    fields?: string[];
    ids?: string[];
    fromDate?: string;
    toDate?: string;
    forecastFromDate?: string;
    forecastToDate?: string;
    sort?: Record<string, 'asc' | 'desc'>[];
    filter?: FilterInput;
    search?: string;
    isSearchingProduct?: boolean;
    fromSource?: string;
    metadata?: Record<string, any>;
} & Record<string, any>;

export type AggregationItem = {
    key: string;
    options?: QueryOptions;
    children?: AggregationItem[];
    buildQuery: (options: QueryOptions, children?: AggregationItem[]) => esb.Aggregation;
};
export type RequestBodySearchItem = {
    key: string;
    buildQuery: (options: QueryOptions) => esb.RequestBodySearch;
};

export const buildAggregation = ({
    aggregation,
    options,
    children,
    sortAgg,
    filterAgg,
    metadataAgg,
}: {
    aggregation: esb.Aggregation;
    options: any;
    children?: AggregationItem[];
    sortAgg?: BucketSortAggregation;
    filterAgg?: BucketSelectorAggregation;
    metadataAgg?: TopHitsAggregation;
}): esb.Aggregation => {
    let { fields }: { fields: Optional<string[]> } = options;

    if (!fields || fields.length === 0) fields = children?.map((child) => child.key);

    const processedKeys = new Set();

    fields?.forEach((field) => {
        const key: string = field;
        if (processedKeys.has(key)) return;

        const matchedQueries = children?.filter((child) => checkMatchedQuery(key, child));
        if (matchedQueries) {
            matchedQueries.forEach((matchedQuery) => {
                if (matchedQuery.options)
                    aggregation.agg(matchedQuery.buildQuery(matchedQuery.options, matchedQuery.children));
                else aggregation.agg(matchedQuery.buildQuery(options, matchedQuery.children));
            });
        }

        processedKeys.add(key);
    });

    if (metadataAgg) aggregation.agg(metadataAgg);
    if (filterAgg) aggregation.agg(filterAgg);
    if (sortAgg) aggregation.agg(sortAgg);

    return aggregation;
};

const checkMatchedQuery = (key: string, agg: AggregationItem): boolean => {
    if (agg.key === key) return true;

    if (!agg.children) return false;

    const childMatched = agg.children?.some(
        (child) => child.key === key || child.children?.some((c) => checkMatchedQuery(key, c)),
    );

    return childMatched ?? false;
};
