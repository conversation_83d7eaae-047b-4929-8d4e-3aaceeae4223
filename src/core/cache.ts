import { RedisCacheConfig } from '@configs';
import { Logger } from '@heronjs/common';
import { RedisUtil } from '@utils';

export function CacheDAO(ttl: number = RedisCacheConfig.config.config.ttl) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        const originalMethod = descriptor.value;
        const logger = new Logger('CacheDAODecorator');

        descriptor.value = async function (...args: any[]) {
            const cache = useCache();
            const query = args[0] || {};
            const context = args[1] || {};
            const tenantId = context.tenantId;

            const key = RedisUtil.generateKey(
                tenantId,
                target.constructor.name as string,
                propertyKey,
                query,
            );

            try {
                const cacheResult = await cache.store.get(key);
                if (cacheResult) {
                    logger.info(`Cache hit ${key}`);
                    return cacheResult;
                }
            } catch (error) {
                logger.error(`Cache decorator error: ${error}`);
                return originalMethod.apply(this, args);
            }

            // Execute original method if cache miss
            const result = await originalMethod.apply(this, args);

            // Cache the result
            cache.store.set(key, result, ttl).catch((err: Error) => {
                logger?.error?.(`Failed to cache result: ${err}`);
            });

            return result;
        };

        return descriptor;
    };
}
