import { QueryInput, QueryInputFindOne, RepositoryOptions } from '@cbidigital/aqua-ddd';
import {
    Logger,
    ModuleDataSource,
    NotImplementedError,
    Optional,
    SQLError,
    SQLErrors,
} from '@heronjs/common';
import { KnexClient } from '@heronjs/core';
import { Knex } from 'knex';
import { BucketFilter, MetadataFilter } from './filter';

export type FieldSchema<T extends string = any> = {
    [P in T]: {
        key: P;
        filterable: boolean;
        filterClass?: typeof MetadataFilter | typeof BucketFilter;
        sortable: boolean;
        overrideSortKey?: string;
        reverseSortOrder?: boolean;
        requiredKeys?: T[];
        hidden?: boolean;
    };
};

export interface IBaseDao<T> {
    db: ModuleDataSource<KnexClient>;

    startTrx(options?: RepositoryOptions): Promise<Knex.Transaction>;
    commitTrx(trx: Knex.Transaction): Promise<void>;
    rollbackTrx(trx: Knex.Transaction): Promise<void>;
    transaction(exec: (trx: Knex.Transaction) => Promise<any>): Promise<void>;

    create(dto: T, options?: RepositoryOptions): Promise<T>;
    createList(dtos: T[], options?: RepositoryOptions): Promise<T[]>;
    updateById(id: string, dto: Partial<T>, options?: RepositoryOptions): Promise<Partial<T>>;
    updateList(dtos: (Partial<T> & { id: string })[], options?: RepositoryOptions): Promise<Partial<T>[]>;
    deleteById(id: string, options?: RepositoryOptions): Promise<string>;
    deleteList(ids: string[], options?: RepositoryOptions): Promise<string[]>;
    find(payload?: QueryInput<T>, options?: RepositoryOptions): Promise<Partial<T>[]>;
    count(payload?: QueryInput<T>, options?: RepositoryOptions): Promise<number>;
    findOne(payload?: QueryInputFindOne<T>, options?: RepositoryOptions): Promise<Optional<Partial<T>>>;
}

export class BaseDao<T> implements IBaseDao<T> {
    private readonly _db: ModuleDataSource<KnexClient>;
    private readonly _tableName: string;
    constructor({ db, tableName }: { db: ModuleDataSource<KnexClient>; tableName: string }) {
        this._db = db;
        this._tableName = tableName;
    }

    get db() {
        return this._db;
    }

    get tableName() {
        return this._tableName;
    }

    getClient(tenantId?: string): KnexClient {
        const client = this.db.database(tenantId);
        if (!client) throw new SQLError(SQLErrors.CONNECTION_ERR, 'Database client is undefined');
        return client;
    }

    async startTrx(options?: RepositoryOptions): Promise<Knex.Transaction> {
        const tenantId = options?.tenantId;
        return this.getClient(tenantId).transaction();
    }

    async commitTrx(trx: Knex.Transaction): Promise<void> {
        await trx.commit();
    }

    async rollbackTrx(trx: Knex.Transaction): Promise<void> {
        await trx.rollback();
    }

    async transaction(exec: (trx: Knex.Transaction) => Promise<any>, options?: RepositoryOptions) {
        const tenantId = options?.tenantId;
        return this.getClient(tenantId).transaction((trx) => exec(trx));
    }

    async create(dto: T, options: RepositoryOptions = {}): Promise<T> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async createList(dtos: T[], options: RepositoryOptions = {}): Promise<T[]> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async updateById(id: string, dto: Partial<T>, options: RepositoryOptions = {}): Promise<Partial<T>> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async updateList(
        dtos: (Partial<T> & { id: string })[],
        options: RepositoryOptions = {},
    ): Promise<Partial<T>[]> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async deleteById(id: string, options: RepositoryOptions = {}): Promise<string> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async deleteList(ids: string[], options: RepositoryOptions = {}): Promise<string[]> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async find(payload: QueryInput<T> = {}, options: RepositoryOptions = {}): Promise<Partial<T>[]> {
        try {
            try {
                throw new NotImplementedError('Method not implemented.');
            } catch (error) {
                throw this.transformError(error);
            }
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async count(payload: QueryInput<T> = {}, options: RepositoryOptions = {}): Promise<number> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    async findOne(
        payload: QueryInputFindOne<T> = {},
        options: RepositoryOptions = {},
    ): Promise<Optional<Partial<T>>> {
        try {
            throw new NotImplementedError('Method not implemented.');
        } catch (error) {
            throw this.transformError(error);
        }
    }

    protected transformError(err: any) {
        const logger = new Logger(this.constructor.name);
        logger.error(err);
        return err;
    }
}
