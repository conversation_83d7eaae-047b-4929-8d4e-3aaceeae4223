export interface IRecordMapper<DTO, Record> {
    fromRecordToDto(record: Partial<Record>): Partial<DTO>;
    // fromDtoToRecord(dto: Partial<DTO>): Partial<Record>;
    fromRecordsToDtos(records: Partial<Record>[]): Partial<DTO>[];
    // fromDtosToRecords(dtos: Partial<DTO>[]): Partial<Record>[];
}

export abstract class BaseRecordMapper<DTO = any, Record = any> implements IRecordMapper<DTO, Record> {
    abstract fromRecordToDto(record: Partial<Record>): Partial<DTO>;
    // abstract fromDtoToRecord(dto: Partial<DTO>): Partial<Record>;

    fromRecordsToDtos(records: Partial<Record>[]): Partial<DTO>[] {
        return records.map((record) => this.fromRecordToDto(record));
    }

    // fromDtosToRecords(dtos: Partial<DTO>[]): Partial<Record>[] {
    //     return dtos.map((dto) => this.fromDtoToRecord(dto));
    // }
}
