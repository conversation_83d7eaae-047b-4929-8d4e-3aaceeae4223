import { CommonDataSourceConfig, DataSourceClient, DataSourceDriver } from '@heronjs/common';
import { readFileSync } from 'fs';

export const OPENSEARCH_DATA_SOURCE_CONFIG = {
    NODE: process.env.OPENSEARCH_NODE ?? 'http://host.docker.internal:9200',
    USERNAME: process.env.OPENSEARCH_USERNAME ?? '',
    PASSWORD: process.env.OPENSEARCH_PASSWORD ?? '',
    REGION: process.env.OPENSEARCH_REGION ?? '',
    SERVICE: process.env.OPENSEARCH_SERVICE ?? '',
    ACCESS_KEY: process.env.OPENSEARCH_ACCESS_KEY ?? '',
    SECRET_KEY: process.env.OPENSEARCH_SECRET_KEY ?? '',
};

export const OPENSEARCH_CLUSTER_DATA_SOURCE_CONFIG = {
    NODE: process.env.OPENSEARCH_CLUSTER_URL ?? 'http://host.docker.internal:9200',
    USERNAME: process.env.OPENSEARCH_CLUSTER_USERNAME ?? '',
    PASSWORD: process.env.OPENSEARCH_CLUSTER_PASSWORD ?? '',
};

export const POSTGRES_DATA_SOURCE_CONFIG = {
    client: DataSourceClient.KNEX,
    config: <CommonDataSourceConfig>{
        host: process.env.POSTGRES_HOST || 'localhost',
        port: Number(process.env.POSTGRES_PORT) || 5432,
        user: process.env.POSTGRES_USER || 'dev',
        password: process.env.POSTGRES_PASSWORD || 'dev@123',
        database: process.env.POSTGRES_DATABASE || 'dev',
        pooling: {
            min: Number(process.env.POSTGRES_POOLING_MIN) || 2,
            max: Number(process.env.POSTGRES_POOLING_MAX) || 10,
        },
        driver: DataSourceDriver.POSTGRES,
        cluster: process.env.POSTGRES_SLAVES
            ? {
                  slaves: process.env.POSTGRES_SLAVES || '',
              }
            : undefined,
        ssl: process.env.USE_TLS === 'true' ? { rejectUnauthorized: false } : undefined,
        tls: process.env.SSL_CERT
            ? {
                  ssl: readFileSync(process.env.SSL_CERT).toString('utf-8'),
                  enabled: true,
              }
            : undefined,
    },
};
