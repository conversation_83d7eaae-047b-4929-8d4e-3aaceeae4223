import { CacheClient, ModuleStore, RedisOptions, StoreType } from '@heronjs/common';

export const RedisCacheConfig: ModuleStore = {
    type: StoreType.CACHE,
    name: 'redis',
    isDefault: true,
    config: {
        client: CacheClient.REDIS,
        config: <RedisOptions>{
            host: process.env.REDIS_HOST || 'redis',
            port: Number(process.env.REDIS_PORT || 6379),
            db: Number(process.env.REDIS_DB || 0),
            ttl: Number(process.env.REDIS_TTL || 600000), // milliseconds
            password: process.env.REDIS_PASSWORD || 'dev@123',
        },
    },
};
