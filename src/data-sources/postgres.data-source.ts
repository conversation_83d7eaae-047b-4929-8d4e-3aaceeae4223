import { IDatabase, RepositoryOptions } from '@cbidigital/aqua-ddd';
import { Default, SQLError, SQLErrors, UseConfig } from '@heronjs/common';
import { AbstractTenancyDataSourceClient, KnexClient } from '@heronjs/core';
import { Knex } from 'knex';
import { POSTGRES_DATA_SOURCE_CONFIG } from '../configs';

@UseConfig(POSTGRES_DATA_SOURCE_CONFIG)
@Default()
export class PostgresDataSource extends AbstractTenancyDataSourceClient<KnexClient> implements IDatabase {
    constructor() {
        super();
    }

    getClient(tenantId?: string): KnexClient {
        const client = this.database(tenantId);
        if (!client) throw new SQLError(SQLErrors.CONNECTION_ERR, 'Database client is undefined');
        return client;
    }

    async startTrx(options?: RepositoryOptions): Promise<Knex.Transaction> {
        const tenantId = options?.tenantId;
        return this.getClient(tenantId).transaction();
    }

    async commitTrx(trx: Knex.Transaction): Promise<void> {
        await trx.commit();
    }

    async rollbackTrx(trx: Knex.Transaction): Promise<void> {
        await trx.rollback();
    }

    async withTransaction<R>(
        operation: (trx: Knex.Transaction, client: KnexClient) => Promise<R>,
        options: RepositoryOptions = {},
    ): Promise<R> {
        const externalTrx = options?.trx;
        const client = this.getClient(options?.tenantId);
        const trx = externalTrx ?? (await client.transaction());

        try {
            const result = await operation(trx, client);
            if (!externalTrx) await trx.commit();
            return result;
        } catch (error) {
            if (!externalTrx) await trx.rollback();
            throw error;
        }
    }
}
