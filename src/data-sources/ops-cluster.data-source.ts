import { OP<PERSON><PERSON><PERSON><PERSON>_CLUSTER_DATA_SOURCE_CONFIG } from '@configs';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { Lifecycle, Logger, Provider } from '@heronjs/common';
import { ApiResponse, Client } from '@opensearch-project/opensearch';
import { Search } from '@opensearch-project/opensearch/api/requestParams';
import { TransportRequestOptions } from '@opensearch-project/opensearch/lib/Transport';
import { withCache } from '@utils';
import { Agent } from 'https';
import { IOPSDataSource } from './ops.data-source';

@Provider({
    token: OPENSEARCH_INJECT_TOKENS.OPS_DATA_SOURCE,
    scope: Lifecycle.Singleton,
})
export class OPSClusterDataSource implements IOPSDataSource {
    private readonly logger = new Logger(this.constructor.name);
    private client: Client;

    constructor() {
        this.client = new Client({
            agent: () => new Agent({ family: 4 }), // forces IPv4 to fix issues ConnectError when using VPN
            node: OPENSEARCH_CLUSTER_DATA_SOURCE_CONFIG.NODE,
            auth: {
                username: OPENSEARCH_CLUSTER_DATA_SOURCE_CONFIG.USERNAME,
                password: OPENSEARCH_CLUSTER_DATA_SOURCE_CONFIG.PASSWORD,
            },
        });
    }

    public getClient(): Client {
        return this.client;
    }

    async search({
        tenantId,
        params,
        options,
    }: {
        tenantId: string;
        params?: Search<object | Record<string, unknown>>;
        options?: TransportRequestOptions;
    }): Promise<ApiResponse<Record<string, any>, unknown>> {
        return withCache(
            {
                tenantId,
                entityName: this.constructor.name,
                subType: 'search',
                query: { ...params },
                logger: this.logger,
            },
            async () => {
                return await this.client.search(params, options);
            },
        );
    }
}
