// @deprecated This file is deprecated and should not be used in new code
import { OPENSEARCH_DATA_SOURCE_CONFIG } from '@configs';
import { OPENSEARCH_INJECT_TOKENS } from '@constants';
import { Lifecycle, Logger, Provider } from '@heronjs/common';
import { Client } from '@opensearch-project/opensearch';
import { Search } from '@opensearch-project/opensearch/api/requestParams';
import { AwsSigv4Signer } from '@opensearch-project/opensearch/aws-v3';
import { ApiResponse, TransportRequestOptions } from '@opensearch-project/opensearch/lib/Transport';
import { withCache } from '@utils';
import { Agent } from 'https';

export interface IOPSDataSource {
    getClient(): Client;
    search(payload: {
        tenantId: string;
        params?: Search<object | Record<string, unknown>>;
        options?: TransportRequestOptions;
    }): Promise<ApiResponse<Record<string, any>, unknown>>;
}

/**
 * @deprecated This class is deprecated and should not be used in new code
 */
@Provider({
    token: OPENSEARCH_INJECT_TOKENS.OPS_SERVERLESS_DATA_SOURCE,
    scope: Lifecycle.Singleton,
})
export class OPSServerlessDataSource implements IOPSDataSource {
    private readonly logger = new Logger(this.constructor.name);
    private client: Client;

    constructor() {
        this.client = new Client({
            agent: () => new Agent({ family: 4 }), // forces IPv4 to fix issues ConnectError when using VPN
            ...AwsSigv4Signer({
                region: OPENSEARCH_DATA_SOURCE_CONFIG.REGION,
                service: OPENSEARCH_DATA_SOURCE_CONFIG.SERVICE == 'aoss' ? 'aoss' : 'es',
                async getCredentials() {
                    return {
                        accessKeyId: OPENSEARCH_DATA_SOURCE_CONFIG.ACCESS_KEY,
                        secretAccessKey: OPENSEARCH_DATA_SOURCE_CONFIG.SECRET_KEY,
                    };
                },
            }),
            node: OPENSEARCH_DATA_SOURCE_CONFIG.NODE,
        });
    }

    public getClient(): Client {
        return this.client;
    }

    async search({
        tenantId,
        params,
        options,
    }: {
        tenantId: string;
        params?: Search<object | Record<string, unknown>>;
        options?: TransportRequestOptions;
    }): Promise<ApiResponse<Record<string, any>, unknown>> {
        return withCache(
            {
                tenantId,
                entityName: this.constructor.name,
                subType: 'search',
                query: { ...params },
                logger: this.logger,
            },
            async () => {
                return await this.client.search(params, options);
            },
        );
    }
}
