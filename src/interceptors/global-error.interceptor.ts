import { APIError, <PERSON>rro<PERSON>, HttpResponseUtils, isString, Logger, RuntimeError } from '@heronjs/common';
import { ExpressErrorInterceptor, HttpRequest, HttpResponse, HttpNext as Next } from '@heronjs/express';
import { StatusCodes } from 'http-status-codes';

const GlobalApiErrorInterceptor: ExpressErrorInterceptor = (
    err: Error,
    req: HttpRequest,
    res: HttpResponse,
    next: Next,
) => {
    if (err) {
        const logger = new Logger('GlobalApiErrorInterceptor');

        if (err instanceof APIError) {
            const cin = isString(err.code) ? +err.code : err.code;
            return res.status(cin).send(HttpResponseUtils.error(err));
        }

        if (err instanceof SyntaxError) {
            return res.status(StatusCodes.BAD_REQUEST).send({
                message: err.message,
            });
        }

        if (err instanceof RuntimeError) {
            const cin = StatusCodes.BAD_REQUEST;
            let validationErrors;

            if (err.name === Errors.VALIDATION_ERR) {
                validationErrors = err.payload;
            }

            return res.status(cin).send({ ...HttpResponseUtils.error(err), validationErrors });
        }

        logger.error(err.message, err);

        return res.status(StatusCodes.INTERNAL_SERVER_ERROR).send({
            message: 'Internal server error',
        });
    }

    return next();
};

export { GlobalApiErrorInterceptor };
