import { INTERNAL_API_KEY } from '@configs';
import { APIError } from '@heronjs/common';
import {
    type ExpressInterceptor,
    type HttpNext as Next,
    type HttpRequest as Request,
    type HttpResponse as Response,
} from '@heronjs/express';
import { StatusCodes } from 'http-status-codes';

export const AuthInterceptor: ExpressInterceptor = (req: Request, res: Response, next: Next) => {
    const internalApiKey = req.headers['internal-api-key'];
    if (internalApiKey !== INTERNAL_API_KEY)
        throw new APIError(StatusCodes.UNAUTHORIZED, 'Invalid credentials.');
    return next();
};

export const RouteInterceptor: ExpressInterceptor = (req: Request, res: Response, next: Next) => {
    // console.log('route' + req.headers['route' + req.url]);
    return next();
};

export const ControllerInterceptor: ExpressInterceptor = (req: Request, res: Response, next: Next) => {
    // console.log('controller' + req.headers['controller' + req.url]);
    return next();
};
