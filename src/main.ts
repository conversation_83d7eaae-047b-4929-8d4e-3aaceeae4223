import { RedisCacheConfig } from '@configs';
import { DataSources, GateKeeper, Module, Stores } from '@heronjs/common';
import { HeronJS } from '@heronjs/core';
import 'reflect-metadata';
import { AuthContext } from './context';
import { PostgresDataSource } from './data-sources';
import { OpensearchDaemon, OpensearchModule } from './features';
import { GlobalApiErrorInterceptor } from './interceptors';

@Module({
    imports: [OpensearchModule],
    services: [OpensearchDaemon],
})
@GateKeeper(AuthContext, AuthContext.Resolver)
@DataSources([PostgresDataSource])
@Stores([RedisCacheConfig])
export class AppModule {}

const main = async () => {
    const app = await HeronJS.create({ module: AppModule });
    await app.listen({
        port: 3000,
        options: {
            cors: {
                origin: '*',
                preflightContinue: false,
                methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
            },
            globalError: GlobalApiErrorInterceptor,
        },
    });
};

(async () => main())();
