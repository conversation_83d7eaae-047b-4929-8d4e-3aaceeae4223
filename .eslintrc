{
    "root": true,
    "parser": "@typescript-eslint/parser",
    "plugins": ["@typescript-eslint", "prettier"],
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/eslint-recommended",
        "plugin:@typescript-eslint/recommended",
        "prettier"
    ],
    "rules": {
        "no-console": 0, // Means warning
        "prettier/prettier": 2, // Means error
        "@typescript-eslint/no-explicit-any": 0,
        "@typescript-eslint/ban-ts-comment": 0,
        "@typescript-eslint/no-non-null-assertion": 0,
        "@typescript-eslint/no-var-requires": 0,
        "@typescript-eslint/no-empty-interface": 0,
        "@typescript-eslint/no-namespace": 0,
        "@typescript-eslint/no-non-null-asserted-optional-chain": 0
    }
}
