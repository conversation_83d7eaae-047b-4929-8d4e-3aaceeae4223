"""
Sales bar chart represents a graphical representation of sales data over time.
It is typically used to visualize sales trends and patterns over a specific period.
"""
type SalesBarChart @doc(category: "Catalog") {
    """
    Start date for the analysis period.
    """
    period_start: String!

    """
    Actual gross quantity.
    """
    gross_qty: Int

    """
    Forecasted gross quantity.
    """
    forecasted_gross_qty: Int
}

enum TimeFrame {
    day
    week
    month
    quarter
    year
}

"""
Filter input for querying sales bar chart with various criteria.
"""
input SalesBarChartFilter @doc(category: "Catalog") {
    dummy: String
}

"""
Input parameters for querying sales bar chart with pagination, sorting, and filtering.
"""
input SalesBarChartQueryInput @doc(category: "Catalog") {
    """
    Unique hash key identifier for the item.
    """
    h_key: String!

    """
    Type of the item.
    """
    type: String!

    """
    Time frame for the analysis period.
    """
    time_frame: TimeFrame!

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String!

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String!
}

"""
Response type for sales bar chart queries containing results and metadata.
"""
type SalesBarChartQueryOutput @doc(category: "Catalog") {
    """
    Sales bar chart data.
    """
    items: [SalesBarChart]
}

extend type Query {
    """
    Query sales bar chart with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      sales_bar_chart(query: {
        h_key: "prod_123",
        type: "product",
        from_date: "2024-01-01",
        to_date: "2024-12-31"
      }) {
        data {
          period_start
          gross_qty
          forecasted_gross_qty
        }
      }
    }
    ```
    """
    sales_bar_chart(query: SalesBarChartQueryInput!): SalesBarChartQueryOutput! @doc(category: "Catalog")
}
