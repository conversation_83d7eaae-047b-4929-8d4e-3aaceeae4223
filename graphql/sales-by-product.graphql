"""
Collection information associated with products in sales analytics.
"""
type ProductCollection @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Collection name.
    """
    name: String!

    """
    Position of the product within the collection.
    """
    position: Int!

    """
    Unique hash key identifier for the collection.
    """
    h_key: String!
}

"""
Category information associated with products in sales analytics.
"""
type ProductCategory @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Category name.
    """
    name: String!

    """
    Unique hash key identifier for the category.
    """
    h_key: String!
}

"""
Tag information associated with products in sales analytics.
"""
type ProductTag @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Tag name.
    """
    name: String!

    """
    Unique hash key identifier for the tag.
    """
    h_key: String!
}

"""
Vendor/supplier information associated with products in sales analytics.
"""
type ProductVendor @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Vendor name.
    """
    name: String!

    """
    Unique hash key identifier for the vendor.
    """
    h_key: String!
}

"""
Comprehensive sales analytics data for individual products.
Contains detailed sales metrics, inventory data, forecasting, and performance indicators.
This is the primary entity for product-level business intelligence and reporting.
"""
type SalesByProductItem @doc(category: "Analytics") {
    """
    Unique hash key identifier for the product.
    """
    h_key: String!

    """
    Business key identifier.
    """
    b_key: String

    """
    Categories this product belongs to.
    """
    categories: [ProductCategory]

    """
    Collections this product is part of.
    """
    collections: [ProductCollection]

    """
    Product color attribute.
    """
    color: String

    """
    Original price before discounts.
    """
    compare_at_price: Float

    """
    Number of days the product has been available.
    """
    days_on_site: Int

    """
    DDA (Dynamic Data Attribute) code.
    """
    dda_code: String

    """
    DDA (Dynamic Data Attribute) value.
    """
    dda_value: String

    """
    Source system identifier.
    """
    from_source: String

    """
    Product image URL.
    """
    image: String

    """
    Product name.
    """
    name: String

    """
    Quantity on order from suppliers.
    """
    on_order: Int

    """
    Current selling price.
    """
    price: Float

    """
    Hash key of the parent product.
    """
    product_h_key: String

    """
    Product identifier from source system.
    """
    product_id: String

    """
    Whether the product can be replenished.
    """
    replenishable: Boolean

    """
    Timestamp when created in source system.
    """
    source_created_at: String

    """
    Product status.
    """
    status: String

    """
    Current stock quantity.
    """
    stock: Int

    """
    Tags associated with this product.
    """
    tags: [ProductTag]

    """
    Product type classification.
    """
    type: String

    """
    Vendors/suppliers for this product.
    """
    vendors: [ProductVendor]

    """
    Available-to-sell quantity.
    """
    ats: Float

    """
    Total discount amount applied.
    """
    discount: Float

    """
    Total quantity sold (gross).
    """
    gross_qty: Int

    """
    Total gross sales revenue.
    """
    gross_sales: Float

    """
    Net quantity after returns.
    """
    net_qty: Int

    """
    Net sales after returns.
    """
    net_sales: Float

    """
    Total quantity returned.
    """
    return_qty: Int

    """
    Return rate percentage.
    """
    return_rate: Float

    """
    Total value of returns.
    """
    return_value: Float

    """
    Average sales per day.
    """
    sales_per_day: Float

    """
    Total shipping charges.
    """
    shipping: Float

    """
    Total tax amount.
    """
    tax: Float

    """
    Total sales including all charges.
    """
    total_sales: Float

    """
    Estimated days of sales remaining.
    """
    sales_days_left: Float

    """
    Sell-through rate.
    """
    sell_through: Float

    """
    Gross profit.
    """
    gross_profit: Float

    """
    Gross margin percentage.
    """
    gross_margin: Float

    """
    Cost per item.
    """
    cost_per_item: Float

    """
    Total inventory value based on current stock.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value (ATS-based).
    """
    total_inventory_value_ats: Float

    """
    Weeks of supply remaining.
    """
    wos: Float

    """
    Recommended reorder quantity.
    """
    re_order_qty: Int

    """
    Percentage of total stock.
    """
    stock_percentage: Float

    """
    Reorder point.
    """
    rop: Float

    """
    Lead time in days.
    """
    lead_time: Int

    """
    Days of stock remaining.
    """
    days_of_stock: Int

    """
    Percentage of total gross sales.
    """
    gross_sales_percentage: Float

    """
    Total advertising spend.
    """
    ad_spends: Float

    """
    Number of ad clicks.
    """
    clicks: Int

    """
    Number of page views.
    """
    views: Int

    """
    Conversion rate.
    """
    conversion_rate: Float

    """
    Sales trend indicator.
    """
    trend: String

    """
    Sales pattern classification.
    """
    sale_pattern: String

    """
    Risk level of the forecast.
    """
    forecast_confidence: String

    """
    Forecasting confidence score.
    """
    confidence_score: String

    """
    Forecasted sales value.
    """
    forecast_value: Int

    """
    Forecasted sales per day.
    """
    forecast_sales_per_day: Int

    """
    Forecasted gross quantity.
    """
    forecasted_gross_qty: Int

    """
    Forecasted gross sales.
    """
    forecasted_gross_sales: Float

    """
    Forecasted sales days remaining (stock-based).
    """
    forecasted_sales_days_remaining_based_on_stock: Int

    """
    Forecasted sales days remaining (ATS-based).
    """
    forecasted_sales_days_remaining_based_on_ats: Int

    """
    Product grade classification.
    """
    product_grade: RevenueGrade

    """
    Position within collection.
    """
    collection_position: Int

    """
    Gross sales rank.
    """
    gross_sales_rank: Int
}

"""
Comprehensive filter input for sales-by-product queries.
Allows filtering on product attributes, sales metrics, inventory data, and forecasting fields.
All filters are optional and can be combined to create complex query conditions.
"""
input SalesByProductFilter @doc(category: "Analytics") {
    """
    Filter by product hash key identifier.
    """
    h_key: StringFilterInput

    """
    Filter by business key identifier.
    """
    b_key: StringFilterInput

    """
    Filter by variant SKU.
    """
    variant_sku: StringFilterInput

    """
    Filter by original price before discounts.
    """
    compare_at_price: NumericFilterInput

    """
    Filter by conversion rate.
    """
    conversion_rate: NumericFilterInput

    """
    Filter by cost per item.
    """
    cost_per_item: NumericFilterInput

    """
    Filter by channel ID.
    """
    channel_id: StringFilterInput

    """
    Filter by number of days the product has been available.
    """
    days_on_site: NumericFilterInput

    """
    Filter by DDA (Dynamic Data Attribute) code.
    """
    dda_code: StringFilterInput

    """
    Filter by DDA (Dynamic Data Attribute) value.
    """
    dda_value: StringFilterInput

    """
    Filter by source system identifier.
    """
    from_source: StringFilterInput

    """
    Filter by product image URL.
    """
    image: StringFilterInput

    """
    Filter by product name.
    """
    name: StringFilterInput

    """
    Filter by quantity on order from suppliers.
    """
    on_order: NumericFilterInput

    """
    Filter by current selling price.
    """
    price: NumericFilterInput

    """
    Filter by parent product hash key.
    """
    product_h_key: StringFilterInput

    """
    Filter by product identifier from source system.
    """
    product_id: StringFilterInput

    """
    Filter by whether the product can be replenished.
    """
    replenishable: BooleanFilterInput

    """
    Filter by timestamp when created in source system.
    """
    source_created_at: StringFilterInput

    """
    Filter by product status.
    """
    status: StringFilterInput

    """
    Filter by current stock quantity.
    """
    stock: NumericFilterInput

    """
    Filter by total inventory value based on stock.
    """
    total_inventory_value_stock: NumericFilterInput

    """
    Filter by product type classification.
    """
    type: StringFilterInput

    """
    Filter by available-to-sell quantity.
    """
    ats: NumericFilterInput

    """
    Filter by total discount amount applied.
    """
    discount: NumericFilterInput

    """
    Filter by total quantity sold (gross).
    """
    gross_qty: NumericFilterInput

    """
    Filter by total gross sales revenue.
    """
    gross_sales: NumericFilterInput

    """
    Filter by gross margin percentage.
    """
    gross_margin: NumericFilterInput

    """
    Filter by gross profit.
    """
    gross_profit: NumericFilterInput

    """
    Filter by net quantity after returns.
    """
    net_qty: NumericFilterInput

    """
    Filter by net sales after returns.
    """
    net_sales: NumericFilterInput

    """
    Filter by total quantity returned.
    """
    return_qty: NumericFilterInput

    """
    Filter by return rate percentage.
    """
    return_rate: NumericFilterInput

    """
    Filter by total value of returns.
    """
    return_value: NumericFilterInput

    """
    Filter by average sales per day.
    """
    sales_per_day: NumericFilterInput

    """
    Filter by estimated days of sales remaining.
    """
    sales_days_left: NumericFilterInput

    """
    Filter by total shipping charges.
    """
    shipping: NumericFilterInput

    """
    Filter by total tax amount.
    """
    tax: NumericFilterInput

    """
    Filter by total sales including all charges.
    """
    total_sales: NumericFilterInput

    """
    Filter by total advertising spend.
    """
    ad_spends: NumericFilterInput

    """
    Filter by number of ad clicks.
    """
    clicks: NumericFilterInput

    """
    Filter by number of page views.
    """
    views: NumericFilterInput

    """
    Filter by category hash key identifier.
    """
    categories_h_key: StringFilterInput

    """
    Filter by collection hash key identifier.
    """
    collections_h_key: StringFilterInput

    """
    Filter by category name.
    """
    categories_name: StringFilterInput

    """
    Filter by collection name.
    """
    collections_name: StringFilterInput

    """
    Filter by tag hash key identifier.
    """
    tags_h_key: StringFilterInput

    """
    Filter by tag name.
    """
    tags_name: StringFilterInput

    """
    Filter by sales trend indicator.
    """
    trend: StringFilterInput

    """
    Filter by sales pattern classification.
    """
    sale_pattern: StringFilterInput

    """
    Filter by forecast confidence.
    """
    forecast_confidence: StringFilterInput

    """
    Filter by forecasted sales value.
    """
    forecast_value: NumericFilterInput

    """
    Filter by forecasted sales per day.
    """
    forecast_sales_per_day: NumericFilterInput

    """
    Filter by forecasted gross quantity.
    """
    forecasted_gross_qty: NumericFilterInput

    """
    Filter by forecasted gross sales.
    """
    forecasted_gross_sales: NumericFilterInput

    """
    Filter by forecasted sales days remaining (stock-based).
    """
    forecasted_sales_days_remaining_based_on_stock: NumericFilterInput

    """
    Filter by forecasted sales days remaining (ATS-based).
    """
    forecasted_sales_days_remaining_based_on_ats: NumericFilterInput

    """
    Filter by percentage of total stock.
    """
    stock_percentage: NumericFilterInput

    """
    Filter by percentage of total gross sales.
    """
    gross_sales_percentage: NumericFilterInput

    """
    Filter by product grade classification.
    """
    product_grade: EnumFilterInput

    """
    Filter by gross sales rank.
    """
    gross_sales_rank: NumericFilterInput
}

"""
Input parameters for querying sales-by-product analytics.
"""
input SalesByProductQueryInput @doc(category: "Analytics") {
    """
    Number of records to skip for pagination.
    """
    offset: Int

    """
    Maximum number of records to return.
    """
    limit: Int

    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Sort criteria.
    """
    sort: [SortInput]

    """
    Text search across product names, SKUs, etc.
    """
    search: String

    """
    Filter criteria.
    """
    filter: SalesByProductFilter
}

extend type Query {
    """
    Query comprehensive sales analytics data for products.

    Example:
    ```graphql
    query {
      sales_by_product(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        sort: [{ field: "gross_sales", order: "desc" }]
      }) {
        h_key
        name
        gross_sales
        gross_qty
        stock
        ats
      }
    }
    ```
    """
    sales_by_product(query: SalesByProductQueryInput!): [SalesByProductItem] @doc(category: "Analytics")
}
