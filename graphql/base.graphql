"""
Directive for documentation categorization.
Used to organize types and fields into logical groups in the generated documentation.
"""
directive @doc(
    category: String!
) on SCALAR | FIELD_DEFINITION | ENUM | ENUM_VALUE | INPUT_OBJECT | INTERFACE | UNION | OBJECT

"""
Custom JSON scalar type for handling arbitrary JSON data structures.
Used for complex nested data that doesn't fit standard GraphQL types.
"""
scalar JSON @doc(category: "Base")

"""
Custom directive for validating enum values on input fields.
Restricts input values to a predefined set of allowed strings.
"""
directive @enum(values: [String!]!) on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

"""
Input type for specifying sort criteria in queries.
Used across all paginated queries to control result ordering.
"""
input SortInput @doc(category: "Base") {
    """
    The field name to sort by. Must match a field in the target type.
    """
    field: String!

    """
    Sort direction: 'asc' for ascending, 'desc' for descending.
    """
    order: String! @enum(values: ["asc", "desc"])
}

"""
Comprehensive string filtering input with multiple comparison operators.
Supports exact matching, pattern matching, and case-insensitive operations.
"""
input StringFilterInput @doc(category: "Base") {
    """
    Full-text search match (implementation-specific).
    """
    match: String

    """
    Exact equality match.
    """
    eq: String

    """
    Not equal to (negation of eq).
    """
    neq: String

    """
    Value must be in the provided list.
    """
    in: [String]

    """
    Value must not be in the provided list.
    """
    nin: [String]

    """
    Case-sensitive substring match.
    """
    contains: String

    """
    Case-sensitive prefix match.
    """
    startswith: String

    """
    Case-sensitive suffix match.
    """
    endswith: String

    """
    Case-insensitive substring match.
    """
    icontains: String

    """
    Case-insensitive prefix match.
    """
    istartswith: String

    """
    Case-insensitive suffix match.
    """
    iendswith: String

    """
    Check if field exists (true) or is null/undefined (false).
    """
    exists: Boolean
}

"""
Numeric filtering input supporting range queries and list operations.
Works with Float, Int, and other numeric types.
"""
input NumericFilterInput @doc(category: "Base") {
    """
    Exact equality match.
    """
    eq: Float

    """
    Not equal to (negation of eq).
    """
    neq: Float

    """
    Greater than.
    """
    gt: Float

    """
    Greater than or equal to.
    """
    gte: Float

    """
    Less than.
    """
    lt: Float

    """
    Less than or equal to.
    """
    lte: Float

    """
    Value must be in the provided list.
    """
    in: [Float]

    """
    Value must not be in the provided list.
    """
    nin: [Float]

    """
    Check if field exists (true) or is null/undefined (false).
    """
    exists: Boolean
}

"""
Boolean filtering input for true/false fields.
Supports equality checks and existence validation.
"""
input BooleanFilterInput @doc(category: "Base") {
    """
    Exact equality match.
    """
    eq: Boolean

    """
    Not equal to (negation of eq).
    """
    neq: Boolean

    """
    Check if field exists (true) or is null/undefined (false).
    """
    exists: Boolean
}

"""
Comprehensive enum filtering input with multiple comparison operators.
"""
input EnumFilterInput @doc(category: "Base") {
    """
    Exact equality match.
    """
    eq: String

    """
    Not equal to (negation of eq).
    """
    neq: String

    """
    Value must be in the provided list.
    """
    in: [String]

    """
    Value must not be in the provided list.
    """
    nin: [String]
}

"""
Revenue grade classification for products based on sales performance.
Used for ABC analysis and inventory prioritization.
"""
enum RevenueGrade @doc(category: "Analytics") {
    """
    High-performing products (top 80% of revenue).
    """
    A

    """
    Medium-performing products (next 15% of revenue).
    """
    B

    """
    Low-performing products (bottom 5% of revenue).
    """
    C
}

input SemanticSearchInput @doc(category: "Base") {
    """
    Text to search for in the variant name.
    """
    text: String!

    """
    Minimum score for the search.
    """
    min_score: Float
}
