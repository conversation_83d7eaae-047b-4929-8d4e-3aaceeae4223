"""
Product collection entity representing curated groups of products.
Collections are typically used for marketing campaigns, seasonal promotions,
or thematic product groupings that cross category boundaries.
"""
type Collection @doc(category: "Catalog") {
    """
    Unique hash key identifier for the collection.
    """
    h_key: String!

    """
    Business key identifier, typically from the source system.
    """
    b_key: String!

    """
    Source system identifier (e.g., 'shopify', 'magento', 'custom').
    """
    from_source: String!

    """
    Human-readable collection name displayed in the UI.
    """
    name: String!
}

"""
Filter input for querying collections with various criteria.
All filters support the full range of string comparison operations.
"""
input CollectionsFilter @doc(category: "Catalog") {
    """
    Filter by collection hash key.
    """
    h_key: StringFilterInput

    """
    Filter by business key.
    """
    b_key: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by collection name (supports text search).
    """
    name: StringFilterInput
}

"""
Input parameters for querying collections with pagination, sorting, and filtering.
"""
input CollectionsQueryInput @doc(category: "Catalog") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: CollectionsFilter

    """
    Semantic search input.
    """
    semantic_search: SemanticSearchInput
}

"""
Response type for collection queries containing results and metadata.
"""
type CollectionsQueryOutput @doc(category: "Catalog") {
    """
    Total number of collections matching the filter criteria.
    """
    total_count: Int

    """
    Array of collection records for the current page.
    """
    items: [Collection]
}

extend type Query {
    """
    Query collections with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      collections(query: {
        limit: 20,
        sort: [{ field: "name", order: "asc" }],
        filter: { name: { icontains: "summer" } },
        semantic_search: {
          text: "summer",
          min_score: 0.5
        }
      }) {
        total_count
        items {
          h_key
          name
          from_source
        }
      }
    }
    ```
    """
    collections(query: CollectionsQueryInput!): CollectionsQueryOutput! @doc(category: "Catalog")
}
