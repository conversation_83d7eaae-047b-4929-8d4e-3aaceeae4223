"""
Product category entity representing hierarchical product classification.
Categories are used to organize products into logical groups for navigation and filtering.
"""
type Category @doc(category: "Catalog") {
    """
    Unique hash key identifier for the category.
    """
    h_key: String!

    """
    Business key identifier, typically from the source system.
    """
    b_key: String!

    """
    Source system identifier (e.g., 'shopify', 'magento', 'custom').
    """
    from_source: String!

    """
    Human-readable category name displayed in the UI.
    """
    name: String!
}

"""
Filter input for querying categories with various criteria.
All filters support the full range of string comparison operations.
"""
input CategoriesFilter @doc(category: "Catalog") {
    """
    Filter by category hash key.
    """
    h_key: StringFilterInput

    """
    Filter by business key.
    """
    b_key: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by category name (supports text search).
    """
    name: StringFilterInput
}

"""
Input parameters for querying categories with pagination, sorting, and filtering.
"""
input CategoriesQueryInput @doc(category: "Catalog") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: CategoriesFilter

    """
    Semantic search input.
    """
    semantic_search: SemanticSearchInput
}

"""
Response type for category queries containing results and metadata.
"""
type CategoriesQueryOutput @doc(category: "Catalog") {
    """
    Total number of categories matching the filter criteria.
    """
    total_count: Int

    """
    Array of category records for the current page.
    """
    items: [Category]
}

extend type Query {
    """
    Query categories with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      categories(query: {
        limit: 20,
        sort: [{ field: "name", order: "asc" }],
        filter: { name: { icontains: "electronics" } },
        semantic_search: {
          text: "electronics",
          min_score: 0.5
        }
      }) {
        total_count
        items {
          h_key
          name
          from_source
        }
      }
    }
    ```
    """
    categories(query: CategoriesQueryInput!): CategoriesQueryOutput! @doc(category: "Catalog")
}
