"""
Category information associated with product attributes.
Used to group and classify attribute-based analytics.
"""
type AttributeCategory @doc(category: "Catalog") {
    """
    Category name.
    """
    name: String!

    """
    Unique hash key identifier for the category.
    """
    h_key: String!
}

"""
Product attribute analytics entity containing sales, inventory, and performance metrics.
Provides detailed analytics for specific product attributes (like color, size, etc.)
with comprehensive business intelligence data.
"""
type Attribute @doc(category: "Catalog") {
    """
    The specific attribute value being analyzed (e.g., 'Red', 'Large', 'Cotton').
    """
    attribute_value: String

    """
    Product identifier from the source system.
    """
    product_id: String

    """
    Categories associated with this attribute.
    """
    categories: [AttributeCategory]

    """
    Total gross sales revenue before returns and discounts.
    """
    gross_sales: Float

    """
    Total quantity sold (gross, before returns).
    """
    gross_qty: Int

    """
    Total value of returned items.
    """
    return_value: Float

    """
    Total quantity of returned items.
    """
    return_qty: Int

    """
    Return rate as a percentage (return_qty / gross_qty * 100).
    """
    return_rate: Float

    """
    Net sales after returns (gross_sales - return_value).
    """
    net_sales: Float

    """
    Net quantity after returns (gross_qty - return_qty).
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc.
    """
    total_sales: Float

    """
    Average sales per day over the analysis period.
    """
    sales_per_day: Float

    """
    Estimated days of sales remaining based on available-to-sell inventory.
    """
    sales_day_left: Float

    """
    Sell-through rate as a percentage.
    """
    sell_through: Float

    """
    Total tax amount collected.
    """
    tax: Float

    """
    Total discount amount applied.
    """
    discount: Float

    """
    Total shipping charges.
    """
    shipping: Float

    """
    Number of product page views.
    """
    views: Int

    """
    Current stock quantity on hand.
    """
    stock: Int

    """
    Quantity on order from suppliers.
    """
    on_order: Int

    """
    Available-to-sell quantity (stock - committed).
    """
    ats: Int

    """
    Total inventory value at cost.
    """
    total_inventory_value_ats: Float

    """
    Current selling price.
    """
    price: Float

    """
    Product name.
    """
    name: String

    """
    Product image URL.
    """
    image: String

    """
    Reorder point - minimum stock level before reordering.
    """
    rop: Float

    """
    Conversion rate (purchases / views).
    """
    conversion_rate: Float

    """
    Percentage of total gross sales this attribute represents.
    """
    gross_sales_percentage: Float

    """
    Forecasted gross quantity.
    """
    forecasted_gross_qty: Float

    """
    Recommended reorder quantity.
    """
    re_order_qty: Int

    """
    Gross profit (net_sales - cost).
    """
    gross_profit: Float

    """
    Gross margin percentage.
    """
    gross_margin: Float

    """
    Weeks of supply remaining.
    """
    wos: Float

    """
    Percentage of total stock this attribute represents.
    """
    stock_percentage: Float

    """
    Forecasted sales per day.
    """
    forecast_sales_per_day: Float
}

"""
Input parameters for querying product attribute analytics.
Requires a specific product and attribute type to analyze.
"""
input AttributeQueryInput @doc(category: "Catalog") {
    """
    Hash key of the product to analyze.
    """
    product_h_key: String!

    """
    Attribute type to analyze (e.g., 'color', 'size', 'material').
    """
    attribute: String!

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String

    """
    Start date for forecast analysis (ISO 8601 format).
    """
    forecast_from_date: String

    """
    End date for forecast analysis (ISO 8601 format).
    """
    forecast_to_date: String

    """
    Sort criteria for the results.
    """
    sort: [SortInput]
}

extend type Query {
    """
    Query product attribute analytics for a specific product and attribute type.
    Returns detailed sales, inventory, and performance metrics for each attribute value.

    Example:
    ```graphql
    query {
      attributes(query: {
        product_h_key: "prod_123",
        attribute: "color",
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        sort: [{ field: "gross_sales", order: "desc" }]
      }) {
        attribute_value
        gross_sales
        gross_qty
        return_rate
        stock
        ats
        conversion_rate
      }
    }
    ```
    """
    attributes(query: AttributeQueryInput!): [Attribute] @doc(category: "Catalog")
}
