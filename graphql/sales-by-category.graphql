type SalesOverTimeItem @doc(category: "Analytics") {
    """
    Start date of the time period.
    """
    period_start: String!

    """
    Total gross sales revenue for this time period.
    """
    gross_sales: Float!

    """
    Total gross quantity sold for this time period.
    """
    gross_qty: Int!
}

type ForecastOverTimeItem @doc(category: "Analytics") {
    """
    Start date of the time period.
    """
    period_start: String!

    """
    Total forecasted gross sales revenue for this time period.
    """
    forecasted_gross_qty: Float!
}

"""
Sales analytics data aggregated by product category.
Provides category-level insights for sales performance and inventory management.
"""
type SalesByCategoryItem @doc(category: "Analytics") {
    """
    Unique hash key identifier for the category.
    """
    h_key: String!

    """
    Category name.
    """
    name: String

    """
    Total gross sales revenue for this category.
    """
    gross_sales: Float

    """
    Total gross quantity sold for this category.
    """
    gross_qty: Int

    """
    Total net sales after returns for this category.
    """
    net_sales: Float

    """
    Total net quantity after returns for this category.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. for this category.
    """
    total_sales: Float

    """
    Total return value for this category.
    """
    return_value: Float

    """
    Total return quantity for this category.
    """
    return_qty: Int

    """
    Percentage of total stock this category represents.
    """
    stock_percentage: Float

    """
    Sell-through rate for this category.
    """
    sell_through: Float

    """
    Percentage of total gross sales this category represents.
    """
    gross_sales_percentage: Float

    """
    Total advertising spend for this category.
    """
    ad_spends: Float

    """
    Total ad clicks for this category.
    """
    clicks: Int

    """
    Total page views for this category.
    """
    views: Int

    """
    Total stock quantity for this category.
    """
    stock: Int

    """
    Total on-order quantity for this category.
    """
    on_order: Int

    """
    Total available-to-sell quantity for this category.
    """
    ats: Int

    """
    Total inventory value based on stock for this category.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value based on ATS for this category.
    """
    total_inventory_value_ats: Float

    """
    Sales over time for this category.
    """
    sales_over_time: [SalesOverTimeItem]

    """
    Forecast over time for this category.
    """
    forecast_over_time: [ForecastOverTimeItem]
}

"""
Filter input for sales-by-category queries.
Allows filtering category analytics data by various criteria including sales metrics,
inventory levels, and performance indicators.
"""
input SalesByCategoryFilter @doc(category: "Analytics") {
    """
    Filter by category hash key identifier.
    """
    h_key: StringFilterInput

    """
    Filter by category name.
    """
    name: StringFilterInput

    """
    Filter by total gross sales revenue range.
    """
    gross_sales: NumericFilterInput

    """
    Filter by total gross quantity sold range.
    """
    gross_qty: NumericFilterInput

    """
    Filter by total net sales after returns range.
    """
    net_sales: NumericFilterInput

    """
    Filter by total net quantity after returns range.
    """
    net_qty: NumericFilterInput

    """
    Filter by total sales including taxes and shipping range.
    """
    total_sales: NumericFilterInput

    """
    Filter by total return value range.
    """
    return_value: NumericFilterInput

    """
    Filter by total return quantity range.
    """
    return_qty: NumericFilterInput

    """
    Filter by total ad clicks range.
    """
    clicks: NumericFilterInput

    """
    Filter by total page views range.
    """
    views: NumericFilterInput

    """
    Filter by total stock quantity range.
    """
    stock: NumericFilterInput

    """
    Filter by total on-order quantity range.
    """
    on_order: NumericFilterInput

    """
    Filter by total available-to-sell quantity range.
    """
    ats: NumericFilterInput

    """
    Filter by total inventory value based on stock range.
    """
    total_inventory_value_stock: NumericFilterInput

    """
    Filter by total inventory value based on ATS range.
    """
    total_inventory_value_ats: NumericFilterInput

    """
    Filter by stock percentage of total inventory range.
    """
    stock_percentage: NumericFilterInput

    """
    Filter by gross sales percentage of total sales range.
    """
    gross_sales_percentage: NumericFilterInput
}

"""
Input parameters for querying sales-by-category analytics.
"""
input SalesByCategoryQueryInput @doc(category: "Analytics") {
    """
    Number of records to skip for pagination.
    """
    offset: Int

    """
    Maximum number of records to return.
    """
    limit: Int

    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Sort criteria.
    """
    sort: [SortInput]

    """
    Text search across category names.
    """
    search: String

    """
    Filter criteria.
    """
    filter: SalesByCategoryFilter

    """
    Time frame to filter results by.
    """
    time_frame: TimeFrame
}

extend type Query {
    """
    Query sales analytics data aggregated by product category.

    Example:
    ```graphql
    query {
      sales_by_category(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        sort: [{ field: "gross_sales", order: "desc" }]
      }) {
        h_key
        name
        gross_sales
        gross_qty
        stock
        sell_through
        sales_over_time {
          period_start
          gross_sales
          gross_qty
        }
        forecast_over_time {
          period_start
          forecasted_gross_qty
        }
      }
    }
    ```
    """
    sales_by_category(query: SalesByCategoryQueryInput!): [SalesByCategoryItem] @doc(category: "Analytics")
}
