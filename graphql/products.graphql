"""
Product entity representing a master product record.
Products can have multiple variants and are organized into categories and collections.
Contains core product information, pricing, and metadata.
"""
type Product @doc(category: "Catalog") {
    """
    Unique hash key identifier for the product.
    """
    h_key: String!

    """
    Source system identifier (e.g., 'shopify', 'magento', 'custom').
    """
    from_source: String

    """
    Hash key of the parent product (for product hierarchies).
    """
    parent_h_key: String

    """
    Business key identifier, typically from the source system.
    """
    b_key: String

    """
    Variant identifier from the source system.
    """
    variant_id: String

    """
    Product identifier from the source system.
    """
    product_id: String

    """
    Product type classification (e.g., 'simple', 'configurable', 'bundle').
    """
    type: String

    """
    Product name displayed to customers.
    """
    name: String

    """
    Stock Keeping Unit - unique identifier for inventory tracking.
    """
    sku: String

    """
    Product barcode for scanning and identification.
    """
    barcode: String

    """
    Cost per item for margin calculations (in base currency).
    """
    cost_per_item: Float

    """
    Current selling price (in base currency).
    """
    price: Float

    """
    Original price before discounts (in base currency).
    """
    compare_at_price: Float

    """
    URL or path to the primary product image.
    """
    image: String

    """
    Product color attribute.
    """
    color: String

    """
    Default color for the product line.
    """
    default_color: String

    """
    Product size attribute.
    """
    size: String

    """
    Product status (e.g., 'active', 'draft', 'archived').
    """
    status: String

    """
    Timestamp when the product was created in the source system.
    """
    source_created_at: String

    """
    Timestamp when the product was published/made available.
    """
    published_at: String

    """
    Categories this product belongs to.
    """
    categories: [VariantCategory]

    """
    Collections this product is part of.
    """
    collections: [VariantCollection]

    """
    Tags associated with this product.
    """
    tags: [VariantTag]

    """
    Vendors/suppliers for this product.
    """
    vendors: [VariantVendor]
}

"""
Filter input for querying products with various criteria.
Supports filtering by product attributes, pricing, and associated entities.
"""
input ProductsFilter @doc(category: "Catalog") {
    """
    Filter by product hash key.
    """
    h_key: StringFilterInput

    """
    Filter by business key.
    """
    b_key: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by product ID from source system.
    """
    product_id: StringFilterInput

    """
    Filter by product name (supports text search).
    """
    name: StringFilterInput

    """
    Filter by product status.
    """
    status: StringFilterInput

    """
    Filter by current selling price.
    """
    price: NumericFilterInput

    """
    Filter by compare-at price.
    """
    compare_at_price: NumericFilterInput

    """
    Filter by associated category hash keys.
    """
    categories_h_key: StringFilterInput

    """
    Filter by associated collection hash keys.
    """
    collections_h_key: StringFilterInput
}

"""
Input parameters for querying products with pagination, sorting, and filtering.
"""
input ProductsQueryInput @doc(category: "Catalog") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: ProductsFilter

    """
    Semantic search input.
    """
    semantic_search: SemanticSearchInput
}

"""
Response type for product queries containing results and metadata.
"""
type ProductsQueryOutput @doc(category: "Catalog") {
    """
    Total number of products matching the filter criteria.
    """
    total_count: Int

    """
    Array of product records for the current page.
    """
    items: [Product]
}

extend type Query {
    """
    Query products with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      products(query: {
        limit: 20,
        sort: [{ field: "name", order: "asc" }],
        filter: {
          status: { eq: "active" },
          price: { gte: 10.0, lte: 100.0 }
        },
        semantic_search: {
          text: "electronics",
          min_score: 0.5
        }
      }) {
        total_count
        items {
          h_key
          name
          price
          status
          categories { name }
        }
      }
    }
    ```
    """
    products(query: ProductsQueryInput!): ProductsQueryOutput! @doc(category: "Catalog")
}
