"""
Sales over time chart represents a graphical representation of sales data over time.
It is typically used to visualize sales trends and patterns over a specific period.
"""
type SalesOverTimeChart @doc(category: "Catalog") {
    """
    Start date for the analysis period.
    """
    period_start: String!

    """
    Actual gross quantity.
    """
    gross_qty: Int

    """
    Actual gross sales.
    """
    gross_sales: Float

    """
    Actual net sales.
    """
    net_sales: Float

    """
    Actual net quantity.
    """
    net_qty: Int

    """
    Actual return quantity.
    """
    return_qty: Int

    """
    Average selling price.
    """
    avg_selling_price: Float
}

type SalesOverTimeSummary @doc(category: "Catalog") {
    """
    Average selling price.
    """
    avg_selling_price: Float

    """
    Total gross quantity.
    """
    total_gross_qty: Int

    """
    Total gross sales.
    """
    total_gross_sales: Float

    """
    Total net sales.
    """
    total_net_sales: Float

    """
    Total net quantity.
    """
    total_net_qty: Int

    """
    Total return quantity.
    """
    total_return_qty: Int
}

enum TimeFrame {
    day
    week
    month
    quarter
    year
}

"""
Filter input for querying sales bar chart with various criteria.
"""
input SalesOverTimeChartFilter @doc(category: "Catalog") {
    dummy: String
}

"""
Input parameters for querying sales bar chart with pagination, sorting, and filtering.
"""
input SalesOverTimeChartQueryInput @doc(category: "Catalog") {
    """
    Time frame for the analysis period.
    """
    time_frame: TimeFrame!

    """
    Source for the analysis period.
    """
    from_source: String

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String!

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String!
}

"""
Response type for sales bar chart queries containing results and metadata.
"""
type SalesOverTimeChartQueryOutput @doc(category: "Catalog") {
    """
    Sales bar chart data.
    """
    items: [SalesOverTimeChart]

    """
    Sales bar chart summary.
    """
    summary: SalesOverTimeSummary
}

extend type Query {
    """
    Query sales over time chart with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      sales_over_time_chart(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31"
      }) {
        data {
          period_start
          gross_qty
          gross_sales
          net_sales
          net_qty
        }
      }
    }
    ```
    """
    sales_over_time_chart(query: SalesOverTimeChartQueryInput!): SalesOverTimeChartQueryOutput!
        @doc(category: "Catalog")
}
