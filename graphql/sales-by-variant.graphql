"""
Collection information associated with variants in sales analytics.
Includes positioning data for merchandising insights.
"""
type VariantCollection @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Collection name.
    """
    name: String!

    """
    Position of the variant within the collection.
    """
    position: Int!

    """
    Unique hash key identifier for the collection.
    """
    h_key: String!
}

"""
Category information associated with variants in sales analytics.
"""
type VariantCategory @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Category name.
    """
    name: String!

    """
    Unique hash key identifier for the category.
    """
    h_key: String!
}

"""
Tag information associated with variants in sales analytics.
"""
type VariantTag @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Tag name.
    """
    name: String!

    """
    Unique hash key identifier for the tag.
    """
    h_key: String!
}

"""
Vendor/supplier information associated with variants in sales analytics.
"""
type VariantVendor @doc(category: "Analytics") {
    """
    Source system identifier.
    """
    from_source: String!

    """
    Vendor name.
    """
    name: String!

    """
    Unique hash key identifier for the vendor.
    """
    h_key: String!
}

"""
Sales over time.
"""
type SalesOverTime @doc(category: "Analytics") {
    """
    Period start.
    """
    period_start: String

    """
    Gross sales.
    """
    gross_sales: Float

    """
    Gross quantity.
    """
    gross_qty: Int
}

"""
Forecast over time.
"""
type ForecastOverTime @doc(category: "Analytics") {
    """
    Period start.
    """
    period_start: String
    """
    Forecasted gross quantity.
    """
    forecasted_gross_qty: Float
}

"""
Days in stock over time.
"""
type DaysInStockOverTime @doc(category: "Analytics") {
    """
    Period start.
    """
    period_start: String

    """
    Days in stock.
    """
    days_in_stock: Int
}

"""
Comprehensive sales analytics data for individual product variants.
Contains detailed sales metrics, inventory data, forecasting, and performance indicators.
This is the primary entity for variant-level business intelligence and reporting.
"""
type SalesByVariantItem @doc(category: "Analytics") {
    """
    Unique hash key identifier for the variant.
    """
    h_key: String!

    """
    Source system identifier (e.g., 'shopify', 'magento', 'big-commerce',...).
    """
    from_source: String

    """
    Hash key of the parent product this variant belongs to.
    """
    parent_h_key: String

    """
    Business key identifier, typically from the source system.
    """
    b_key: String

    """
    Variant identifier from the source system.
    """
    variant_id: String

    """
    Product identifier from the source system.
    """
    product_id: String

    """
    Hash key of the parent product.
    """
    product_h_key: String

    """
    Variant type classification.
    """
    type: String

    """
    Variant name displayed to customers.
    """
    name: String

    """
    Stock Keeping Unit - unique identifier for inventory tracking.
    """
    sku: String

    """
    Variant barcode for scanning and identification.
    """
    barcode: String

    """
    Current selling price (in base currency).
    """
    price: Float

    """
    Original price before discounts (in base currency).
    """
    compare_at_price: Float

    """
    URL or path to the variant-specific image.
    """
    image: String

    """
    Variant color attribute.
    """
    color: String

    """
    Default color for the product line.
    """
    default_color: String

    """
    Variant size attribute.
    """
    size: String

    """
    Variant status (e.g., 'active', 'draft', 'archived').
    """
    status: String

    """
    Whether the variant can be replenished/restocked.
    """
    replenishable: Boolean

    """
    Current stock quantity on hand.
    """
    stock: Int

    """
    Quantity on order from suppliers.
    """
    on_order: Int

    """
    Available-to-sell quantity (stock - committed).
    """
    ats: Int

    """
    Categories this variant belongs to.
    """
    categories: [VariantCategory]

    """
    Collections this variant is part of.
    """
    collections: [VariantCollection]

    """
    Tags associated with this variant.
    """
    tags: [VariantTag]

    """
    Vendors/suppliers for this variant.
    """
    vendors: [VariantVendor]

    """
    Timestamp when the variant was created in the source system.
    """
    source_created_at: String

    """
    Cost per item for margin calculations (in base currency).
    """
    cost_per_item: Float

    """
    Number of days the variant has been available on the site.
    """
    days_on_site: Int

    """
    Total gross sales revenue before returns and discounts.
    """
    gross_sales: Float

    """
    Total quantity sold (gross, before returns).
    """
    gross_qty: Int

    """
    Net sales after returns (gross_sales - return_value).
    """
    net_sales: Float

    """
    Net quantity after returns (gross_qty - return_qty).
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc.
    """
    total_sales: Float

    """
    Total value of returned items.
    """
    return_value: Float

    """
    Total quantity of returned items.
    """
    return_qty: Int

    """
    Total discount amount applied.
    """
    discount: Float

    """
    Total tax amount collected.
    """
    tax: Float

    """
    Total shipping charges.
    """
    shipping: Float

    """
    Average sales per day over the analysis period.
    """
    sales_per_day: Float

    """
    Estimated days of sales remaining based on current sales velocity.
    """
    sales_days_left: Float

    """
    Sell-through rate as a percentage.
    """
    sell_through: Float

    """
    Gross profit (net_sales - cost).
    """
    gross_profit: Float

    """
    Gross margin percentage.
    """
    gross_margin: Float

    """
    Return rate as a percentage (return_qty / gross_qty * 100).
    """
    return_rate: Float

    """
    Total inventory value based on current stock.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value based on available-to-sell quantity.
    """
    total_inventory_value_ats: Float

    """
    Weeks of supply remaining.
    """
    wos: Float

    """
    Recommended reorder quantity.
    """
    re_order_qty: Int

    """
    Percentage of total stock this variant represents.
    """
    stock_percentage: Float

    """
    Reorder point - minimum stock level before reordering.
    """
    rop: Float

    """
    Lead time in days for restocking.
    """
    lead_time: Int

    """
    Days of stock remaining at current sales velocity.
    """
    days_of_stock: Int

    """
    Percentage of total gross sales this variant represents.
    """
    gross_sales_percentage: Float

    """
    Total advertising spend allocated to this variant.
    """
    ad_spends: Float

    """
    Number of ad clicks.
    """
    clicks: Int

    """
    Number of product page views.
    """
    views: Int

    """
    Conversion rate (purchases / views).
    """
    conversion_rate: Float

    """
    Sales trend indicator (e.g., 'increasing', 'decreasing', 'stable').
    """
    trend: String

    """
    Sales pattern classification (e.g., 'seasonal', 'steady', 'volatile').
    """
    sale_pattern: String

    """
    Risk level of the forecast.
    """
    forecast_confidence: String

    """
    Confidence score for forecasting accuracy.
    """
    confidence_score: String

    """
    Forecasted sales value.
    """
    forecast_value: Int

    """
    Forecasted sales per day.
    """
    forecast_sales_per_day: Int

    """
    Forecasted gross quantity to be sold.
    """
    forecasted_gross_qty: Int

    """
    Forecasted gross sales revenue.
    """
    forecasted_gross_sales: Float

    """
    Forecasted days of sales remaining based on current stock.
    """
    forecasted_sales_days_remaining_based_on_stock: Int

    """
    Forecasted days of sales remaining based on available-to-sell inventory.
    """
    forecasted_sales_days_remaining_based_on_ats: Int

    """
    Revenue grade classification (A/B/C analysis).
    """
    product_grade: RevenueGrade

    """
    Position of this variant within its collection.
    """
    collection_position: Int

    """
    Gross sales rank.
    """
    gross_sales_rank: Int

    """
    Sales over time.
    """
    sales_over_time: [SalesOverTime]

    """
    Forecast over time.
    """
    forecast_over_time: [ForecastOverTime]

    """
    Days in stock over time.
    """
    days_in_stock_over_time: [DaysInStockOverTime]

    """
    In transit stock.
    """
    in_transit_stock: Int
}

"""
Comprehensive filter input for sales-by-variant queries.
Supports filtering by variant attributes, sales metrics, inventory data, and forecasting fields.
"""
input SalesByVariantFilter @doc(category: "Analytics") {
    """
    Filter by variant hash key.
    """
    h_key: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by variant ID.
    """
    variant_id: StringFilterInput

    """
    Filter by product ID.
    """
    product_id: StringFilterInput

    """
    Filter by product hash key.
    """
    product_h_key: StringFilterInput

    """
    Filter by variant name.
    """
    name: StringFilterInput

    """
    Filter by SKU.
    """
    sku: StringFilterInput

    """
    Filter by barcode.
    """
    barcode: StringFilterInput

    """
    Filter by color.
    """
    color: StringFilterInput

    """
    Filter by channel ID.
    """
    channel_id: StringFilterInput

    """
    Filter by size.
    """
    size: StringFilterInput

    """
    Filter by status.
    """
    status: StringFilterInput

    """
    Filter by replenishable flag.
    """
    replenishable: BooleanFilterInput

    """
    Filter by stock quantity.
    """
    stock: NumericFilterInput

    """
    Filter by on-order quantity.
    """
    on_order: NumericFilterInput

    """
    Filter by available-to-sell quantity.
    """
    ats: NumericFilterInput

    """
    Filter by days on site.
    """
    days_on_site: NumericFilterInput

    """
    Filter by gross sales.
    """
    gross_sales: NumericFilterInput

    """
    Filter by gross quantity.
    """
    gross_qty: NumericFilterInput

    """
    Filter by gross margin.
    """
    gross_margin: NumericFilterInput

    """
    Filter by gross profit.
    """
    gross_profit: NumericFilterInput

    """
    Filter by net sales.
    """
    net_sales: NumericFilterInput

    """
    Filter by net quantity.
    """
    net_qty: NumericFilterInput

    """
    Filter by total sales.
    """
    total_sales: NumericFilterInput

    """
    Filter by return value.
    """
    return_value: NumericFilterInput

    """
    Filter by return quantity.
    """
    return_qty: NumericFilterInput

    """
    Filter by discount amount.
    """
    discount: NumericFilterInput

    """
    Filter by tax amount.
    """
    tax: NumericFilterInput

    """
    Filter by shipping amount.
    """
    shipping: NumericFilterInput

    """
    Filter by sales per day.
    """
    sales_per_day: NumericFilterInput

    """
    Filter by sales days left.
    """
    sales_days_left: NumericFilterInput

    """
    Filter by return rate.
    """
    return_rate: NumericFilterInput

    """
    Filter by price.
    """
    price: NumericFilterInput

    """
    Filter by compare-at price.
    """
    compare_at_price: NumericFilterInput

    """
    Filter by cost per item.
    """
    cost_per_item: NumericFilterInput

    """
    Filter by category hash keys.
    """
    categories_h_key: StringFilterInput

    """
    Filter by collection hash keys.
    """
    collections_h_key: StringFilterInput

    """
    Filter by category names.
    """
    categories_name: StringFilterInput

    """
    Filter by collection names.
    """
    collections_name: StringFilterInput

    """
    Filter by tag hash keys.
    """
    tags_h_key: StringFilterInput

    """
    Filter by tag names.
    """
    tags_name: StringFilterInput

    """
    Filter by sales trend.
    """
    trend: StringFilterInput

    """
    Filter by sales pattern.
    """
    sale_pattern: StringFilterInput

    """
    Filter by forecast confidence.
    """
    forecast_confidence: StringFilterInput

    """
    Filter by forecast value.
    """
    forecast_value: NumericFilterInput

    """
    Filter by forecast sales per day.
    """
    forecast_sales_per_day: NumericFilterInput

    """
    Filter by forecasted gross quantity.
    """
    forecasted_gross_qty: NumericFilterInput

    """
    Filter by forecasted gross sales.
    """
    forecasted_gross_sales: NumericFilterInput

    """
    Filter by forecasted sales days remaining (stock-based).
    """
    forecasted_sales_days_remaining_based_on_stock: NumericFilterInput

    """
    Filter by forecasted sales days remaining (ATS-based).
    """
    forecasted_sales_days_remaining_based_on_ats: NumericFilterInput

    """
    Filter by stock percentage.
    """
    stock_percentage: NumericFilterInput

    """
    Filter by gross sales percentage.
    """
    gross_sales_percentage: NumericFilterInput

    """
    Filter by product grade (A/B/C).
    """
    product_grade: EnumFilterInput

    """
    Filter by total inventory value stock.
    """
    total_inventory_value_stock: NumericFilterInput

    """
    Filter by total inventory value ats.
    """
    total_inventory_value_ats: NumericFilterInput

    """
    Filter by gross sales rank.
    """
    gross_sales_rank: NumericFilterInput

    """
    Filter by vendor hash key.
    """
    vendors_h_key: StringFilterInput
}

"""
Input parameters for querying sales-by-variant analytics with comprehensive filtering and date range options.
"""
input SalesByVariantQueryInput @doc(category: "Analytics") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String

    """
    Start date for forecast analysis (ISO 8601 format).
    """
    forecast_from_date: String

    """
    End date for forecast analysis (ISO 8601 format).
    """
    forecast_to_date: String

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Text search across variant names, SKUs, and other searchable fields.
    """
    search: String

    """
    Filter criteria to narrow down results.
    """
    filter: SalesByVariantFilter

    """
    Channel ID to filter results by specific sales channel.
    """
    channel_id: String

    """
    Time frame to filter results by.
    """
    time_frame: TimeFrame
}

extend type Query {
    """
    Query comprehensive sales analytics data for product variants.
    Returns detailed sales metrics, inventory data, forecasting, and performance indicators.

    Example:
    ```graphql
    query {
      sales_by_variant(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        limit: 50,
        sort: [{ field: "gross_sales", order: "desc" }],
        filter: {
          status: { eq: "active" },
          gross_sales: { gte: 1000 }
        }
      }) {
        h_key
        name
        sku
        gross_sales
        gross_qty
        stock
        ats
        product_grade
      }
    }
    ```
    """
    sales_by_variant(query: SalesByVariantQueryInput!): [SalesByVariantItem] @doc(category: "Analytics")
}
