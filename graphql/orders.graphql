"""
Order item entity representing individual items within an order.
"""
type OrderItem @doc(category: "Orders") {
    """
    Hash key identifier for the item.
    """
    h_item_key: String

    """
    SKU of the item.
    """
    item_sku: String

    """
    Name of the item.
    """
    item_name: String

    """
    Categories associated with the item.
    """
    item_categories: [OrderItemCategories]

    """
    Price per item.
    """
    item_price: Float

    """
    Cost per item.
    """
    cost_per_item: Float

    """
    Tax amount for this item.
    """
    tax: Float

    """
    Net quantity sold (after returns).
    """
    net_qty: Int

    """
    Discount amount applied to this item.
    """
    discount: Float

    """
    Shipping cost for this item.
    """
    shipping: Float

    """
    Gross quantity sold (before returns).
    """
    gross_qty: Int

    """
    Net sales amount (after returns and discounts).
    """
    net_sales: Float

    """
    Quantity returned.
    """
    return_qty: Int

    """
    Gross sales amount (before returns and discounts).
    """
    gross_sales: Float

    """
    Total sales amount including tax.
    """
    total_sales: Float

    """
    Total value of returns for this item.
    """
    return_value: Float

    """
    Total cost for this item.
    """
    cost: Float

    """
    Gross profit for this item.
    """
    gross_profit: Float
}

"""
Categories associated with an order item.
"""
type OrderItemCategories @doc(category: "Orders") {
    """
    Category name.
    """
    name: String

    """
    Category hash key.
    """
    h_key: String
}

"""
Order entity representing a customer order transaction.
"""
type Order @doc(category: "Orders") {
    """
    Unique order identifier.
    """
    order_id: String

    """
    Source system identifier (e.g., 'shopify', 'magento', 'custom').
    """
    from_source: String

    """
    Channel identifier where the order was placed.
    """
    channel_id: String

    """
    Order date in YYYY-MM-DD format.
    """
    date: String

    """
    Total sales amount including tax and shipping.
    """
    total_sales: Float

    """
    Net sales amount after returns and discounts.
    """
    net_sales: Float

    """
    Gross sales amount before returns and discounts.
    """
    gross_sales: Float

    """
    Total gross quantity of items sold.
    """
    gross_qty: Int

    """
    Total net quantity of items sold (after returns).
    """
    net_qty: Int

    """
    Total quantity of items returned.
    """
    return_qty: Int

    """
    Total value of returned items.
    """
    return_value: Float

    """
    Total discount amount applied to the order.
    """
    discount: Float

    """
    Total tax amount for the order.
    """
    tax: Float

    """
    Total shipping cost for the order.
    """
    shipping: Float

    """
    Number of unique items in the order.
    """
    unique_item_count: Int

    """
    Total gross profit for the order.
    """
    gross_profit: Float

    """
    Individual items within the order.
    """
    order_items: [OrderItem]
}

type OrderAggregation @doc(category: "Orders") {
    """
    Aggregation key.
    """
    key: String

    """
    Total sales amount including tax and shipping.
    """
    total_sales: Float

    """
    Net sales amount after returns and discounts.
    """
    net_sales: Float

    """
    Gross sales amount before returns and discounts.
    """
    gross_sales: Float

    """
    Total gross quantity of items sold.
    """
    gross_qty: Int

    """
    Total net quantity of items sold (after returns).
    """
    net_qty: Int

    """
    Total quantity of items returned.
    """
    return_qty: Int

    """
    Total value of returned items.
    """
    return_value: Float

    """
    Total discount amount applied to the order.
    """
    discount: Float

    """
    Total tax amount for the order.
    """
    tax: Float

    """
    Total gross profit for the order.
    """
    gross_profit: Float
}

"""
Filter input for querying orders with various criteria.
All filters support the full range of string and numeric comparison operations.
"""
input OrdersFilter @doc(category: "Orders") {
    """
    Filter by order ID.
    """
    order_id: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by channel ID.
    """
    channel_id: StringFilterInput

    """
    Filter by total sales amount.
    """
    total_sales: NumericFilterInput

    """
    Filter by net sales amount.
    """
    net_sales: NumericFilterInput

    """
    Filter by gross sales.
    """
    gross_sales: NumericFilterInput

    """
    Filter by gross quantity.
    """
    gross_qty: NumericFilterInput

    """
    Filter by net quantity.
    """
    net_qty: NumericFilterInput

    """
    Filter by return quantity.
    """
    return_qty: NumericFilterInput

    """
    Filter by return value.
    """
    return_value: NumericFilterInput

    """
    Filter by discount amount.
    """
    discount: NumericFilterInput

    """
    Filter by tax amount.
    """
    tax: NumericFilterInput

    """
    Filter by shipping cost.
    """
    shipping: NumericFilterInput

    """
    Filter by unique item count.
    """
    unique_item_count: NumericFilterInput

    """
    Filter by gross profit.
    """
    gross_profit: NumericFilterInput

    """
    Filter by item h_key.
    """
    h_item_key: StringFilterInput

    """
    Filter by variant name.
    """
    variant_name: StringFilterInput

    """
    Filter by variant SKU.
    """
    variant_sku: StringFilterInput

    """
    Filter by variant ID.
    """
    variant_id: StringFilterInput

    """
    Filter by barcode.
    """
    variant_barcode: StringFilterInput

    """
    Filter by category h_keys.
    """
    variant_categories_h_key: StringFilterInput

    """
    Filter by collection hash keys.
    """
    variant_collections_h_key: StringFilterInput

    """
    Filter by category names.
    """
    variant_categories_name: StringFilterInput

    """
    Filter by collection names.
    """
    variant_collections_name: StringFilterInput
}

"""
Input parameters for querying orders with pagination, sorting, and filtering.
"""
input OrdersQueryInput @doc(category: "Orders") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: OrdersFilter

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String

    """
    Group by criteria.
    """
    group_by: String @enum(values: ["day", "week", "month", "quarter", "year"])
}

"""
Response type for order queries containing results and metadata.
"""
type OrdersQueryOutput @doc(category: "Orders") {
    """
    Total number of orders matching the filter criteria.
    """
    total_count: Int

    """
    Total gross sales amount across all matching orders.
    """
    total_gross_sales: Float

    """
    Total gross quantity across all matching orders.
    """
    total_gross_qty: Int

    """
    Total net sales amount across all matching orders.
    """
    total_net_sales: Float

    """
    Total net quantity across all matching orders.
    """
    total_net_qty: Int

    """
    Total sales amount (including tax and shipping) across all matching orders.
    """
    total_total_sales: Float

    """
    Total tax amount across all matching orders.
    """
    total_tax: Float

    """
    Total discount amount across all matching orders.
    """
    total_discount: Float

    """
    Total return quantity across all matching orders.
    """
    total_return_qty: Int

    """
    Total return value across all matching orders.
    """
    total_return_value: Float

    """
    Total gross profit across all matching orders.
    """
    total_gross_profit: Float

    """
    Total number of orders matching the filter criteria.
    """
    total_order_count: Int

    """
    Total number of return orders matching the filter criteria.
    """
    total_return_order_count: Int

    """
    Array of order records for the current page.
    """
    items: [Order]

    """
    Aggregations for the query.
    """
    aggs: [OrderAggregation]
}

extend type Query {
    """
    Query orders with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      orders(query: {
        limit: 20,
        sort: [{ field: "date", order: "desc" }],
        filter: {
          from_source: { eq: "shopify" },
        },
        semantic_search: {
          text: "large orders",
          min_score: 0.5
        }
      }) {
        total_count
        items {
          order_id
          from_source
          channel_id
          date
          total_sales
          gross_sales
          net_sales
          order_items {
            h_item_key
            gross_sales
            net_sales
          }
        }
      }
    }
    ```
    """
    orders(query: OrdersQueryInput!): OrdersQueryOutput! @doc(category: "Orders")
}
