"""
Purchase order entity representing a purchase order transaction.
"""
type PurchaseOrder @doc(category: "Purchase Orders") {
    """
    Unique purchase order identifier.
    """
    id: String

    """
    Additional notes or comments for the purchase order.
    """
    note: String

    """
    Source system of the purchase order (e.g., 'conative', 'external').
    """
    source: String

    """
    Purchase order number (human-readable identifier).
    """
    po_number: String

    """
    Purchase order status (e.g., 'pending', 'confirmed', 'received', 'cancelled').
    """
    status: String

    """
    Whether the purchase order is archived.
    """
    archived: Boolean

    """
    Expected delivery date in ISO 8601 format.
    """
    expected_date: String

    """
    Transaction type (e.g., 'import', 'export', 'transfer').
    """
    transaction_type: String

    """
    Ship date in ISO 8601 format.
    """
    ship_date: String

    """
    Creation date in ISO 8601 format.
    """
    created_at: String

    """
    Last update date in ISO 8601 format.
    """
    updated_at: String

    """
    Total quantity ordered across all items.
    """
    total_order_quantity: Int

    """
    Total quantity received across all items.
    """
    total_received_quantity: Int
}

"""
Filter input for querying purchase orders with various criteria.
All filters support the full range of string and numeric comparison operations.
"""
input PurchaseOrdersFilter @doc(category: "Purchase Orders") {
    """
    Filter by purchase order ID.
    """
    id: StringFilterInput

    """
    Filter by note content.
    """
    note: StringFilterInput

    """
    Filter by source system.
    """
    source: StringFilterInput

    """
    Filter by purchase order number.
    """
    po_number: StringFilterInput

    """
    Filter by purchase order status.
    """
    status: StringFilterInput

    """
    Filter by archived status.
    """
    archived: BooleanFilterInput

    """
    Filter by transaction type.
    """
    transaction_type: StringFilterInput

    """
    Filter by vendor ID.
    """
    vendor_id: StringFilterInput

    """
    Filter by vendor name.
    """
    vendor_name: StringFilterInput

    """
    Filter by warehouse ID.
    """
    warehouse_id: StringFilterInput

    """
    Filter by warehouse name.
    """
    warehouse_name: StringFilterInput
}

"""
Input parameters for querying purchase orders with pagination, sorting, and filtering.
"""
input PurchaseOrdersQueryInput @doc(category: "Purchase Orders") {
    """
    Page number for pagination.
    """
    offset: Int

    """
    Number of records to return per page.
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: PurchaseOrdersFilter
}

"""
Response type for purchase order queries containing results and metadata.
"""
type PurchaseOrdersQueryOutput @doc(category: "Purchase Orders") {
    """
    Total number of purchase orders matching the filter criteria.
    """
    total_count: Int

    """
    Array of purchase order records for the current page.
    """
    items: [PurchaseOrder]
}

extend type Query {
    """
    Query purchase orders with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      purchase_orders(query: {
        offset: 0,
        limit: 20,
        sort: [{ field: "expected_date", order: "desc" }],
        filter: {
          status: { eq: "pending" },
          source: { eq: "shopify" }
        }
      }) {
        total_count
        items {
          id
          note
          source
          po_number
          status
          archived
          expected_date
          transaction_type
          ship_date
          created_at
          updated_at
          total_order_quantity
          total_received_quantity
          vendor_id
          vendor_name
          warehouse_id
          warehouse_name
        }
      }
    }
    ```
    """
    purchase_orders(query: PurchaseOrdersQueryInput!): PurchaseOrdersQueryOutput!
        @doc(category: "Purchase Orders")
}
