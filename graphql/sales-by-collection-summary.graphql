"""
Aggregated summary statistics for sales-by-collection analytics.
Provides totals across all collections matching the query criteria.
"""
type SalesByCollectionSummary @doc(category: "Analytics") {
    """
    Total number of collections included in the summary.
    """
    total_count: Int!

    """
    Total gross sales revenue across all collections.
    """
    gross_sales: Float

    """
    Total gross quantity sold across all collections.
    """
    gross_qty: Int

    """
    Total net sales after returns across all collections.
    """
    net_sales: Float

    """
    Total net quantity after returns across all collections.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. across all collections.
    """
    total_sales: Float

    """
    Total return value across all collections.
    """
    return_value: Float

    """
    Total return quantity across all collections.
    """
    return_qty: Int

    """
    Total advertising spend across all collections.
    """
    ad_spends: Float

    """
    Total ad clicks across all collections.
    """
    clicks: Int

    """
    Total page views across all collections.
    """
    views: Int

    """
    Total stock quantity across all collections.
    """
    stock: Int

    """
    Total on-order quantity across all collections.
    """
    on_order: Int

    """
    Total available-to-sell quantity across all collections.
    """
    ats: Int

    """
    Total inventory value based on stock across all collections.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value based on ATS across all collections.
    """
    total_inventory_value_ats: Float

    """
    Average sell-through rate across all collections.
    """
    sell_through: Float
}

"""
Input parameters for querying sales-by-collection summary statistics.
"""
input SalesByCollectionSummaryQueryInput @doc(category: "Analytics") {
    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Text search across collection names.
    """
    search: String

    """
    Filter criteria to narrow down which collections are included.
    """
    filter: SalesByCollectionFilter
}

extend type Query {
    """
    Get aggregated sales summary statistics for collections.

    Example:
    ```graphql
    query {
      sales_by_collection_summary(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31"
      }) {
        total_count
        gross_sales
        gross_qty
        stock
        ats
      }
    }
    ```
    """
    sales_by_collection_summary(query: SalesByCollectionSummaryQueryInput!): SalesByCollectionSummary!
        @doc(category: "Analytics")
}
