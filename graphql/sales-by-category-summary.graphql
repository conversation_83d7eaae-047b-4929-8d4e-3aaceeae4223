"""
Aggregated summary statistics for sales-by-category analytics.
Provides totals across all categories matching the query criteria.
"""
type SalesByCategorySummary @doc(category: "Analytics") {
    """
    Total number of categories included in the summary.
    """
    total_count: Int!

    """
    Total gross sales revenue across all categories.
    """
    gross_sales: Float

    """
    Total gross quantity sold across all categories.
    """
    gross_qty: Int

    """
    Total net sales after returns across all categories.
    """
    net_sales: Float

    """
    Total net quantity after returns across all categories.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. across all categories.
    """
    total_sales: Float

    """
    Total return value across all categories.
    """
    return_value: Float

    """
    Total return quantity across all categories.
    """
    return_qty: Int

    """
    Total advertising spend across all categories.
    """
    ad_spends: Float

    """
    Total ad clicks across all categories.
    """
    clicks: Int

    """
    Total page views across all categories.
    """
    views: Int

    """
    Total stock quantity across all categories.
    """
    stock: Int

    """
    Total on-order quantity across all categories.
    """
    on_order: Int

    """
    Total available-to-sell quantity across all categories.
    """
    ats: Int

    """
    Total inventory value based on stock across all categories.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value based on ATS across all categories.
    """
    total_inventory_value_ats: Float

    """
    Average sell-through rate across all categories.
    """
    sell_through: Float
}

"""
Input parameters for querying sales-by-category summary statistics.
"""
input SalesByCategorySummaryQueryInput @doc(category: "Analytics") {
    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Text search across category names.
    """
    search: String

    """
    Filter criteria to narrow down which categories are included.
    """
    filter: SalesByCategoryFilter
}

extend type Query {
    """
    Get aggregated sales summary statistics for categories.

    Example:
    ```graphql
    query {
      sales_by_category_summary(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31"
      }) {
        total_count
        gross_sales
        gross_qty
        stock
        ats
      }
    }
    ```
    """
    sales_by_category_summary(query: SalesByCategorySummaryQueryInput!): SalesByCategorySummary!
        @doc(category: "Analytics")
}
