"""
Product performance chart represents a graphical representation of product performance data over time.
It is typically used to visualize product performance trends and patterns over a specific period.
"""
type ProductPerformanceChart @doc(category: "Catalog") {
    """
    Unique hash key identifier for the product.
    """
    h_key: String!

    """
    Product name.
    """
    name: String!

    """
    Actual gross quantity.
    """
    ac_gross_qty: Int

    """
    Actual gross sales.
    """
    ac_gross_sales: Float

    """
    Actual net sales.
    """
    ac_net_sales: Float

    """
    Actual net quantity.
    """
    ac_net_qty: Int

    """
    Actual return quantity.
    """
    ac_return_qty: Int

    """
    Previous gross sales.
    """
    prev_gross_sales: Float

    """
    Previous gross quantity.
    """
    prev_gross_qty: Int

    """
    Previous net sales.
    """
    prev_net_sales: Float

    """
    Previous net quantity.
    """
    prev_net_qty: Int

    """
    Previous return quantity.
    """
    prev_return_qty: Int

    """
    Delta gross sales.
    """
    delta_gross_sales: Float

    """
    Delta gross quantity.
    """
    delta_gross_qty: Int

    """
    Delta net sales.
    """
    delta_net_sales: Float

    """
    Delta net quantity.
    """
    delta_net_qty: Int

    """
    Delta return quantity.
    """
    delta_return_qty: Int

    """
    Delta percentage gross sales.
    """
    delta_percentage_gross_sales: Float

    """
    Delta percentage gross quantity.
    """
    delta_percentage_gross_qty: Float

    """
    Delta percentage net sales.
    """
    delta_percentage_net_sales: Float

    """
    Delta percentage net quantity.
    """
    delta_percentage_net_qty: Float

    """
    Delta percentage return quantity.
    """
    delta_percentage_return_qty: Float
}

enum Type {
    variant
    product
}

"""
Filter input for querying product performance chart with various criteria.
"""
input ProductPerformanceChartFilter @doc(category: "Catalog") {
    dummy: String
}

"""
Input parameters for querying product performance chart with pagination, sorting, and filtering.
"""
input ProductPerformanceChartQueryInput @doc(category: "Catalog") {
    """
    Source for the analysis period.
    """
    from_source: String

    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String!

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String!

    """
    Start date for the comparison period (ISO 8601 format).
    """
    compare_from_date: String!

    """
    End date for the comparison period (ISO 8601 format).
    """
    compare_to_date: String!

    """
    Type of the item.
    """
    type: Type!

    """
    Fields to be returned.
    """
    fields: [String!]!

    """
    Number of records to skip for pagination.
    """
    offset: Int

    """
    Maximum number of records to return.
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]
}

"""
Response type for product performance chart queries containing results and metadata.
"""
type ProductPerformanceChartQueryOutput @doc(category: "Catalog") {
    items: [ProductPerformanceChart]
}

extend type Query {
    product_performance_chart(query: ProductPerformanceChartQueryInput!): ProductPerformanceChartQueryOutput!
        @doc(category: "Catalog")
}
