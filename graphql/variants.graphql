"""
Product variant entity representing specific variations of a product.
Variants are the actual sellable items with unique SKUs, prices, and attributes.
Each variant belongs to a parent product and has specific characteristics like color, size, etc.
"""
type Variant @doc(category: "Catalog") {
    """
    Unique hash key identifier for the variant.
    """
    h_key: String!

    """
    Source system identifier (e.g., 'shopify', 'magento', 'big-commerce',...).
    """
    from_source: String

    """
    Hash key of the parent product this variant belongs to.
    """
    parent_h_key: String

    """
    Business key identifier, typically from the source system.
    """
    b_key: String

    """
    Variant identifier from the source system.
    """
    variant_id: String

    """
    Product identifier from the source system.
    """
    product_id: String

    """
    Variant type classification ('simple').
    """
    type: String

    """
    Variant name displayed to customers.
    """
    name: String

    """
    Stock Keeping Unit - unique identifier for inventory tracking.
    """
    sku: String

    """
    Variant barcode for scanning and identification.
    """
    barcode: String

    """
    Cost per item for margin calculations (in base currency).
    """
    cost_per_item: Float

    """
    Current selling price (in base currency).
    """
    price: Float

    """
    Original price before discounts (in base currency).
    """
    compare_at_price: Float

    """
    URL or path to the variant-specific image.
    """
    image: String

    """
    Variant color attribute.
    """
    color: String

    """
    Default color for the product line.
    """
    default_color: String

    """
    Variant size attribute.
    """
    size: String

    """
    Variant status (e.g., 'active', 'draft', 'archived', 'deleted').
    """
    status: String

    """
    Timestamp when the variant was published/made available.
    """
    published_at: String

    """
    Categories this variant belongs to.
    """
    categories: [VariantCategory]

    """
    Collections this variant is part of.
    """
    collections: [VariantCollection]

    """
    Tags associated with this variant.
    """
    tags: [VariantTag]

    """
    Vendors/suppliers for this variant.
    """
    vendors: [VariantVendor]
}

"""
Filter input for querying variants with various criteria.
Supports filtering by variant attributes, pricing, and associated entities.
"""
input VariantsFilter @doc(category: "Catalog") {
    """
    Filter by variant hash key.
    """
    h_key: StringFilterInput

    """
    Filter by business key.
    """
    b_key: StringFilterInput

    """
    Filter by source system.
    """
    from_source: StringFilterInput

    """
    Filter by variant ID from source system.
    """
    variant_id: StringFilterInput

    """
    Filter by product ID from source system.
    """
    product_id: StringFilterInput

    """
    Filter by variant name (supports text search).
    """
    name: StringFilterInput

    """
    Filter by SKU.
    """
    sku: StringFilterInput

    """
    Filter by barcode.
    """
    barcode: StringFilterInput

    """
    Filter by color attribute.
    """
    color: StringFilterInput

    """
    Filter by size attribute.
    """
    size: StringFilterInput

    """
    Filter by variant status.
    """
    status: StringFilterInput

    """
    Filter by current selling price.
    """
    price: NumericFilterInput

    """
    Filter by compare-at price.
    """
    compare_at_price: NumericFilterInput

    """
    Filter by cost per item.
    """
    cost_per_item: NumericFilterInput

    """
    Filter by associated category hash keys.
    """
    categories_h_key: StringFilterInput

    """
    Filter by associated collection hash keys.
    """
    collections_h_key: StringFilterInput
}

"""
Input parameters for querying variants with pagination, sorting, and filtering.
"""
input VariantsQueryInput @doc(category: "Catalog") {
    """
    Number of records to skip for pagination (0-based).
    """
    offset: Int

    """
    Maximum number of records to return (default: 50, max: 1000).
    """
    limit: Int

    """
    Sort criteria. Multiple sorts are applied in order.
    """
    sort: [SortInput]

    """
    Filter criteria to narrow down results.
    """
    filter: VariantsFilter

    """
    Semantic search input.
    """
    semantic_search: SemanticSearchInput
}

"""
Response type for variant queries containing results and metadata.
"""
type VariantsQueryOutput @doc(category: "Catalog") {
    """
    Total number of variants matching the filter criteria.
    """
    total_count: Int

    """
    Array of variant records for the current page.
    """
    items: [Variant]
}

type Query {
    """
    Query variants with pagination, sorting, and filtering capabilities.

    Example:
    ```graphql
    query {
      variants(query: {
        limit: 20,
        sort: [{ field: "sku", order: "asc" }],
        filter: {
          status: { eq: "active" },
          color: { icontains: "blue" },
          price: { gte: 10.0 }
        },
        semantic_search: {
          text: "electronics",
          min_score: 0.5
        }
      }) {
        total_count
        items {
          h_key
          sku
          name
          color
          size
          price
        }
      }
    }
    ```
    """
    variants(query: VariantsQueryInput!): VariantsQueryOutput! @doc(category: "Catalog")
}
