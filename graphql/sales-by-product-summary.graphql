"""
Aggregated summary statistics for sales-by-product analytics.
Provides totals and averages across all products matching the query criteria.
"""
type SalesByProductSummary @doc(category: "Analytics") {
    """
    Total number of products included in the summary.
    """
    total_count: Int!

    """
    Total gross sales revenue across all products.
    """
    gross_sales: Float

    """
    Total gross quantity sold across all products.
    """
    gross_qty: Int

    """
    Total net sales after returns across all products.
    """
    net_sales: Float

    """
    Total net quantity after returns across all products.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. across all products.
    """
    total_sales: Float

    """
    Total return value across all products.
    """
    return_value: Float

    """
    Total return quantity across all products.
    """
    return_qty: Int

    """
    Average return rate across all products.
    """
    return_rate: Float

    """
    Total discount amount across all products.
    """
    discount: Float

    """
    Total tax amount across all products.
    """
    tax: Float

    """
    Total shipping charges across all products.
    """
    shipping: Float

    """
    Total advertising spend across all products.
    """
    ad_spends: Float

    """
    Total ad clicks across all products.
    """
    clicks: Int

    """
    Total page views across all products.
    """
    views: Int

    """
    Total stock quantity across all products.
    """
    stock: Int

    """
    Total on-order quantity across all products.
    """
    on_order: Int

    """
    Total available-to-sell quantity across all products.
    """
    ats: Int

    """
    Total gross profit across all products.
    """
    gross_profit: Float

    """
    Total inventory value (stock-based) across all products.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value (ATS-based) across all products.
    """
    total_inventory_value_ats: Float
}

"""
Input parameters for querying sales-by-product summary statistics.
"""
input SalesByProductSummaryQueryInput @doc(category: "Analytics") {
    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Text search across product names, SKUs, etc.
    """
    search: String

    """
    Filter criteria to narrow down which products are included.
    """
    filter: SalesByProductFilter

    """
    Channel ID to filter by specific sales channel.
    """
    channel_id: String
}

extend type Query {
    """
    Get aggregated sales summary statistics for products.

    Example:
    ```graphql
    query {
      sales_by_product_summary(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        filter: { status: { eq: "active" } }
      }) {
        total_count
        gross_sales
        gross_qty
        net_sales
        stock
        ats
      }
    }
    ```
    """
    sales_by_product_summary(query: SalesByProductSummaryQueryInput!): SalesByProductSummary!
        @doc(category: "Analytics")
}
