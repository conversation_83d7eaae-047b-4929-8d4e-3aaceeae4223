"""
Aggregated summary statistics for sales-by-variant analytics.
Provides totals and averages across all variants matching the query criteria.
"""
type SalesByVariantSummary @doc(category: "Analytics") {
    """
    Total number of variants included in the summary.
    """
    total_count: Int!

    """
    Total gross sales revenue across all variants.
    """
    gross_sales: Float

    """
    Total gross quantity sold across all variants.
    """
    gross_qty: Int

    """
    Total net sales after returns across all variants.
    """
    net_sales: Float

    """
    Total net quantity after returns across all variants.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. across all variants.
    """
    total_sales: Float

    """
    Total return value across all variants.
    """
    return_value: Float

    """
    Total return quantity across all variants.
    """
    return_qty: Int

    """
    Average return rate across all variants.
    """
    return_rate: Float

    """
    Total discount amount across all variants.
    """
    discount: Float

    """
    Total tax amount across all variants.
    """
    tax: Float

    """
    Total shipping charges across all variants.
    """
    shipping: Float

    """
    Total advertising spend across all variants.
    """
    ad_spends: Float

    """
    Total ad clicks across all variants.
    """
    clicks: Int

    """
    Total page views across all variants.
    """
    views: Int

    """
    Total stock quantity across all variants.
    """
    stock: Int

    """
    Total on-order quantity across all variants.
    """
    on_order: Int

    """
    Total available-to-sell quantity across all variants.
    """
    ats: Int

    """
    Total gross profit across all variants.
    """
    gross_profit: Float

    """
    Total inventory value (stock-based) across all variants.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value (ATS-based) across all variants.
    """
    total_inventory_value_ats: Float
}

"""
Input parameters for querying sales-by-variant summary statistics.
"""
input SalesByVariantSummaryQueryInput @doc(category: "Analytics") {
    """
    Start date for the analysis period (ISO 8601 format).
    """
    from_date: String

    """
    End date for the analysis period (ISO 8601 format).
    """
    to_date: String

    """
    Start date for forecast analysis (ISO 8601 format).
    """
    forecast_from_date: String

    """
    End date for forecast analysis (ISO 8601 format).
    """
    forecast_to_date: String

    """
    Text search across variant names, SKUs, and other searchable fields.
    """
    search: String

    """
    Filter criteria to narrow down which variants are included in the summary.
    """
    filter: SalesByVariantFilter

    """
    Channel ID to filter results by specific sales channel.
    """
    channel_id: String
}

extend type Query {
    """
    Get aggregated sales summary statistics across all variants.

    @deprecated Use sales_by_variant_summary instead.
    """
    sales_summary(query: SalesByVariantSummaryQueryInput!): SalesByVariantSummary! @doc(category: "Analytics")

    """
    Get aggregated sales summary statistics for variants matching the query criteria.

    Example:
    ```graphql
    query {
      sales_by_variant_summary(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        filter: { status: { eq: "active" } }
      }) {
        total_count
        gross_sales
        gross_qty
        net_sales
        stock
        ats
      }
    }
    ```
    """
    sales_by_variant_summary(query: SalesByVariantSummaryQueryInput!): SalesByVariantSummary!
        @doc(category: "Analytics")
}
