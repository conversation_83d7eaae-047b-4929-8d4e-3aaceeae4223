"""
Sales analytics data aggregated by product collection.
Provides collection-level insights for sales performance and inventory management.
"""
type SalesByCollectionItem @doc(category: "Analytics") {
    """
    Unique hash key identifier for the collection.
    """
    h_key: String!

    """
    Collection name.
    """
    name: String

    """
    Total gross sales revenue for this collection.
    """
    gross_sales: Float

    """
    Total gross quantity sold for this collection.
    """
    gross_qty: Int

    """
    Total net sales after returns for this collection.
    """
    net_sales: Float

    """
    Total net quantity after returns for this collection.
    """
    net_qty: Int

    """
    Total sales including taxes, shipping, etc. for this collection.
    """
    total_sales: Float

    """
    Total return value for this collection.
    """
    return_value: Float

    """
    Total return quantity for this collection.
    """
    return_qty: Int

    """
    Total advertising spend for this collection.
    """
    ad_spends: Float

    """
    Total ad clicks for this collection.
    """
    clicks: Int

    """
    Total page views for this collection.
    """
    views: Int

    """
    Total stock quantity for this collection.
    """
    stock: Int

    """
    Total on-order quantity for this collection.
    """
    on_order: Int

    """
    Total available-to-sell quantity for this collection.
    """
    ats: Int

    """
    Total inventory value based on stock for this collection.
    """
    total_inventory_value_stock: Float

    """
    Total inventory value based on ATS for this collection.
    """
    total_inventory_value_ats: Float

    """
    Sell-through rate for this collection.
    """
    sell_through: Float

    """
    Percentage of total stock this collection represents.
    """
    stock_percentage: Float

    """
    Percentage of total gross sales this collection represents.
    """
    gross_sales_percentage: Float
}

"""
Filter input for sales-by-collection queries.
Allows filtering collection analytics data by various criteria including sales metrics,
inventory levels, and performance indicators.
"""
input SalesByCollectionFilter @doc(category: "Analytics") {
    """
    Filter by collection hash key identifier.
    """
    h_key: StringFilterInput

    """
    Filter by collection name.
    """
    name: StringFilterInput

    """
    Filter by total gross sales revenue range.
    """
    gross_sales: NumericFilterInput

    """
    Filter by total gross quantity sold range.
    """
    gross_qty: NumericFilterInput

    """
    Filter by total net sales after returns range.
    """
    net_sales: NumericFilterInput

    """
    Filter by total net quantity after returns range.
    """
    net_qty: NumericFilterInput

    """
    Filter by total sales including taxes and shipping range.
    """
    total_sales: NumericFilterInput

    """
    Filter by total return value range.
    """
    return_value: NumericFilterInput

    """
    Filter by total return quantity range.
    """
    return_qty: NumericFilterInput

    """
    Filter by total ad clicks range.
    """
    clicks: NumericFilterInput

    """
    Filter by total page views range.
    """
    views: NumericFilterInput

    """
    Filter by total stock quantity range.
    """
    stock: NumericFilterInput

    """
    Filter by total on-order quantity range.
    """
    on_order: NumericFilterInput

    """
    Filter by total available-to-sell quantity range.
    """
    ats: NumericFilterInput

    """
    Filter by total inventory value based on stock range.
    """
    total_inventory_value_stock: NumericFilterInput

    """
    Filter by total inventory value based on ATS range.
    """
    total_inventory_value_ats: NumericFilterInput

    """
    Filter by stock percentage of total inventory range.
    """
    stock_percentage: NumericFilterInput

    """
    Filter by gross sales percentage of total sales range.
    """
    gross_sales_percentage: NumericFilterInput
}

"""
Input parameters for querying sales-by-collection analytics.
"""
input SalesByCollectionQueryInput @doc(category: "Analytics") {
    """
    Number of records to skip for pagination.
    """
    offset: Int

    """
    Maximum number of records to return.
    """
    limit: Int

    """
    Start date for analysis period.
    """
    from_date: String

    """
    End date for analysis period.
    """
    to_date: String

    """
    Start date for forecast analysis.
    """
    forecast_from_date: String

    """
    End date for forecast analysis.
    """
    forecast_to_date: String

    """
    Sort criteria.
    """
    sort: [SortInput]

    """
    Text search across collection names.
    """
    search: String

    """
    Filter criteria.
    """
    filter: SalesByCollectionFilter
}

extend type Query {
    """
    Query sales analytics data aggregated by product collection.

    Example:
    ```graphql
    query {
      sales_by_collection(query: {
        from_date: "2024-01-01",
        to_date: "2024-12-31",
        sort: [{ field: "gross_sales", order: "desc" }]
      }) {
        h_key
        name
        gross_sales
        gross_qty
        stock
        sell_through
      }
    }
    ```
    """
    sales_by_collection(query: SalesByCollectionQueryInput!): [SalesByCollectionItem]
        @doc(category: "Analytics")
}
