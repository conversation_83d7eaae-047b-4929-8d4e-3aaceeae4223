version: '3'

networks:
    todos_test:

services:
    todos_test_service:
        image: node:18-alpine
        working_dir: /usr/app
        user: '1000:1000'
        volumes:
            - ./:/usr/app:rw
        environment:
            NPM_TOKEN: ${NPM_TOKEN}
            DATABASE_HOST: todos_test_postgres
            DATABASE_PORT: 5432
            DATABASE_SCHEMA: dev
            DATABASE_USER: dev
            DATABASE_PASSWORD: dev@123
            POSTGRES_HOST: todos_test_postgres
            POSTGRES_PORT: 5432
            POSTGRES_DATABASE: dev
            POSTGRES_USER: dev
            POSTGRES_PASSWORD: dev@123
            OPENSEARCH_MODULE_TABLE_NAME_PREFIX: tbl_
        command: sh -c "yarn && sleep 3 && yarn migrate:latest && yarn test"
        depends_on:
            - todos_test_postgres
        networks:
            - todos_test

    todos_test_postgres:
        image: postgres
        environment:
            POSTGRES_PASSWORD: dev@123
            POSTGRES_USER: dev
        networks:
            - todos_test
