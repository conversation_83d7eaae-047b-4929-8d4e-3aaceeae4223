{"compilerOptions": {"emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "declaration": true, "importHelpers": true, "module": "commonjs", "outDir": "dist", "removeComments": true, "skipLibCheck": true, "sourceMap": false, "strict": true, "target": "es6", "moduleResolution": "node", "lib": ["es6"], "types": ["node"], "typeRoots": ["node_modules/@types"], "paths": {"@core": ["./src/core"], "@utils": ["./src/utils"], "@configs": ["./src/configs"], "@constants": ["./src/constants"], "@features/*": ["./src/features/*"], "@interceptors": ["./src/interceptors"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "spec/**/*.ts", "src/features/opensearch/infra/ops/mappers/variant.record-mapperts"], "exclude": ["spec", "src/**/*.mock.ts", "www/public/", "dist/"]}