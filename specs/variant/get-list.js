const client = require('../jest/client');
const { StatusCodes } = require('http-status-codes');
const axios = client.axios;

// Config
const { SERVER_ADDRESS } = process.env;
const rootUrl = `${SERVER_ADDRESS}/variants`;

module.exports = () => {
    test('get list of variants', async () => {
        const res = await axios.get(`${rootUrl}`);
        expect(res.status).toEqual(StatusCodes.OK);
    });
};
