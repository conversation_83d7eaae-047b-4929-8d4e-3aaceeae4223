networks:
    opensearch-network:

services:
    opensearch_service:
        image: node:22-alpine
        working_dir: /usr/app
        volumes:
            - ./:/usr/app
        ports:
            - 30000:3000
            - 42290:9229
        env_file: .env
        environment:
            NPM_TOKEN: ${NPM_TOKEN}
        command: sh -c "npm run nodemon"
        networks:
            - opensearch-network
    redis:
        image: redis:latest
        command: redis-server --requirepass dev@123
        networks:
            - opensearch-network
