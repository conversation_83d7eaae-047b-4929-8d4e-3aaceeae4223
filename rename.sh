#!/bin/bash

# Run this file for rename all sub directories and file name of source code from A to B
# Example : ./rename.sh /home/<USER>/Projects/apply-ddd-for-heronjs/src/ todo cart

# $1 = /path/to/src . Example : /home/<USER>/Projects/apply-ddd-for-heronjs/src/
# $2 = current_name . Example : todo
# $2 = new_name . Example : cart

if [ $(dpkg-query -W -f='${Status}' rename | grep -c "installed") -eq 0 ];
then
  sudo apt-get install rename;
fi

cd "${1}"
for i in {1..5} # loop level
do
   find . -type d|while read directory; do
     if [ -d "$directory" ]
     then
       cd "$directory"
       rename "s/$2/$3/" *
       cd "${1}"
     fi
   done
done

find ./ -type f -exec sed -i "s/$2/$3/g" {} \; -exec sed -i "s/${2^^}/${3^^}/g" {} \; -exec sed -i "s/${2^}/${3^}/g" {} \;
