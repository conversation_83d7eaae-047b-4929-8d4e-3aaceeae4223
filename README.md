# OpenSearch Module

## Variant API Fields

### Metadata Fields

| Name                        | Key                         | Description                      | Type    | Filterable | Sortable |
| --------------------------- | --------------------------- | -------------------------------- | ------- | ---------- | -------- |
| Business Key                | b_key                       | Business identifier              | string  | ❌         | ✅       |
| Variant ID                  | variant_id                  | Unique variant identifier        | string  | ✅         | ✅       |
| SKU                         | sku                         | Stock keeping unit               | string  | ✅         | ✅       |
| Name                        | name                        | Product variant name             | string  | ✅         | ✅       |
| Type                        | type                        | Product type                     | string  | ✅         | ❌       |
| Barcode                     | barcode                     | Product barcode                  | string  | ✅         | ✅       |
| Color                       | color                       | Product color                    | string  | ✅         | ✅       |
| Size                        | size                        | Product size                     | string  | ✅         | ✅       |
| Price                       | price                       | Product price                    | number  | ✅         | ❌       |
| Compare At Price            | compare_at_price            | Original price for comparison    | string  | ✅         | ❌       |
| Cost                        | cost                        | Product cost                     | number  | ✅         | ❌       |
| Stock                       | stock                       | Current stock level              | number  | ✅         | ❌       |
| Status                      | status                      | Product status                   | string  | ✅         | ❌       |
| Replenishable               | replenishable               | Whether product can be restocked | boolean | ✅         | ❌       |
| Available To Sell           | ats                         | Available quantity to sell       | number  | ✅         | ❌       |
| Total Inventory Value Stock | total_inventory_value_stock | Total inventory value            | string  | ✅         | ❌       |
| Trend                       | trend                       | Product trend indicator          | string  | ❌         | ✅       |
| Sale Pattern                | sale_pattern                | Sales pattern category           | string  | ❌         | ✅       |

### Sales Metrics

| Name            | Key           | Description                 | Type   | Filterable | Sortable |
| --------------- | ------------- | --------------------------- | ------ | ---------- | -------- |
| Gross Sales     | gross_sales   | Total gross sales value     | number | ✅         | ✅       |
| Gross Quantity  | gross_qty     | Total quantity sold (gross) | number | ✅         | ✅       |
| Net Sales       | net_sales     | Total net sales value       | number | ✅         | ✅       |
| Net Quantity    | net_qty       | Total quantity sold (net)   | number | ✅         | ✅       |
| Total Sales     | total_sales   | Overall total sales         | number | ✅         | ✅       |
| Return Value    | return_value  | Value of returns            | number | ✅         | ✅       |
| Return Quantity | return_qty    | Quantity of returns         | number | ✅         | ✅       |
| Discount        | discount      | Discount amount             | number | ✅         | ✅       |
| Tax             | tax           | Tax amount                  | number | ✅         | ✅       |
| Shipping        | shipping      | Shipping cost               | number | ✅         | ✅       |
| Sales Per Day   | sales_per_day | Average daily sales         | number | ✅         | ✅       |

### Forecast Metrics

| Name                   | Key                    | Description           | Type   | Filterable | Sortable |
| ---------------------- | ---------------------- | --------------------- | ------ | ---------- | -------- |
| Forecast Value         | forecast_value         | Predicted sales value | number | ✅         | ✅       |
| Forecast Sales Per Day | forecast_sales_per_day | Predicted daily sales | number | ❌         | ✅       |

### Ads Metrics

| Name      | Key       | Description         | Type   | Filterable | Sortable |
| --------- | --------- | ------------------- | ------ | ---------- | -------- |
| Ad Spends | ad_spends | Advertising spend   | number | ❌         | ✅       |
| Clicks    | clicks    | Number of ad clicks | number | ✅         | ✅       |
| Views     | views     | Number of ad views  | number | ✅         | ✅       |

## Filtering

The API supports filtering on various fields with different operators based on the field type.

### String Fields

Applies to: name, sku, barcode, type, color, size, status, etc.

Supported operators:

-   `eq`: Exact match (case-sensitive)
-   `neq`: Not equal
-   `in`: Match any value in array
-   `nin`: Not in array
-   `contains`: Contains substring (case-sensitive)
-   `startswith`: Starts with prefix (case-sensitive)
-   `endswith`: Ends with suffix (case-sensitive)
-   `icontains`: Contains substring (case-insensitive)
-   `istartswith`: Starts with prefix (case-insensitive)
-   `iendswith`: Ends with suffix (case-insensitive)
-   `match`: Full text search
-   `exists`: Field exists and is not null

Examples:

```json
{
    "name": {
        "contains": "Shirt"
    },
    "sku": {
        "in": ["SKU001", "SKU002"]
    },
    "barcode": {
        "istartswith": "abc"
    }
}
```

### Number Fields

Applies to: price, cost, stock, ats, gross_sales, net_sales, etc.

Supported operators:

-   `eq`: Equal to
-   `neq`: Not equal to
-   `gt`: Greater than
-   `gte`: Greater than or equal
-   `lt`: Less than
-   `lte`: Less than or equal
-   `in`: Match any value in array
-   `nin`: Not in array
-   `exists`: Field exists and is not null

Examples:

```json
{
    "price": {
        "gte": 10,
        "lte": 100
    },
    "stock": {
        "gt": 0
    },
    "gross_sales": {
        "in": [100, 200, 300]
    }
}
```

### Boolean Fields

Applies to: replenishable, etc.

Supported operators:

-   `eq`: Equal to
-   `neq`: Not equal to
-   `exists`: Field exists and is not null

Example:

```json
{
    "replenishable": {
        "eq": true
    }
}
```

### Array Fields

Applies to: categories, tags, etc.

Supported operators:

-   `in`: Any array element matches any value in the input array
-   `nin`: No array element matches any value in the input array
-   `eq`: Array exactly matches the input value
-   `neq`: Array does not exactly match the input value
-   `exists`: Field exists and is not null

Example:

```json
{
    "categories": {
        "in": ["Electronics", "Accessories"]
    }
}
```

### Multiple Filters

You can combine multiple filters. All filters are combined with AND logic.

Example:

```json
{
    "type": {
        "eq": "shirt"
    },
    "price": {
        "gte": 20,
        "lte": 50
    },
    "stock": {
        "gt": 0
    }
}
```

## Sorting

The API supports sorting on fields marked as sortable in the field tables above.

### Sort Direction

-   `asc`: Ascending order
-   `desc`: Descending order

### Sort Format

```json
{
    "sort": [
        {
            "field": "field_name",
            "order": "asc|desc"
        }
    ]
}
```

### Examples

Sort by name ascending:

```json
{
    "sort": [
        {
            "field": "name",
            "order": "asc"
        }
    ]
}
```

Multiple sort criteria (sort by gross sales descending, then by name ascending):

```json
{
    "sort": [
        {
            "field": "gross_sales",
            "order": "desc"
        },
        {
            "field": "name",
            "order": "asc"
        }
    ]
}
```

### Default Sort
