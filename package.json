{"name": "@cbidigital/opensearch-module", "version": "0.0.0", "description": "Opensearch Module", "engines": {"node": ">=18"}, "scripts": {"dev": "npm run docker:up", "docker:up": "docker compose up --no-log-prefix", "docker:down": "docker compose down", "docker:exec": "docker compose exec component_service $*", "nodemon": "nodemon --config nodemon.debug.json", "build": "node build.js && tsc --build tsconfig.json && tsc-alias", "debug": "npm run bdev && nodemon --config nodemon.debug.json", "bdev": "node build.js && tsc --build tsconfig.debug.json", "start": "node dist/index.js", "test": "npm run build && jest --updateSnapshot --detectOpenHandles", "test:ci": "docker-compose -f docker-compose.test.yml down && docker-compose -f docker-compose.test.yml up --abort-on-container-exit --no-log-prefix", "migrate:make": "knex migrate:make", "migrate:up": "knex migrate:up", "migrate:latest": "knex migrate:latest", "migrate:down": "knex migrate:down", "migrate:rollback": "knex migrate:rollback", "migrate:list": "knex migrate:list", "seed:make": "knex seed:make", "seed:run": "knex seed:run", "format": "prettier --write .", "lint": "eslint --ext .js,.ts,.json .", "lint:fix": "npm run format && eslint --fix --ext .js,.ts .", "package": "npm run lint && npm run build && node module.js", "setup:git-hook": "cp scripts/git-hook/* .git/hooks && chmod -R +x .git/hooks && echo 'hook copied'"}, "keywords": [], "nodemonConfig": {"legacyWatch": true, "watch": ["src"], "ext": "ts,html", "ignore": ["src/public"], "exec": "ts-node src"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.11", "@types/node": "^20.12.13", "@types/reflect-metadata": "^0.1.0", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.3.1", "eslint": "^8.2.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^11.2.0", "jest": "^29.7.0", "nodemon": "^3.1.2", "prettier": "^2.4.1", "ts-node": "^10.9.2", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.5"}, "dependencies": {"@cbidigital/aqua-ddd": "1.0.0-rc.23", "@heronjs/common": "^3.4.16", "@heronjs/core": "^3.5.16", "@heronjs/express": "^3.1.19", "@heronjs/gql": "^3.1.28", "@opensearch-project/opensearch": "^2.12.0", "axios": "^1.7.2", "cache-manager": "^3.4.4", "cache-manager-ioredis": "^2.1.0", "class-validator": "^0.14.1", "cors": "^2.8.5", "elastic-builder": "^2.29.0", "express": "^4.19.2", "fs": "^0.0.1-security", "http-status-codes": "^2.3.0", "ioredis": "^4.27.8", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "moment": "^2.30.1", "openai": "^5.3.0", "pg": "^8.11.5", "pino": "^9.1.0", "pino-pretty": "^11.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "zod": "^3.23.8"}}